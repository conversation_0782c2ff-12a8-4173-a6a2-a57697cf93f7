name: Stage Push
on: workflow_dispatch
env:
  SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
  STAGE: "stage"
  DISTRIBUTION: ${{ secrets.STAGE_CDN_DISTRIBUTION_ID }}
  DISTRIBUTION_ID: ${{ secrets.STAGE_CDN_DISTRIBUTION_ID }}
  AWS_REGION: ${{ secrets.STAGE_AWS_REGION }}
  AWS_BUCKET: ${{ secrets.STAGE_AWS_BUCKET }}
  AWS_ACCESS_KEY_ID: ${{ secrets.STAGE_AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.STAGE_AWS_SECRET_ACCESS_KEY }}
  PATHS: "/*"
jobs:
  build:
    name: Push Job - SwellCast Site
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Source
        uses: actions/checkout@v1

      - name: Setup Nodejs 20.x
        uses: actions/setup-node@v1
        with:
          node-version: "20.x"

      - name: Install Dependencies
        run: yarn

      - name: Export Env
        run: yarn exportenv --stage stage

      - name: GQL Codegen
        run: yarn codegen
        continue-on-error: true

      - name: Pack Client
        run: yarn pack:client --mode production

      - name: Pack Serverless
        run: yarn pack:sls -s stage

      - name: Deploy Assets
        run: yarn deploy:assets

      - name: Deploy Serverless
        run: yarn deploy:sls --stage stage

      - name: Invalidate CDN stage.swellcast.com
        uses: chetan/invalidate-cloudfront-action@v2
