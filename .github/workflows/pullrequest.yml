name: Pull Request Pipeline
on:
  pull_request:
    branches:
      - master
jobs:
  build:
    name: Pull Request Job
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Source
        uses: actions/checkout@v1
      - name: Setup Nodejs 20.x
        uses: actions/setup-node@v1
        with:
          node-version: '20.x'
      - name: Install Dependencies
        run: yarn
      - name: Build
        run: yarn build
        env:
          AWS_BUCKET: ${{ secrets.STAGE_AWS_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.STAGE_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.STAGE_AWS_SECRET_ACCESS_KEY }}
      # - name: Unit Test
      # run: yarn test
      # - name: Integration Test & Coverage
      # run: yarn test:coverage
