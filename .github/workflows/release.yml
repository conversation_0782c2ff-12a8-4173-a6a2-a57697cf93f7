name: Release Pipeline
on:
  push:
    tags:
      - "v*"
env:
  SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
  STAGE: "prod"
  DISTRIBUTION: ${{ secrets.STAGE_CDN_DISTRIBUTION_ID }}
  DISTRIBUTION_ID: ${{ secrets.PDN_CDN_DISTRIBUTION_ID }}
  AWS_REGION: ${{ secrets.PDN_AWS_REGION }}
  AWS_BUCKET: ${{ secrets.PDN_AWS_BUCKET }}
  AWS_ACCESS_KEY_ID: ${{ secrets.PDN_AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.PDN_AWS_SECRET_ACCESS_KEY }}
  PATHS: "/*"
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  SLACK_USERNAME: CICD
jobs:
  build:
    name: Release Job - SwellCast Site
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Source
        uses: actions/checkout@v1

      - name: Setup Nodejs 20.x
        uses: actions/setup-node@v1
        with:
          node-version: "20.x"

      - name: Install Dependencies
        run: yarn

      - name: Export Env
        run: yarn exportenv --stage prod

      - name: GQL Codegen
        run: yarn codegen
        continue-on-error: true

      - name: Pack Client
        run: yarn pack:client --mode production

      - name: Pack Serverless
        run: yarn pack:sls -s prod

      - name: Deploy Assets
        run: yarn deploy:assets

      - name: Deploy Serverless
        run: yarn deploy:sls --stage prod

      - name: Invalidate cdn swellcast.com # https://github.com/marketplace/actions/aws-cloudfront-invalidate-action-and-wait-for-completion
        uses: muratiger/invalidate-cloudfront-and-wait-for-completion-action@master

      - name: Slack Notification
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_MESSAGE: "new Release >> Status - ${{ job.status }}"
