name: Revet Release Pipeline
on:
  workflow_dispatch:
    inputs:
      tagName:
        description: 'tagName (Eg: v1.0.35)'
        required: true
        default: ''
jobs:
  build:
    name: Release Job - SwellCast Site
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Source
        uses: actions/checkout@v1
        with:
          ref: ${{ github.event.inputs.tagName }}
      - name: Setup Nodejs 20.x
        uses: actions/setup-node@v1
        with:
          node-version: '20.x'
          registry-url: 'https://npm.pkg.github.com'
      - name: Install Dependencies
        run: yarn
      - name: Build
        run: yarn build
        env:
          AWS_BUCKET: ${{ secrets.PDN_AWS_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.PDN_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.PDN_AWS_SECRET_ACCESS_KEY }}
      - name: Deploy
        run: yarn deploy --stage prod
        env:
          AWS_BUCKET: ${{ secrets.PDN_AWS_BUCKET }}
          AWS_ACCESS_KEY_ID: ${{ secrets.PDN_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.PDN_AWS_SECRET_ACCESS_KEY }}
      - name: Invalidate cache # https://github.com/marketplace/actions/aws-cloudfront-invalidate-action-and-wait-for-completion
        uses: muratiger/invalidate-cloudfront-and-wait-for-completion-action@master
        env:
          DISTRIBUTION_ID: ${{ secrets.PDN_DISTRIBUTION_ID }}
          PATHS: '/swellcast/*'
          AWS_REGION: 'us-east-2'
          AWS_ACCESS_KEY_ID: ${{ secrets.PDN_AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.PDN_AWS_SECRET_ACCESS_KEY }}
      - name: Slack Notification
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: CICD
          SLACK_MESSAGE: 'new Release >> Status - ${{ job.status }}'
