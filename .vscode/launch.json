{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "launch cryptonator aggregator",
      "program": "${workspaceRoot}/node_modules/serverless/bin/serverless",
      "cwd": "${workspaceRoot}",
      "protocol": "inspector",
      "args": ["offline", "start"],
      "console": "integratedTerminal"
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Debug in chrome",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}/src"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Test Case",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["--runInBand", "--env=jsdom"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "disableOptimisticBPs": true,
      "windows": {
        "program": "${workspaceFolder}/node_modules/jest/bin/jest"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Lanuch Program",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}\\scripts\\start.js"
    }
  ]
}
