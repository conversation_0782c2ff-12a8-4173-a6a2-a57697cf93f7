{"typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "editor.fontFamily": "'FiraCode-<PERSON><PERSON>', 'Fira Code Retina', Consolas, 'Courier New', monospace", "editor.fontLigatures": true, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.formatOnSave": false, "diffEditor.renderSideBySide": true, "telemetry.enableCrashReporter": false, "npm.packageManager": "yarn", "fileHeaderComment.parameter": {"*": {"company": "Anecure Inc"}}, "fileHeaderComment.template": {"*": ["${commentbegin}", "${commentprefix} Created on ${date}", "${commentprefix}", "${commentprefix} Copyright (c) ${year}, ${company}. All rights reserved.", "${commentend}"]}, "docthis.includeAuthorTag": true, "docthis.includeDateTag": true, "docthis.includeDescriptionTag": true, "docthis.enableHungarianNotationEvaluation": true, "docthis.inferTypesFromNames": true, "editor.minimap.maxColumn": 1000, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "prettier.printWidth": 600, "[typescriptreact]": {"editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "[typescript]": {"editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "javascript.format.semicolons": "ignore", "typescript.format.semicolons": "ignore", "[typescript][typescriptreact]": {"editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}}