### Swellcast Site

---

To start this project
$ nvm use 14
$ yarn install
$ yarn start
$ yarn start:api

Release Prod:
    - Switch to master branch
    - $ git pull
    - $ yarn release:prod

- Fast Local Dev
    - Use $yarn start
    - Create a .env.dev file that defines:
```ASSET_URL=http://*************:3000
CANONICAL_DOMAIN=*************:3000
CANONICAL_ALT_DOMAIN=*************:3000
SWELL_API=/graphql?stage=stage```
CODEGEN_API=https://stagewidgetapi.swell.life/graphql```
---

#Schema.org

- About
    - https://developers.google.com/search/docs/advanced/structured-data/search-gallery

- Validators
    - https://validator.schema.org/
    - https://search.google.com/test/rich-results
    - note: Use "Code Snippet" option for stage domain html
- Swell
    - type: https://schema.org/SocialMediaPosting
    - type: https://schema.org/Blog
- Swellcast
    - none

---

#Issues:

- Sometimes the styles don't render. To fix run: \$npm rebuild node-sass.
- Important: If you also have the vscode-tslint extension in VS Code installed, please disable it to avoid linting files twice.
- Post css is used so in some cases this pck need the name of a style class manually entered. So if you see a comment with postcss and some class name listed, that's functional code.
    - Update: added prestart script to handle this: "npm rebuild node-sass"
- Everytime time you try to quit the ```$ yarn start``` process, it fails to actually kill the process. Here's the absolutely ridiculous and tedious fix:
    - List the running process on the specific port
        - ```$ lsof -i tcp:3003```
    - In the list that appears, you'll see something like this:
        - ```$ node      35624 jasoncontento   51u  IPv4 0x6ebdb029744598c9      0t0  TCP localhost:cgms->localhost:53472 (ESTABLISHED)```
    - That "35624" is the process id. Kill that specific process like this:
        - ```kill -9 35624```
    - Of course you could just $ killall node, but you'll have to restart all your node processes
- importing individual scss files must happen in main.scss - otherwise styles will be duplicated
---

Periodically use "Sort package.json" from https://marketplace.visualstudio.com/items?itemName=unional.vscode-sort-package-json

#Note:

- ```$ export SLS_DEBUG=true```
- ```$ export SLS_DEBUG=false```

#Updating Packages

- npm-check-updates: https://www.npmjs.com/package/npm-check-updates
  - check which libs need update: $ ncu
  - update libs: ```$ ncu -u```
  - update only minor updates: ```$ ncu -u -t minor```
- eslint migration: https://github.com/typescript-eslint/tslint-to-eslint-config

- Need to make sure https://anecure.atlassian.net/browse/SA-3426 is actively filter station IDs
---
- Deploy Notes
    - 2022-04-20: Need "snippets" field in API

#Test OG
```
$ INPUT=test_urls.csv
while read httpRequest_requestUrl
do
  random_url="${httpRequest_requestUrl}"
  echo “\nRequesting ${random_url}”
  curl -s ${random_url} | grep -ioE '^<meta.*property="og:title".*content="([^"]+)".*\/>$'
done < $INPUT
```