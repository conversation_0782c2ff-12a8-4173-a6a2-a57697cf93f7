import { CodegenConfig } from '@graphql-codegen/cli';
import dotEnv from 'dotenv';

dotEnv.config({ path: '.env.sls' });

const SWELL_API = process.env.CODEGEN_API || process.env.SWELL_API!;

const config: CodegenConfig = {
  overwrite: true,
  schema: [
    {
      [SWELL_API]: {
        headers: {
          swellstagekey: 'OIE837&jah#jkwe873Nlsh*@nshKH_ms',
          swellpdnkey: 'H&ABDG%MAYsdfy7S$HJAT_NAund23ns8',
          'User-Agent': 'github action',
        },
      },
    },
  ],
  generates: {
    'src/generated/graphql.tsx': {
      plugins: ['typescript'],
      config: {
        skipTypename: true,
      },
    },
  },
};

export default config;
