import path from 'path';
import { Configuration } from 'webpack';
const envPath = path.join(__dirname, '../.env.sls');
require('dotenv').config({ path: envPath });

const config = (): Configuration[] => {
  const isLocal = process.env.STAGE === 'local';
  const isStage = process.env.STAGE === 'stage';

  return [
    {
      extends: [
        path.resolve(__dirname, './webpack.base.config.ts'), //
        path.resolve(__dirname, './webpack.proxy.config.ts'),
      ],
      entry: {
        swellcast: './src/view/website/index.tsx',
        finder_featured: './src/view/go/finder/featured/index.tsx',
        finder_search: './src/view/go/finder/hash/index.tsx',
        finder_latest: './src/view/go/finder/latest/index.tsx',
        widget_swellcast: './src/view/swellcast-widget/index.tsx',
        widget_swell: './src/view/swell-widget/index.tsx',
        widget_search: './src/view/search-widget/index.tsx',
        ...(isStage || isLocal
          ? {
              test: './src/view/test/index.tsx',
              swell_internal: './src/view/private/index.tsx',
            //   media: './src/view/media/index.tsx',
            }
          : {}),
        ...(isLocal
          ? {
              me: './src/view/me/index.tsx',
              swellweb: './src/view/me/swellweb.js',
            }
          : {}),
      },
      output: {
        clean: true,
      },
    },
  ];
};

export default config;
