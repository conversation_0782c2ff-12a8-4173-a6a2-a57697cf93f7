import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin';
import CopyPlugin from 'copy-webpack-plugin';
import Dotenv from 'dotenv-webpack';
import fs from 'fs';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import path from 'path';
import { Configuration } from 'webpack';

const envStage = path.join(__dirname, '../.env');
require('dotenv').config({ path: envStage });
// dotenv.config({ path: envStage, override: true });

interface WebpackDevConfig extends Configuration {
  devServer?: any;
}

const reactQueryState = {
  mutations: [],
  queries: [
    {
      state: {
        data: {
          ASSET_PATH: '/', //
          ASSET_URL: '/',
          CANONICAL_ALT_DOMAIN: '/',
          CANONICAL_DOMAIN: '/',
          GTM_ID: 'GTM-T6Z7W76',
          SITE_DOMAIN: '/',
          STAGE: 'local',
          SWELL_API: '/graphql',
          SWELL_DEVELOPERS_URL: 'https://stagedevelopers.swellcast.com/',
          TRACKING_API: 'https://webevent-stage.swell.life/streams/event-pipe/record',
          TIME_BEFORE_LOGIN_POPUP: '10000',
          TIME_BETWEEN_LOGIN_POPUP: '120000',
          TIME_BEFORE_FOLLOW_POPUP: '10000',
        },
        dataUpdateCount: 2,
        dataUpdatedAt: 1749820046624,
        error: null,
        errorUpdateCount: 0,
        errorUpdatedAt: 0,
        fetchFailureCount: 0,
        fetchFailureReason: null,
        fetchMeta: null,
        isInvalidated: false,
        status: 'success',
        fetchStatus: 'idle',
      },
      queryKey: ['env'],
      queryHash: '["env"]',
    },
    {
      state: {
        data: {
          isLocal: true, //
          isStage: false,
          isProd: false,
          stage: 'stage',
          countryCode: 'US',
          serverCountryCode: 'US',
          apiUrl: '/graphql',
          resolvedColorMode: 'light',
          colorMode: 'light',
          autoPlay: true,
          debug: '',
          volume: 1,
          playbackRate: 1,
          isWidget: false,
          version: 1,
          allowPlayInCard: false,
          utmParams: {},
        },
        dataUpdateCount: 2,
        dataUpdatedAt: 1749820047007,
        error: null,
        errorUpdateCount: 0,
        errorUpdatedAt: 0,
        fetchFailureCount: 0,
        fetchFailureReason: null,
        fetchMeta: null,
        isInvalidated: false,
        status: 'success',
        fetchStatus: 'idle',
      },
      queryKey: ['server_settings'],
      queryHash: '["server_settings"]',
    },
  ],
};

const templateContent = `<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>SWELLCAST</title>
        <script>window.__REACT_QUERY_STATE__ = ${JSON.stringify(reactQueryState)};</script>
    </head>
    <body>
        <div id="root"></div>
    </body>
    <script src="/me/js/swellweb.js"></script>
</html>`;

const config = async (): Promise<WebpackDevConfig> => {
  return {
    // Remove extends to avoid CSS loader conflicts with production config
    entry: {
      // Main website entry point - handles most routes including catchall
      swellcast: './src/view/website/index.tsx',
      me: './src/view/me/index.tsx', // /me/
      swell_internal: './src/view/private/index.tsx',
    },
    devServer: {
      devMiddleware: {
        writeToDisk: false, // Write generated files to disk
      },
      server: {
        type: 'https', // ← turn on HTTPS
        options: {
          // ← (optional) custom TLS settings
          key: fs.readFileSync(path.resolve(__dirname, '../https/key.pem')),
          cert: fs.readFileSync(path.resolve(__dirname, '../https/cert.pem')),
          //   ca: fs.readFileSync(path.resolve(__dirname, '../https/ca.pem')),
          // passphrase, requestCert, minVersion, etc. all go here
        },
      },
      historyApiFallback: {
        // Ensure all routes are properly handled by the appropriate entry point
        rewrites: [
          { from: /^\/go\/finder\/featured/, to: '/finder_featured.html' },
          { from: /^\/go\/finder\/search/, to: '/finder_search.html' },
          { from: /^\/go\/finder\/latest/, to: '/finder_latest.html' },
          { from: /^\/widget-swell/, to: '/widget_swell.html' },
          { from: /^\/widget\/search/, to: '/widget_search.html' },
          { from: /^\/widget/, to: '/widget_swellcast.html' },
          { from: /^\/test/, to: '/test.html' },
          { from: /^\/__private/, to: '/swell_internal.html' },
          { from: /^\/me\/$/, to: '/me.html' },
          { from: /^\/me\/js\/swellweb\.js/, to: '/swellweb.js' },
          // Default route - catchall (using a more explicit pattern)
          { from: /^\/.*$/, to: '/swellcast.html' },
        ],
      },
      hot: true,
      port: 3001,
      proxy: [
        {
          context: '/graphql',
          target: 'https://stagewidgetapi.swell.life',
          changeOrigin: true,
          secure: true,
          router: function (req: { query: { stage: string } }) {
            const stage = req.query.stage as string;
            const target = `https://${stage === 'prod' ? '' : 'stage'}widgetapi.swell.life`;
            return target;
          },
        },
        {
          context: ['/proxy'],
          target: 'http://localhost:3001',
          router: function (req: { url: string }) {
            const url = new URL(req.url, 'http://localhost:3001');
            const targetUrl = url.searchParams.get('url');
            if (!targetUrl) return 'http://localhost:3001';
            return targetUrl;
          },
          pathRewrite: { '^/proxy/.*': '' },
          changeOrigin: true,
          secure: false,
        },
      ],
    },
    target: 'web',
    mode: 'development',
    resolve: {
      extensions: ['.ts', '.tsx', '.js', '.jsx'],
      alias: {
        '@images': path.resolve(__dirname, '../src/assets/images'),
        '@icons': path.resolve(__dirname, '../src/assets/icons'),
        '@audio': path.resolve(__dirname, '../src/assets/audio'),
        '@css': path.resolve(__dirname, '../src/assets/css'),
      },
    },
    plugins: [
      new CopyPlugin({
        patterns: [
          {
            from: path.resolve(__dirname, '../src/view/me/swellweb.js'),
            to: 'me/js/swellweb.js',
          },
        ],
      }),
      new HtmlWebpackPlugin({
        filename: 'swellcast.html',
        templateContent,
        chunks: ['swellcast'],
      }),
      new HtmlWebpackPlugin({
        filename: 'finder_featured.html',
        templateContent,
        chunks: ['finder_featured'],
      }),
      new HtmlWebpackPlugin({
        filename: 'finder_search.html',
        templateContent,
        chunks: ['finder_search'],
      }),
      new HtmlWebpackPlugin({
        filename: 'finder_latest.html',
        templateContent,
        chunks: ['finder_latest'],
      }),
      new HtmlWebpackPlugin({
        filename: 'widget_swellcast.html',
        templateContent,
        chunks: ['widget_swellcast'],
      }),
      new HtmlWebpackPlugin({
        filename: 'widget_swell.html',
        templateContent,
        chunks: ['widget_swell'],
      }),
      new HtmlWebpackPlugin({
        filename: 'widget_search.html',
        templateContent,
        chunks: ['widget_search'],
      }),
      new HtmlWebpackPlugin({
        filename: 'test.html',
        templateContent,
        chunks: ['test'],
      }),
      new HtmlWebpackPlugin({
        filename: 'swell_internal.html',
        templateContent,
        chunks: ['swell_internal'],
      }),
      new HtmlWebpackPlugin({
        filename: 'me.html',
        templateContent,
        chunks: ['me'],
      }),
      //   new HtmlWebpackPlugin({
      //     filename: 'swellweb.js',
      //     templateContent: '',
      //     chunks: ['swellweb'],
      //     inject: false,
      //   }),
      new ReactRefreshWebpackPlugin(),
      new Dotenv({ path: envStage }),
    ],
    module: {
      rules: [
        /*
        {
          test: /\.(ts|tsx)$/,
          exclude: /node_modules/,
          use: [
            {
              loader: require.resolve('ts-loader'),
              options: {
                transpileOnly: true, // Speed up compilation in development
                getCustomTransformers: () => ({
                  before: [require('react-refresh-typescript').default()],
                }),
              },
            },
          ],
        },
        */
        {
          test: /\.(ts|tsx|js|jsx)$/,
          exclude: /node_modules/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                presets: ['@babel/preset-env', ['@babel/preset-react', { runtime: 'automatic' }], '@babel/preset-typescript'],
                plugins: [
                  // Add React Compiler plugin
                  'react-compiler',
                  // Add React Refresh plugin for hot reloading
                  require.resolve('react-refresh/babel'),
                ],
              },
            },
          ],
        },
        {
          test: /\.(css|scss|sass)$/,
          use: [
            'style-loader',
            'css-loader',
            'postcss-loader',
            {
              loader: 'sass-loader',
              options: {
                api: 'modern',
                sassOptions: {
                  quietDeps: true, // Suppress warnings from imported files
                  verbose: true,
                },
              },
            },
          ],
        },
        {
          test: /\.svg$/,
          resourceQuery: /url/, // Handle *.svg?url
          type: 'asset/resource',
        },
        {
          test: /\.svg$/,
          issuer: /\.[jt]sx?$/,
          resourceQuery: { not: [/url/] }, // exclude react component if *.svg?url
          use: {
            loader: '@svgr/webpack',
            options: {
              svgo: false, // Disable SVGO optimization in development to avoid parsing errors
            },
          },
        },
        {
          test: /\.(png|jpe?g|gif)$/i,
          type: 'asset/resource',
        },
        {
          test: /\.(mp3|wav|ogg)$/i,
          type: 'asset/resource',
        },
      ],
    },
    output: {
      crossOriginLoading: 'anonymous',
      publicPath: '/',
      path: path.join(__dirname, '../dist'),
      filename: '[name].js',
      clean: true,
    },
  };
};

export default config;
