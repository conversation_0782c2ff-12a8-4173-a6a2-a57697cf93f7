// import dotenv from 'dotenv';
// import path from 'path';
import { Configuration } from 'webpack';
import type { Configuration as DevServerConfiguration } from 'webpack-dev-server';

// dotenv.config({ path: path.join(__dirname, '../.env.sls') });

const config: Configuration = {
  devServer: {
    hot: true,
    devMiddleware: {
      writeToDisk: true,
    },
    historyApiFallback: {
      disableDotRule: true,
    },
    server: 'https',
    port: 3003,
    host: '0.0.0.0',
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
    },
    proxy: [
      {
        context: '/proxy',
        target: 'https://stagewidgetapi.swell.life',
        changeOrigin: true,
        secure: true,
        onProxyReq: function (_proxyReq: any, req: any) {
          const stage = req.query.stage as string;
          return `https://${stage === 'prod' ? '' : 'stage'}widgetapi.swell.life`;
        },
      },
    ],
  } as DevServerConfiguration,
};

export default config;
