import CopyWebpackPlugin from 'copy-webpack-plugin';
import dotenv from 'dotenv';
import Dotenv from 'dotenv-webpack';
import ForkTsCheckerWebpackPlugin from 'fork-ts-checker-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import path from 'path';
import slsw from 'serverless-webpack';
import { Configuration } from 'webpack';

const envPath = path.join(__dirname, '../.env.sls');
dotenv.config({ path: envPath });

const config: Configuration = {
  entry: slsw.lib.entries,
  target: 'node',
  mode: slsw.lib.webpack.isLocal ? 'development' : 'production',
  infrastructureLogging: {
    level: 'error'
  },
  optimization: {
    // We don't need to minimize our Lambda code.
    minimize: false
  },
  performance: {
    // Turn off size warnings for entry points
    hints: false
  },
  devtool: 'eval',
  resolve: {
    extensions: ['.ts', '.tsx', '...'],
    alias: {
      '@images': path.resolve(__dirname, '../src/assets/images/')
    }
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        loader: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.(sa|sc|c)ss$/,
        use: [{ loader: MiniCssExtractPlugin.loader, options: { emit: false } }, 'css-loader', 'sass-loader']
      },
      {
        test: /\.(jpg|jpeg|png|gif|eot|otf|webp|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|ico)$/,
        type: 'asset/resource',
        generator: { emit: false }
      },
      {
        test: /\.(ejs|txt|html)$/,
        type: 'asset/source',
        generator: { emit: false }
      },
      {
        test: /\.svg$/i,
        type: 'asset/resource',
        resourceQuery: /url/, // *.svg?url
        generator: { emit: false }
      },
      {
        test: /\.(svg)$/,
        issuer: /\.[jt]sx?$/,
        resourceQuery: { not: [/url/] }, // exclude react component if *.svg?url
        use: '@svgr/webpack',
        generator: { emit: false }
      }
    ]
  },
  plugins: [
    new ForkTsCheckerWebpackPlugin(),
    new CopyWebpackPlugin({
      patterns: [{ from: 'public', to: 'public' }]
    }),
    new MiniCssExtractPlugin({
      filename: '[name].css'
    }),
    new Dotenv({ path: envPath })
  ],
  output: {
    libraryTarget: 'commonjs2',
    publicPath: process.env.ASSET_PATH + '/',
    path: path.join(__dirname, '../.webpack'),
    filename: '[name].js',
    sourceMapFilename: '[file].map',
    assetModuleFilename: 'assets/[name][ext]',
    clean: true
  }
};

export default config;
