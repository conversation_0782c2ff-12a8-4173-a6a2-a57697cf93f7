import { APIGatewayProxyEvent, APIGatewayProxyHandler } from 'aws-lambda';
import ejs from 'ejs';
import { matchRoutes } from 'react-router-dom';
import { ServerRoutes } from './server/ServerRoutes';
import { renderDebug } from './server/renderDebug';

const STAGE = process.env?.STAGE ?? 'prod';
const SERVER_ROUTES = STAGE in ServerRoutes ? ServerRoutes[STAGE] : ServerRoutes.prod;

export const serve: APIGatewayProxyHandler = async (event: APIGatewayProxyEvent) => {
  try {
    if (STAGE !== 'prod' && event.path === '/__lambda') {
      return renderDebug(event);
    }
    if (STAGE !== 'prod' && event.path === '/__error') {
      throw new Error('Failure IS an option!');
    }
    const routeInfo = matchRoutes(SERVER_ROUTES, event.path)![0];
    return await routeInfo.route.renderResponse({ ...routeInfo, event });
  } catch (err) {
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'text/html' },
      body: await ejs.renderFile(`./public/html/error.ejs`, { err }),
    };
  }
};
