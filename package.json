{"name": "swellcast-site", "version": "2.0.91-4", "private": true, "description": "<PERSON><PERSON>", "license": "MIT", "author": "<PERSON><PERSON>", "scripts": {"build:prod": "sls export-env --stage prod && yarn codegen && webpack --config ./config/webpack.client.config.ts --mode production && sls package -s prod", "build:stage": "webpack --config ./config/webpack.client.config.ts --mode production && sls package -s stage", "codegen": "graphql-codegen", "deploy:assets": "ts-node ./script/deployServer.ts", "deploy:local": "ts-node ./script/deployLocal.ts", "deploy:sls": "sls deploy --force", "dev": "webpack serve --config ./config/webpack.dev.config.ts --mode development", "doc": "typedoc", "exportenv": "sls export-env", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"src/**/*.tsx\" \"test/**/*.tsx\"", "lint": "eslint -c .eslintrc.js --ext .tsx,.ts src", "pack:client": "webpack --config ./config/webpack.client.config.ts", "pack:sls": "sls package", "release:prod": "cli-confirm \"Are you sure you want to deploy to production?\" && (yarn version --patch && git push origin --tags && git push)", "release:stage": "yarn version --prerelease && git push", "prestart": "rimraf dist & rm -f .env.sls", "start": "(sls export-env --stage stage && yarn codegen) && yarn pack:client --mode development && (webpack serve --config ./config/webpack.client.config.ts --mode development & sls offline start -s stage --reloadHandler)"}, "browserslist": "> 0.5%, last 2 versions, not dead", "dependencies": {}, "devDependencies": {"@aws-sdk/client-cloudfront": "^3.726.1", "@aws-sdk/client-s3": "^3.726.1", "@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@emotion/react": "^11.10.8", "@emotion/styled": "^11.10.8", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/introspection": "^3.0.1", "@graphql-codegen/typescript": "^4.0.6", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/types": "^7.2.14", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@serverless/utils": "^6.15.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.71.1", "@tanstack/react-query-devtools": "^5.71.2", "@types/aws-lambda": "^8.10.131", "@types/cors": "^2.8.13", "@types/debounce": "^1.2.1", "@types/dotenv-webpack": "^7.0.8", "@types/ejs": "^3.1.1", "@types/enzyme": "^3.10.12", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/grecaptcha": "^3.0.9", "@types/jest": "^27.5.2", "@types/js-cookie": "^3.0.6", "@types/js-search": "^1.4.0", "@types/mime-types": "^2.1.4", "@types/node": "^20.12.6", "@types/node-fetch": "^2.6.12", "@types/proper-url-join": "^2.1.5", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/react-helmet": "^6.1.5", "@types/react-helmet-async": "^1.0.3", "@types/react-router": "^5.1.20", "@types/sass-loader": "^8.0.9", "@types/source-map-support": "^0.5.6", "@types/unused-webpack-plugin": "^2.4.5", "@types/url-join": "^4.0.1", "@types/webpack": "^5.28.5", "@types/webpack-dev-server": "^4.7.2", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1324.0", "babel-loader": "^10.0.0", "babel-plugin-react-compiler": "^19.1.0-rc.2", "bootstrap": "^5.3.3", "classnames": "^2.5.1", "cli-confirm": "^1.0.1", "copy-webpack-plugin": "^11.0.0", "cross-fetch": "^3.1.5", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^3.4.1", "dotenv": "^16.4.5", "dotenv-webpack": "^8.0.1", "ejs": "^3.1.8", "enzyme": "^3.11.0", "eslint": "^8.26.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "fork-ts-checker-webpack-plugin": "^7.2.13", "glob": "^11.0.1", "graphql": "^16.6.0", "html-webpack-plugin": "^5.6.3", "http-proxy-middleware": "^2.0.6", "idb-keyval": "^6.2.2", "identity-obj-proxy": "^3.0.0", "imagemin": "^8.0.1", "imagemin-optipng": "^8.0.0", "imagemin-svgo": "^10.0.1", "imagemin-webp": "^8.0.0", "jest": "^27.5.1", "js-cookie": "^3.0.1", "js-search": "^2.0.0", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.7.6", "node-fetch": "^3.3.2", "postcss-loader": "^8.1.1", "proper-url-join": "^2.1.2", "purgecss-webpack-plugin": "^4.1.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.4", "react-intersection-observer": "^9.13.1", "react-keep-awake": "^0.1.1", "react-qr-code": "^2.0.8", "react-refresh": "^0.17.0", "react-refresh-typescript": "^2.0.10", "react-router": "^7.6.2", "react-router-dom": "^7.6.3", "react-scrubber": "^2.0.0", "react-use-wake-lock": "^1.0.1", "regenerator-runtime": "^0.13.10", "rimraf": "^3.0.2", "sass": "^1.83.0", "sass-loader": "^16.0.4", "serverless": "^4.10.0", "serverless-export-env": "^2.2.0", "serverless-offline": "^14.4.0", "serverless-provisioned-concurrency-autoscaling": "^2.0.1", "serverless-webpack": "^5.15.1", "source-map-support": "^0.5.21", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.6", "testcafe": "^1.20.1", "ts-jest": "^27.1.5", "ts-loader": "^9.4.1", "ts-node": "^10.9.2", "typedoc": "^0.23.20", "typescript": "^5.7.2", "unused-webpack-plugin": "^2.4.0", "usehooks-ts": "^3.1.0", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-node-externals": "^3.0.0"}, "engines": {"node": ">=20.10.0", "yarn": "^1.22.19"}, "NOTES": ["TODO: graphql-codegen not working in github - affects build:prod, build:stage", "he:^1.2.0"], "test": {"swell-common-js": "https://github.com/anecure/swell-common-js"}}