<!DOCTYPE html>
<html lang="en">
  <head>
    <% if (base) { %>
    <base href="<%- base %>" />
    <% } %>
    <!-- Helmet tags -->
    <%- helmet?.title?.toString().replace(/\/>/g,'/>\n\t') ?? '' %> <%- helmet?.meta?.toString().split('/><').filter(m => m.indexOf('content=""')===-1).join('/>\n<') ?? '' %> <%- helmet?.link?.toString().replace(/\/>/g,'/>\n\t') ?? '' %> <%- helmet?.script?.toString().replace(/\/>/g,'/>\n\t') ?? '' %>
    <!-- Google Tags -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', '<%-process.env.GTM_ID %>');
    </script>
    <link rel="stylesheet" href="<%-css%>" />
    <script>
      window.__REACT_QUERY_STATE__ = <%-JSON.stringify(dehydratedState)%>;
    </script>
  </head>
  <body data-bs-theme="light">
    <script src="<%-beforeContentJS%>"></script>
    <script src="<%-webviewOverlayJS%>"></script>
    <div id="root"><%-innerHTML%></div>
    <script>
      // hide the page instantaneously
      document.getElementById('root').style.display = 'none';
    </script>
    <script src="/me/js/swellweb.js"></script>
    <script src="<%-js%>"></script>
  </body>
</html>
