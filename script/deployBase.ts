import { CloudFrontClient, CreateInvalidationCommand } from '@aws-sdk/client-cloudfront';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import * as fs from 'fs';
import { glob } from 'glob';
import mime from 'mime-types';
import * as path from 'path';

// Configuration interface
interface S3UploadConfig {
  sourcePattern: string; // e.g., 'dist/**/*'
  destinationPrefix: string; // e.g., 'my-app/1.0.0'
  bucket: string; // S3 bucket name
  region: string; // AWS region, defaults to 'us-east-2'
  accessKeyId: string; // AWS access key
  secretAccessKey: string; // AWS secret key
  includeSourceDir?: boolean; // Whether to include the source dir (e.g., 'dist') in S3 keys
  distributionId: string; // your CloudFront ID
  invalidateAfterUpload: boolean;
}

// Cache control settings
const CACHE_CONTROL = {
  js: 86400, // 1 day for JS files
  css: 86400, // 1 day for CSS files
  images: 86400, // 1 day for images
  html: 300, // 5 minutes for HTML
  default: 86400, // Default fallback
};

// Determine cache control based on file extension
function getCacheControl(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase().replace('.', '');
  let maxAge: number;

  switch (ext) {
    case 'js':
    case 'map':
      maxAge = CACHE_CONTROL.js;
      break;
    case 'css':
      maxAge = CACHE_CONTROL.css;
      break;
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
      maxAge = CACHE_CONTROL.images;
      break;
    case 'html':
      maxAge = CACHE_CONTROL.html;
      break;
    default:
      maxAge = CACHE_CONTROL.default;
  }

  return `public, max-age=${maxAge}`;
}

export async function uploadToS3({ region, accessKeyId, secretAccessKey, ...config }: S3UploadConfig): Promise<void> {
  // Validate config
  if (!config.bucket || !accessKeyId || !secretAccessKey) {
    throw new Error('Missing required S3 configuration: bucket, accessKeyId, or secretAccessKey');
  }

  // Initialize S3 client
  const s3Client = new S3Client({
    region,
    credentials: {
      accessKeyId,
      secretAccessKey,
    },
  });

  // Normalize paths
  const bucket = config.bucket.replace(/\/$/, '');
  const prefix = config.destinationPrefix.replace(/^\//, '').replace(/\/$/, '');
  const sourceBase = path.dirname(config.sourcePattern.split('/')[0]); // e.g., 'dist'
  const includeSourceDir = config.includeSourceDir ?? true; // Default to including source dir

  // Get list of files to upload
  const files: string[] = await glob(config.sourcePattern, { nodir: true });

  if (files.length === 0) {
    console.warn('No files found to upload');
    return;
  }

  console.log(`Uploading ${files.length} files to s3://${bucket}/${prefix}`);

  // Upload files concurrently
  const uploadPromises = files.map(async (filePath) => {
    // Compute the relative path, optionally stripping the source directory
    const relativePath = includeSourceDir
      ? path.relative(sourceBase, filePath) // Includes 'dist'
      : path.relative(sourceBase, filePath).replace(/^dist\//, ''); // Strips 'dist/'

    const key = prefix ? `${prefix}/${relativePath}` : relativePath; // Apply prefix if provided
    const normalizedKey = key.replace(/\/+/g, '/'); // Ensure single slashes
    const contentTypeBase = mime.lookup(filePath) || 'application/octet-stream';
    const contentType = /^(text\/|application\/(javascript|json|xml))/.test(contentTypeBase) ? `${contentTypeBase}; charset=utf-8` : contentTypeBase;
    const cacheControl = getCacheControl(filePath);

    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: bucket,
          Key: normalizedKey,
          Body: fs.createReadStream(filePath),
          ContentType: contentType,
          CacheControl: cacheControl,
        }),
      );
      console.log(`Uploaded: s3://${bucket}/${normalizedKey}`);
    } catch (error) {
      console.error(`Failed to upload ${filePath} to s3://${bucket}/${normalizedKey}:`, error);
      throw error;
    }
  });

  // Wait for all uploads to complete
  await Promise.all(uploadPromises);
  console.log('Upload complete');
}

interface InvalidateConfig {
  distributionId: string;
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
  paths: string[];
}

export async function invalidateCloudFront({ distributionId, accessKeyId, secretAccessKey, region = 'us-east-2', paths }: InvalidateConfig): Promise<void> {
  const cloudfront = new CloudFrontClient({
    region,
    credentials: {
      accessKeyId,
      secretAccessKey,
    },
  });

  try {
    const command = new CreateInvalidationCommand({
      DistributionId: distributionId,
      InvalidationBatch: {
        CallerReference: `${Date.now()}`,
        Paths: {
          Quantity: paths.length,
          Items: paths,
        },
      },
    });

    const res = await cloudfront.send(command);
    console.log(`✅ CloudFront invalidation created:`, res.Invalidation?.Id);
  } catch (error) {
    console.error(`❌ CloudFront invalidation failed:`, error);
  }
}
