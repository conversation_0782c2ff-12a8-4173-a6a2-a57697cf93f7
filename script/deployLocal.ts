import dotenv from 'dotenv';
import * as path from 'path';
import pkg from '../package.json';
import { invalidateCloudFront, uploadToS3 } from './deployBase';

dotenv.config({ path: '.env.aws' });

async function main() {
  try {
    await uploadToS3({
      sourcePattern: 'dist/{swellcast,widget_search,widget_swell,widget_swellcast}.js',
      destinationPrefix: path.join(pkg.name, pkg.version),
      bucket: process.env.AWS_BUCKET!,
      region: process.env.AWS_REGION!,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      includeSourceDir: false,
      distributionId: 'E1BUNXIG16L04L',
      invalidateAfterUpload: true,
    });

    await invalidateCloudFront({
      region: process.env.AWS_REGION!,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      distributionId: 'E1BUNXIG16L04L',
      paths: [
        `/${path.join(pkg.name, pkg.version)}/swellcast.js`, //
        `/${path.join(pkg.name, pkg.version)}/widget_search.js`,
        `/${path.join(pkg.name, pkg.version)}/widget_swell.js`,
        `/${path.join(pkg.name, pkg.version)}/widget_swellcast.js`,
      ],
    });

    await invalidateCloudFront({
      region: process.env.AWS_REGION!,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      distributionId: 'E2LHGJL621QDKU',
      paths: [
        `/${path.join(pkg.name, pkg.version)}/swellcast.js`, //
        `/${path.join(pkg.name, pkg.version)}/widget_search.js`,
        `/${path.join(pkg.name, pkg.version)}/widget_swell.js`,
        `/${path.join(pkg.name, pkg.version)}/widget_swellcast.js`,
      ],
    });
  } catch (error) {
    console.error('S3 upload failed:', error);
    process.exit(1);
  }
}

main();
