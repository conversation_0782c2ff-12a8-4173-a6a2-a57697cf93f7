import dotenv from 'dotenv';
import * as path from 'path';
import pkg from '../package.json';
import { invalidateCloudFront, uploadToS3 } from './deployBase';

dotenv.config({ path: '.env.sls' });

async function main() {
  try {
    await uploadToS3({
      sourcePattern: 'dist/**/*',
      destinationPrefix: path.join(pkg.name, pkg.version),
      bucket: process.env.AWS_BUCKET!,
      region: process.env.AWS_REGION!,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      includeSourceDir: false,
      distributionId: process.env.DISTRIBUTION_ID!,
      invalidateAfterUpload: true,
    });

    await invalidateCloudFront({
      region: process.env.AWS_REGION!,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      distributionId: process.env.DISTRIBUTION_ID!,
      paths: [`/${path.join(pkg.name, pkg.version)}/*`],
    });
  } catch (error) {
    console.error('S3 upload failed:', error);
    process.exit(1);
  }
}

main();
