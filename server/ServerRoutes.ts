import { DehydratedState } from '@tanstack/react-query';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { HelmetServerState } from 'react-helmet-async';
import { RouteMatch, RouteObject, generatePath } from 'react-router-dom';
import manifestFile from '../public/manifest.json';
import norobots from '../public/norobots.txt';
import robots from '../public/robots.txt';
import { exposeEnv } from '../src/framework/exposeEnv';
import { IServerSettings, RouteParams } from '../src/models/models';
import { finderFeaturedRoutes } from '../src/view/go/finder/featured/routes';
import { finderSearchRoutes } from '../src/view/go/finder/hash/routes';
import { finderLatestRoutes } from '../src/view/go/finder/latest/routes';
import { meRoutes } from '../src/view/me/routes';
import { swellWidgetRoutes } from '../src/view/swell-widget/swellWidgetRoutes';
import { swellcastWidgetRoutes } from '../src/view/swellcast-widget/swellcastWidgetRoutes';
import { testRouteMap } from '../src/view/test/testRoutes';
import { websiteRouteMap } from '../src/view/website/websiteRoutes';
import { proxyAPI } from './proxyAPI';
import { renderPage } from './renderPage';
import { renderProxy } from './renderProxy';
import { renderResponsePolicy } from './renderResponsePolicy';

const manifestBody = JSON.stringify(manifestFile);

interface MatchedRoute<P extends string, R extends RouteObject> {
  match: RouteMatch<P, R>;
}

export interface IServerRenderProps<Q = { [key: string]: string }> extends MatchedRoute<keyof RouteParams, IServerRoute> {
  query: RouteParams & Q;
  settings: IServerSettings;
  event: APIGatewayProxyEvent;
  name?: string;
  basename?: string;
  params?: Q;
  routes: RouteObject[];
  tmpl?: string;
}

export interface ITemplateConfig {
  path: string;
  base: string;
  innerHTML: string;
  helmet: Partial<HelmetServerState> | undefined;
  dehydratedState: DehydratedState | null;
  css: string;
  js: string;
  beforeContentJS: string;
  webviewOverlayJS: string;
}

export type IServerRoute<Q = Record<string, string | undefined>> = RouteObject & {
  renderResponse(props: Partial<IServerRenderProps<Q>>): Promise<APIGatewayProxyResult>;
};

// must explicitly define keys for type checking to work properly. Otherwise you get a murky error if a route doesn't exist
type RouteBlockKeys =
  | 'test'
  | 'norobots' //
  | 'private'
  | 'graphql'
  | 'robots'
  | 'favicon'
  | 'manifest'
  | 'proxy'
  | 'copyrightpolicy'
  | 'termsofservice'
  | 'privacypolicy'
  | 'swellweb'
  | 'me'
  | 'finder_featured'
  | 'finder_search'
  | 'finder_latest'
  | 'swellcast_widget'
  | 'swell_widget'
  | 'widget_search'
  | 'swellForward'
  | 'swellForwardStage'
  | 'swellForwardP'
  | 'swellForwardPStage'
  | 'env'
  | 'offsitepage'
  | 'stations'
  | 'station'
  | 'catchall';

// map of all server renedered routes
export const RouteBlocks: Record<RouteBlockKeys, IServerRoute> = {
  env: {
    path: '/__private/env',
    renderResponse: async () => {
      return {
        statusCode: 200,
        headers: { contentType: 'application/json' },
        body: JSON.stringify(
          exposeEnv.reduce((s, k) => ({ ...s, [k]: process.env?.[k] ?? '' }), {}),
          null,
          2,
        ),
      };
    },
  },
  proxy: {
    path: '/proxy/*',
    renderResponse: renderProxy,
  },
  graphql: {
    path: '/graphql',
    renderResponse: (props) => proxyAPI(props.event),
  },
  norobots: {
    path: '/robots.txt',
    renderResponse: async () => ({
      statusCode: 200, //
      headers: { 'Content-Type': 'text/plain' },
      body: norobots,
    }),
  },
  robots: {
    path: '/robots.txt',
    renderResponse: async () => ({
      statusCode: 200, //
      headers: { 'Content-Type': 'text/plain' },
      body: robots,
    }),
  },
  favicon: {
    path: '/favicon.ico',
    renderResponse: async () => ({
      statusCode: 301,
      headers: { location: 'https://assets.swell.life/public/img/favicon-16x16.png' },
      body: '',
    }),
  },
  manifest: {
    path: '/manifest.json',
    renderResponse: async () => ({
      statusCode: 200, //
      headers: { 'Content-Type': 'application/json' },
      body: manifestBody,
    }),
  },
  swellweb: {
    path: '/me/js/swellweb.js',
    renderResponse: async () => {
      return {
        statusCode: 301,
        headers: { location: `${process.env.ASSET_PATH}/swellweb.js` },
        body: '',
      };
    },
  },
  offsitepage: {
    path: '/go/pages/StartCommunityPodcast.html',
    renderResponse: async () => {
      return {
        statusCode: 200,
        headers: { 'Content-Type': 'text/html' },
        body: `<html><body>Offsite Page</body></html>`,
      };
    },
  },
  test: {
    path: '/test/*',
    renderResponse: (props) => renderPage({ ...props, name: 'test', basename: '/test', routes: testRouteMap }),
  },
  me: {
    path: '/me/',
    renderResponse: (props) => renderPage({ ...props, name: 'me', routes: meRoutes }),
  },
  private: {
    path: '/__private/*',
    renderResponse: (props) => renderPage({ ...props, name: 'swell_internal', tmpl: 'basic' }),
  },
  finder_featured: {
    path: '/go/finder/featured',
    renderResponse: (props) => renderPage({ ...props, name: 'finder_featured', routes: finderFeaturedRoutes }),
  },
  finder_search: {
    path: '/go/finder/search',
    renderResponse: (props) => renderPage({ ...props, name: 'finder_search', routes: finderSearchRoutes }),
  },
  finder_latest: {
    path: '/go/finder/latest',
    renderResponse: (props) => renderPage({ ...props, name: 'finder_latest', routes: finderLatestRoutes }),
  },
  swellcast_widget: {
    path: '/widget/*',
    renderResponse: (props) => renderPage({ ...props, name: 'widget_swellcast', routes: swellcastWidgetRoutes }),
  },
  swell_widget: {
    path: '/widget-swell/*',
    renderResponse: (props) => renderPage({ ...props, name: 'widget_swell', routes: swellWidgetRoutes }),
  },
  widget_search: {
    path: '/widget/search/*',
    renderResponse: (props) => renderPage({ ...props, name: 'widget_search' }),
  },
  swellForward: {
    path: '/t/:id',
    renderResponse: async (props: IServerRenderProps) => {
      return {
        statusCode: 301,
        headers: { location: new URL(generatePath('/:id', { id: props.params?.id as string }), 'https://s.swell.life').href },
        body: '',
      };
    },
  },
  swellForwardStage: {
    path: '/t/:id',
    renderResponse: async (props: IServerRenderProps<{ id: string }>) => {
      return {
        statusCode: 301,
        headers: { location: new URL(generatePath('/:id', { id: props.params?.id as string }), 'https://stageshort.swell.life').href },
        body: '',
      };
    },
  },
  swellForwardP: {
    path: '/p/:id',
    renderResponse: async (props: IServerRenderProps) => {
      return {
        statusCode: 301,
        headers: { location: new URL(generatePath('/:id', { id: props.params?.id as string }), 'https://s.swell.life').href },
        body: '',
      };
    },
  },
  swellForwardPStage: {
    path: '/p/:id',
    renderResponse: async (props: IServerRenderProps<{ id: string }>) => {
      return {
        statusCode: 301,
        headers: { location: new URL(generatePath('/:id', { id: props.params?.id as string }), 'https://stageshort.swell.life').href },
        body: '',
      };
    },
  },
  copyrightpolicy: {
    path: '/copyrightpolicy',
    renderResponse: renderResponsePolicy,
  },
  termsofservice: {
    path: '/termsofservice',
    renderResponse: renderResponsePolicy,
  },
  privacypolicy: {
    path: '/privacypolicy',
    renderResponse: renderResponsePolicy,
  },
  stations: {
    path: '/stations',
    renderResponse: async () => {
      return {
        statusCode: 301,
        headers: { location: '/categories' },
        body: '',
      };
    },
  },
  station: {
    path: '/station/*',
    renderResponse: async (props) => {
      return {
        statusCode: 301,
        headers: { location: '/category/' + (props?.params?.['*'] ?? '') },
        body: '',
      };
    },
  },
  catchall: {
    path: '*',
    renderResponse: (props) => renderPage({ ...props, name: 'swellcast', routes: websiteRouteMap }),
  },
};

// include routes for intended environments
export const ServerRoutes: Record<string, IServerRoute[]> = {
  local: [
    // local only
    RouteBlocks.me,
    RouteBlocks.swellweb,
    RouteBlocks.offsitepage,
    // local/stage only
    RouteBlocks.test,
    RouteBlocks.env,
    RouteBlocks.graphql,
    RouteBlocks.private,
    // prod
    RouteBlocks.favicon,
    RouteBlocks.manifest,
    RouteBlocks.norobots,
    RouteBlocks.stations,
    RouteBlocks.station,
    RouteBlocks.swellForwardStage,
    RouteBlocks.swellForwardPStage,
    RouteBlocks.finder_featured,
    RouteBlocks.finder_latest,
    RouteBlocks.finder_search,
    RouteBlocks.widget_search,
    RouteBlocks.swellcast_widget,
    RouteBlocks.swell_widget,
    RouteBlocks.copyrightpolicy,
    RouteBlocks.termsofservice,
    RouteBlocks.privacypolicy,
    RouteBlocks.proxy,
    RouteBlocks.catchall,
  ],
  stage: [
    // stage only
    RouteBlocks.test,
    RouteBlocks.env,
    RouteBlocks.private,
    // prod
    RouteBlocks.favicon,
    RouteBlocks.manifest,
    RouteBlocks.norobots,
    RouteBlocks.stations,
    RouteBlocks.station,
    RouteBlocks.swellForwardStage,
    RouteBlocks.swellForwardPStage,
    RouteBlocks.finder_featured,
    RouteBlocks.finder_latest,
    RouteBlocks.finder_search,
    RouteBlocks.swellcast_widget,
    RouteBlocks.swell_widget,
    RouteBlocks.widget_search,
    RouteBlocks.copyrightpolicy,
    RouteBlocks.termsofservice,
    RouteBlocks.privacypolicy,
    RouteBlocks.proxy,
    RouteBlocks.catchall,
  ],
  prod: [
    RouteBlocks.favicon, //
    RouteBlocks.manifest,
    RouteBlocks.robots,
    RouteBlocks.stations,
    RouteBlocks.station,
    RouteBlocks.swellForward,
    RouteBlocks.swellForwardP,
    RouteBlocks.finder_featured,
    RouteBlocks.finder_latest,
    RouteBlocks.finder_search,
    RouteBlocks.swellcast_widget,
    RouteBlocks.swell_widget,
    RouteBlocks.widget_search,
    RouteBlocks.copyrightpolicy,
    RouteBlocks.termsofservice,
    RouteBlocks.privacypolicy,
    RouteBlocks.proxy,
    RouteBlocks.catchall,
  ],
};
