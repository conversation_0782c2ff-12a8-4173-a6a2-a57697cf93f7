import SwellLogoAMP from '@images/swell-logo-amp.png';
import { OpenApiSwellResponseEnhanced } from '../src/api/gql.loadSwellById';
import { buildUrl } from '../src/framework/buildUrl';
import { stripFirstUrl } from '../src/utils/Utils';
import { getAuthorFullname } from '../src/utils/swell-utils';

export function buildSchema(path: string, swell: OpenApiSwellResponseEnhanced) {
  let schema: Record<string, unknown> = {};

  // home page, search in query
  if (path === '/') {
    // ref: https://schema.org/WebSite
    schema = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'Swell',
      path,
      url: buildUrl({ host: process.env.CANONICAL_DOMAIN, protocol: 'https:' }),
      potentialAction: {
        '@type': 'SearchAction',
        target: buildUrl({ host: process.env.CANONICAL_DOMAIN, protocol: 'https:', searchParams: { search: '{query}' } }),
        query: 'required',
      },
    };
  }

  // search in url
  if (path.indexOf('/search') === 0) {
    // ref: https://schema.org/WebSite
    schema = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'Swell',
      path,
      url: buildUrl({ host: process.env.CANONICAL_DOMAIN, protocol: 'https:', pathname: path }),
      potentialAction: {
        '@type': 'SearchAction',
        target: buildUrl({ host: process.env.CANONICAL_DOMAIN, protocol: 'https:', pathname: `/search/{query}` }),
        query: 'required',
      },
    };
  }

  // swellpage
  if (swell) {
    try {
      // ref: https://schema.org/SocialMediaPosting
      // ref: https://www.newsweek.com/ - any article
      const canonicalPath = buildUrl({ host: process.env.CANONICAL_DOMAIN, protocol: 'https:', pathname: path });
      const authorUrl = buildUrl({ host: process.env.CANONICAL_DOMAIN, protocol: 'https:', pathname: swell.author?.alias?.toLowerCase() ?? 'author' });
      const description = stripFirstUrl(swell?.description ?? '').trim();
      const longDescription = `${description} ${swell?.snippet ?? ''}`.trim();

      schema = {
        '@context': 'https://schema.org',
        '@type': 'SocialMediaPosting',
        '@id': canonicalPath,
        name: 'Swell',
        mainEntityOfPage: {
          '@type': 'SocialMediaPosting',
          '@id': canonicalPath,
        },
        datePublished: swell.createdOn,
        dateModified: swell.lastRepliedOn,
        publisher: {
          '@type': 'Organization',
          name: 'Swell',
          url: 'https://www.swellcast.com/',
          logo: { '@type': 'ImageObject', url: SwellLogoAMP, width: 428, height: 153 },
        },
        headline: swell.title,
        ...(longDescription ? { description: longDescription } : {}),
        articleBody: [swell, ...swell.replies]
          .map((r) => `@${r.author?.alias}, ${getAuthorFullname(r.author)}, ${r?.snippet ?? ''}`)
          .join(' | ')
          .replace(/^\s+|\s+$/g, ''),
        author: {
          '@type': 'Person',
          name: getAuthorFullname(swell?.author),
          url: authorUrl,
          image: swell.author?.image,
        },
        image: {
          '@type': 'ImageObject',
          url: swell.articles?.[0]?.image,
          // width: 1600,
          // height: 900,
          caption: swell.title,
        },
        keywords: swell.keywords,
        isAccessibleForFree: 'True',
        sharedContent: {
          '@type': 'WebPage',
          headline: swell.title,
          ...(description ? { description: description } : {}),
          url: canonicalPath,
          author: {
            '@type': 'Person',
            name: `@${swell?.author?.alias ?? ''}`,
            image: swell?.author?.image ?? '',
          },
        },
      };
    } catch (err) {
      //   //
      //   schema = { err, path, swell };
    }
  }
  return schema;
}
