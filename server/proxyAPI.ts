import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import fetch from 'cross-fetch';
import { SWELL_API_PROD, SWELL_API_STAGE } from '../src/framework/settings/settings';

export const proxyAPI = async (event: APIGatewayProxyEvent | null | undefined): Promise<APIGatewayProxyResult> => {
  // allow stage switching via query param in local
  const query = event?.queryStringParameters ?? {};
  const stage = query?.stage ?? 'default';
  const url = stage == 'stage' || stage == 'default' ? SWELL_API_STAGE : SWELL_API_PROD;

  try {
    const response = await fetch(url, { method: 'POST', body: event?.body, headers: { 'content-type': 'application/json' } });
    if (!response.ok) {
      return {
        statusCode: response.status,
        body: JSON.stringify({ message: 'Failed to fetch file' }),
      };
    }

    const fileBuffer = await response.arrayBuffer();
    const base64File = Buffer.from(fileBuffer).toString('base64');

    return {
      statusCode: 200,
      headers: {
        'Content-Type': response.headers.get('Content-Type') ?? '',
      },
      body: base64File,
      isBase64Encoded: true,
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error', url }),
    };
  }
};
