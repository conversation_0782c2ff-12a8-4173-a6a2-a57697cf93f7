import { DefinedInitialDataOptions, QueryClient, UseInfiniteQueryOptions, UseQueryOptions, dehydrate } from '@tanstack/react-query';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { Request } from 'cross-fetch';
import ejs from 'ejs';
import { renderToStaticMarkup, renderToString } from 'react-dom/server';
import { HelmetServerState } from 'react-helmet-async';
import { StaticHandlerContext, createStaticHandler, createStaticRouter } from 'react-router-dom';
import { buildUrl } from '../src/framework/buildUrl';
import { exposeEnv } from '../src/framework/exposeEnv';
import { DEFAULT_SERVER_SETTINGS } from '../src/framework/settings/DEFAULT_SERVER_SETTINGS';
import { serverSettingsKey } from '../src/framework/settings/serverSettingsKey';
import { DEFAULT_COUNTRY_CODE, SWELL_API_PROD, SWELL_API_STAGE } from '../src/framework/settings/settings';
import { ServerRoot } from '../src/framework/shared/ServerRoot';
import { IAppProps, IServerSettings } from '../src/models/models';
import { getLocalApiUrl } from '../src/utils/getLocalApiUrl';
import { pickProps } from '../src/utils/pickProps';
import urlJoin from '../src/utils/urlJoin';
import { isWidgetPath } from '../src/utils/useIsWidget';
import { IServerRenderProps, IServerRoute, ITemplateConfig } from './ServerRoutes';

export function getCountryCodes(event: APIGatewayProxyEvent) {
  const serverCountryCode = (event?.headers?.['CloudFront-Viewer-Country'] ?? DEFAULT_COUNTRY_CODE).toUpperCase();
  const queryCountryCode = event?.queryStringParameters?.countryCode;
  const countryCode = queryCountryCode ?? serverCountryCode ?? DEFAULT_COUNTRY_CODE;
  return { serverCountryCode, queryCountryCode, countryCode };
}

export const renderPage: IServerRoute['renderResponse'] = async ({ event, name, basename = '', routes, tmpl = 'index' }: IServerRenderProps) => {
  let ASSET_PATH = process.env.ASSET_PATH ?? '';
  const isProdHost = process.env.STAGE === 'stage' || process.env.STAGE === 'local' ? false : true;
  const css = urlJoin(ASSET_PATH, `${name}.css`);
  const js = urlJoin(ASSET_PATH, `${name}.js`);
  const beforeContentJS = urlJoin(ASSET_PATH, `beforecontent.js`);
  const webviewOverlayJS = urlJoin(ASSET_PATH, `webviewOverlay.js`);

  // default template values
  const templateConfig: ITemplateConfig = {
    path: event.path,
    base: basename,
    innerHTML: '',
    helmet: { title: '', meta: '', link: '', script: '' } as unknown as HelmetServerState,
    dehydratedState: null,
    css,
    js,
    beforeContentJS,
    webviewOverlayJS,
  };

  const query = event?.queryStringParameters ?? {};
  const location = buildUrl({ pathname: event.path, searchParams: query as Record<string, string> });
  const helmetContext: IAppProps['helmetContext'] = { helmet: {} as HelmetServerState };
  const { serverCountryCode, countryCode } = getCountryCodes(event);
  const isLocal = process.env?.STAGE === 'local';
  const isStage = process.env?.STAGE === 'stage';
  const isProd = !isLocal && !isStage;
  const stage = isProd ? 'prod' : query?.stage === 'prod' ? 'prod' : 'stage';

  // when loading data on the server, we use direct API url
  // in local we proxy because of CORS

  const settings: IServerSettings = {
    ...DEFAULT_SERVER_SETTINGS,
    isLocal,
    isStage,
    isProd,
    stage,
    serverCountryCode,
    countryCode,
    apiUrl: stage === 'stage' ? SWELL_API_STAGE : SWELL_API_PROD,
    isWidget: isWidgetPath(event.path),
    version: 1,
  };

  const queryClientServer = new QueryClient();
  queryClientServer.setQueryData(['env'], pickProps(process.env, exposeEnv));

  if (routes) {
    queryClientServer.setQueryData(serverSettingsKey, settings);
    queryClientServer.setQueryData(['env'], { ...pickProps(process.env, exposeEnv), SWELL_API: settings.apiUrl });

    const fullUrl = new URL(location, 'https://' + process.env.CANONICAL_DOMAIN).toString();
    const { query } = createStaticHandler(routes, { basename });
    const ac = new AbortController();
    const context = (await query(new Request(fullUrl, { signal: ac.signal }))) as StaticHandlerContext;
    const router = createStaticRouter(routes, context);

    // render app to capture required queries
    renderToStaticMarkup(<ServerRoot queryClient={queryClientServer} router={router} helmetContext={helmetContext} />);

    // prefetch queries
    const allQueries = queryClientServer
      .getQueryCache()
      .getAll()
      .filter((q) => (q.options as DefinedInitialDataOptions)?.enabled); // enabled must be explicitly true for SSR to load the data
    await Promise.all(
      allQueries.map((q) => {
        const options = q.options as UseQueryOptions<any, any>; // | UseInfiniteQueryOptions<any, any>;
        const isInfinite = typeof (q.options as any)?.getNextPageParam === 'function';
        if (isInfinite) {
          return queryClientServer
            .prefetchInfiniteQuery<UseInfiniteQueryOptions<unknown>>({
              queryKey: q.queryKey,
              // @ts-ignore TODO: what the hell
              queryFn: options.queryFn,
              initialPageParam: (options as UseInfiniteQueryOptions).initialPageParam,
              getNextPageParam: (options as UseInfiniteQueryOptions).getNextPageParam,
            })
            .catch((error) => console.error(`Error prefetching ${q.queryKey}:`, error));
        } else {
          return queryClientServer
            .prefetchQuery({
              queryKey: q.queryKey,
              queryFn: options.queryFn,
            })
            .catch((error) => console.error(`Error prefetching ${q.queryKey}:`, error));
        }
      }),
    );

    templateConfig.innerHTML = renderToString(<ServerRoot router={router} queryClient={queryClientServer} helmetContext={helmetContext} />);
    templateConfig.helmet = helmetContext.helmet;
  }
  // reset settings/env to default values
  queryClientServer.setQueryData(serverSettingsKey, { ...settings, apiUrl: isLocal ? getLocalApiUrl(settings.stage) : settings.apiUrl });
  templateConfig.dehydratedState = dehydrate(queryClientServer);

  // clean up react-query
  queryClientServer.clear();

  // hack to forward android meta webview fail to browser
  const userAgent = event.headers?.['User-Agent'] ?? '';
  const isAndroidWebView = userAgent.includes('wv');
  const isInstagramWebView = userAgent.includes('Instagram');
  const isFacebookWebView = userAgent.includes('FB_IAB');
  const isMetaApp = isAndroidWebView && (isInstagramWebView || isFacebookWebView);
  const ContentType = isMetaApp ? 'application/octet-stream' : 'text/html';

  // add important headers
  const headers = {
    'Content-Type': ContentType,
    'Strict-Transport-Security': 'max-age=2678400; includeSubDomains',
    'X-Robots-Tag': isProdHost ? 'all' : 'noindex, nofollow',
  };

  const body = await ejs.renderFile(`./public/html/${tmpl}.ejs`, templateConfig);

  return {
    statusCode: 200,
    headers,
    body,
  };
};
