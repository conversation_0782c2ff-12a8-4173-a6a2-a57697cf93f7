// This is for local dev only
// It can be used to get the API without permission issues or to load editable images into canvas
import fetch from 'cross-fetch';
import { IServerRenderProps, IServerRoute } from './ServerRoutes';

// NOTE: written entirely by ChatGPT
export const renderProxy: IServerRoute<{ url: string; body?: string }>['renderResponse'] = async ({ event }: Partial<IServerRenderProps>) => {
  const query = event?.queryStringParameters ?? {};
  const fileUrl = query.url;
  const body = query?.body ?? event?.body;

  if (!fileUrl) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'File URL not provided' }),
    };
  }
  const hasBody = !!body;
  const method = hasBody ? 'POST' : 'GET';
  const headers = hasBody ? { 'content-type': 'application/json' } : undefined;

  try {
    const response = await fetch(fileUrl, { method, body, headers });
    const contentType = response.headers.get('Content-Type') ?? '';

    if (!response.ok) {
      return {
        statusCode: response.status,
        body: JSON.stringify({ message: 'Failed to fetch file' }),
      };
    }

    // If it's text-ish, return plain UTF-8:
    if (contentType.startsWith('text/') || contentType.includes('json') || contentType.includes('xml') || contentType.includes('javascript')) {
      const text = await response.text();
      return {
        statusCode: 200,
        headers: {
          'Content-Type': contentType,
        },
        body: text,
        isBase64Encoded: false,
      };
    }

    const fileBuffer = await response.arrayBuffer();
    const base64File = Buffer.from(fileBuffer).toString('base64');

    return {
      statusCode: 200,
      headers: {
        'Content-Type': response.headers.get('Content-Type') ?? '',
      },
      body: base64File,
      isBase64Encoded: true,
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: 'Internal server error', fileUrl }),
    };
  }
};
