import { APIGatewayProxyResult } from 'aws-lambda';
import fetch from 'cross-fetch';
import { IServerRenderProps } from './ServerRoutes';

export const renderResponsePolicy = async (props: Partial<IServerRenderProps<Record<string, string>>>):Promise<APIGatewayProxyResult> => {
  try {
    const fileUrl = `https://www.swell.life${props.event?.path}?webview`;
    const response = await fetch(fileUrl);
    if (!response.ok) {
      return {
        statusCode: 301,
        headers: { location: fileUrl },
        body: `<html><head><meta http-equiv="refresh" content="0; url=${fileUrl}" /></head><body><p><a href="${fileUrl}">Redirect</a></p></body></html>`,
      };
    }

    let body = await response.text();
    body = body.replace('<head>', '<head><base href="https://www.swell.life/">');

    return {
      statusCode: 200,
      headers: {
        'Content-Type': response.headers.get('Content-Type') ?? '',
      },
      body,
    };
  } catch (err) {
    return {
      statusCode: 200,
      body: JSON.stringify(props, null, 2),
    };
  }
};
