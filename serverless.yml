---
frameworkVersion: ^4.16.0
service: swellcast-site
useDotenv: true
configValidationMode: error
build:
  esbuild: false
custom:
  serverless-offline:
    httpsProtocol: "https" # this informs webpack proxy server which protocol to use. It is the folder name of the location of https cert.pem/key.pem
    noPrependStageInUrl: true
    httpPort: 3001
    lambdaPort: 3002
    host: 0.0.0.0
    watch:
      patterns:
        - src/**/*.ts
        - handlers/**/*.js
    chokidarOptions:
      usePolling: true # this forces polling
      interval: 1000 # poll every 1s
      ignoreInitial: true
      ignore:
        - node_modules/**
  webpack:
    webpackConfig: "./config/webpack.server.config.ts"
    includeModules: true
    packager: "yarn"
    # excludeFiles: src/view/test/**/*
  stage:
    DEPLOYMENT_BUCKET: build.stage.serverless
    PROVISIONED_CONCURRENCY: 0
    ENABLE_PROVISONED_CONCURRENCY: false
  prod:
    DEPLOYMENT_BUCKET: serverless.builds
    PROVISIONED_CONCURRENCY: 2
    ENABLE_PROVISONED_CONCURRENCY: true
  export-env:
    filename: .env.sls
    overwrite: true
    enableOffline: true
  contentCompression: 1024

provider:
  name: aws
  runtime: nodejs16.x
  region: us-west-2
  stage: ${opt:stage, 'none'}
  memorySize: 4096
  timeout: 30
  deploymentBucket:
    name: ${self:custom.${self:provider.stage}.DEPLOYMENT_BUCKET}
    blockPublicAccess: true
    serverSideEncryption: AES256
  tracing:
    apiGateway: false #SA-2367
    lambda: false #SA-2367
  environment:
    SERVERLESS_ACCESS_KEY: ${env:SERVERLESS_ACCESS_KEY, ''}
    BRANCH: ${env:BRANCH, ''}
    STAGE: ${env:STAGE, self:provider.stage, 'none'}
    ASSET_PATH: ${self:provider.environment.ASSET_URL}/${file(./package.json):name}/${file(./package.json):version}
    ASSET_URL: ${env:ASSET_URL, ssm:/${self:service}/${self:provider.stage}/ASSET_URL}
    CANONICAL_ALT_DOMAIN: ${env:CANONICAL_ALT_DOMAIN, ssm:/${self:service}/${self:provider.stage}/CANONICAL_ALT_DOMAIN}
    CANONICAL_DOMAIN: ${env:CANONICAL_DOMAIN, ssm:/${self:service}/${self:provider.stage}/CANONICAL_DOMAIN}
    GTM_ID: ${env:GTM_ID, ssm:/${self:service}/${self:provider.stage}/GTM_ID}
    SITE_DOMAIN: ${env:SITE_DOMAIN, ssm:/${self:service}/${self:provider.stage}/SITE_DOMAIN}
    SWELL_API: ${env:SWELL_API, ssm:/${self:service}/${self:provider.stage}/SWELL_API}
    CODEGEN_API: ${env:CODEGEN_API, ssm:/${self:service}/${self:provider.stage}/CODEGEN_API, env:SWELL_API, ssm:/${self:service}/${self:provider.stage}/SWELL_API}
    SWELL_DEVELOPERS_URL: ${env:SWELL_DEVELOPERS_URL, ssm:/${self:service}/${self:provider.stage}/SWELL_DEVELOPERS_URL}
    TRACKING_API: ${env:TRACKING_API, ssm:/${self:service}/${self:provider.stage}/TRACKING_API}
    TIME_BEFORE_LOGIN_POPUP: ${env:TIME_BEFORE_LOGIN_POPUP, ssm:/${self:service}/${self:provider.stage}/TIME_BEFORE_LOGIN_POPUP}
    TIME_BETWEEN_LOGIN_POPUP: ${env:TIME_BETWEEN_LOGIN_POPUP, ssm:/${self:service}/${self:provider.stage}/TIME_BETWEEN_LOGIN_POPUP}
    TIME_BEFORE_FOLLOW_POPUP: ${env:TIME_BEFORE_FOLLOW_POPUP, ssm:/${self:service}/${self:provider.stage}/TIME_BEFORE_FOLLOW_POPUP}

plugins:
  - serverless-export-env # needs to come first as per docs
  - serverless-webpack
  - serverless-offline
  - serverless-provisioned-concurrency-autoscaling

functions:
  serve:
    # Any web request regardless of path or method will be handled by a single Lambda function
    handler: handler.serve
    provisionedConcurrency: ${self:custom.${self:provider.stage}.PROVISIONED_CONCURRENCY}
    concurrencyAutoscaling: ${self:custom.${self:provider.stage}.ENABLE_PROVISONED_CONCURRENCY}
    events:
      - http:
          path: /
          method: any
          cors: true
      - http:
          path: /{any+}
          method: any
          cors: true
