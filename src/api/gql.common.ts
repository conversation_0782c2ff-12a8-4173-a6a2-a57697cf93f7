import { DefinedInitialDataOptions, <PERSON><PERSON><PERSON><PERSON>, UseQueryResult, useQuery } from '@tanstack/react-query';
import fetch from 'cross-fetch';
import { useAuth } from '../components/login/useAuth';
import { useSettings } from '../components/settings/useSettings';
import { Query } from '../generated/graphql';
import { Localize } from '../i18n/Localize';
import { IServerSettings, OpenAPIError } from '../models/models';
/*
I want to allow switching between prod and stage api
Set it in the query string with stage=prod or stage=stage or stage=default and then persist that setting in localStorage and cookie (for server response)
*/
export type APIType = 'stage' | 'prod' | 'preprod' | 'default';
export const validApiTypes: APIType[] = ['stage', 'prod', 'preprod', 'default'];

export interface FetchOptions extends Partial<IServerSettings> {
  useFragments?: boolean;
  stage?: APIType;
  abortController?: AbortController;
  token: string | null;
  apiUrl: string;
  signal?: AbortSignal;
}

function minifyGQL(gql: string): string {
  return gql
    .replace(/  +/g, ' ')
    .replace(/(\n|\t)/g, '')
    .trim();
}

export interface QueryErrors {
  message?: string;
  description?: string;
  errors?: {
    message: string;
    locations: { line: number; column: number }[];
    path: string[];
    extensions: {
      code: string;
      exception: {
        response: {
          message: string;
          description: string;
        };
        status: number;
        message: string;
        name: string;
        stacktrace: string[];
      };
    };
  }[];
}

type QueryData = { data: Query };

export type IQueryOptions<T = unknown> = Partial<DefinedInitialDataOptions<T, OpenAPIError, T, QueryKey>>;

export function useSwellAPI<T>(key: QueryKey, query: string, process = (d: QueryData): T => d as T, options: IQueryOptions<T> = {}): UseQueryResult<T, OpenAPIError> {
  const settings = useSettings();
  const auth = useAuth();
  const queryKey = [...key, settings.stage, settings.countryCode, auth.loggedIn];
  const enabled = options?.enabled ?? true;

  return useQuery({
    refetchOnMount: false,
    ...options,
    enabled,
    queryKey,
    queryFn: async () => {
      const token = await auth.getToken();
      const res = await fetchAPI(query, { token, apiUrl: settings.apiUrl });
      return process(res) as T;
    },
  });
}

export function useSwellFetch<R = QueryData | OpenAPIError>() {
  const settings = useSettings();
  const auth = useAuth();
  const fetch = async (query: string) => {
    const token = await auth.getToken();
    return (await fetchAPI(query, { token, apiUrl: settings.apiUrl })) as R;
  };
  return fetch;
}

export const isValidToken = (token?: string | null): token is string => typeof token === 'string' && token.includes('Bearer');

export const fetchAPI = async (query: string, options: FetchOptions) => {
  const headers: HeadersInit = { 'content-type': 'application/json' };

  if (isValidToken(options.token)) {
    headers.Authorization = options.token;
  }

  const res = await fetch(options.apiUrl, {
    method: 'POST',
    body: JSON.stringify({ query: minifyGQL(query) }),
    headers,
    signal: options.signal,
  });

  if (!res.ok) {
    return Promise.reject({ message: 'ERROR', description: Localize.GENERAL_ERROR });
  }

  const data = await res.json();

  if (data?.message === 'Failed to fetch file') {
    data.description = Localize.GENERAL_ERROR;
    return Promise.reject(data);
  }

  // sometimes an error is returned along with partially successful data
  const hasData = data?.data && typeof data?.data === 'object' && Object.keys(data.data).length > 0;

  if (!hasData && data?.errors?.length) {
    data.message = data?.errors?.[0]?.message ?? 'ERROR';
    data.description = data?.errors?.[0].extensions?.exception?.response?.description ?? Localize.GENERAL_ERROR;
    return Promise.reject(data);
  }
  return data;
};
