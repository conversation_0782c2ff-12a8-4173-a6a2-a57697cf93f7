// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
//
//
// fragments
//
//
import {
    ArticleType,
    AudioType,
    CategoryResponse,
    HomePageActionModel,
    HomePageActionParamsModel,
    HomePageHeaderModel,
    HomePageItemModel,
    HomePageSectionParamsModel,
    HomePageStyleModel,
    OpenApiAudioModel,
    OpenApiAuthorModel,
    OpenApiAuthorSwellcastModel,
    OpenApiCategoryModel,
    OpenApiHomePageSectionResponse,
    OpenApiHomePageSwellResponse,
    OpenApiLatestVideoResponse,
    OpenApiOwnerModel,
    OpenApiPopularInfoResponse,
    OpenApiProfileWithSectionsResponse,
    OpenApiPromoWithSectionsResponse,
    OpenApiPromptResponse,
    OpenApiPromptWithSectionsResponse,
    OpenApiReactionResponse,
    OpenApiReplyResponse,
    OpenApiSearchPageResponse,
    OpenApiSearchWithSectionsResponse,
    OpenApiSeeAllSectionResponse,
    OpenApiSeeAllWithSectionResponse,
    OpenApiStationResponse,
    OpenApiStationSwellsResponse,
    OpenApiStationWithSectionsResponse,
    OpenApiSwellResponse,
    OpenApiSwellSwellcastModel,
    OpenApiSwellcastInfoResponse,
    OpenApiSwellcastOwnerModel,
    OpenApiSwellcastResponse,
    OpenApiSwellcastSectionsResponse,
    OpenApiSwellcastSwellFacesResponse,
    OpenApiSwellcastSwellResponse,
    OpenApiSwellcastWithSwellResponse,
    PodcastDataResponse,
    StationInfoResponse,
} from '../generated/graphql';

type GQLMap<T> = Array<keyof T | { [K in keyof Partial<T>]: GQLMap<T[K] extends Array<infer U> ? U : T[K]> }>;

const homePageStyleModel: GQLMap<HomePageStyleModel> = [
  'alignment', //
  'backgroundColorHex',
  'backgroundImageLayerColorHex',
  'backgroundImageUrl',
  'borderColorHex',
  'descriptionFontSize',
  'descriptionFontWeight',
  'fontColorHex',
  'headingColorHex',
  'headingFontSize',
  'headingFontWeight',
  'titleFontSize',
  'titleFontWeight',
  'width',
];

const homePageActionParamsModel: GQLMap<HomePageActionParamsModel> = [
  'id', //
  'canonicalId',
  'searchKey',
  'tag',
  'doReply',
  'url',
  'title',
  'description',
  'imageUrl',
  'categoryId',
  'channels',
  'alias',
];

const homePageActionModel: GQLMap<HomePageActionModel> = [
  'type', //
  'label',
  'route',
  { style: homePageStyleModel },
  { params: homePageActionParamsModel },
  'referrerId',
];

const homePageHeaderModel: GQLMap<HomePageHeaderModel> = [
  'text', //
  'description',
  { actions: homePageActionModel },
  { style: homePageStyleModel },
  'heading',
];

const promosForWeb: GQLMap<HomePageItemModel> = [
  'type', //
  {
    headers: [{ items: [{ header: homePageHeaderModel }] }],
  },
];

const articles: GQLMap<ArticleType> = [
  'link', //
  'title',
  'image',
  'sitename',
];

const categoriesResponse: GQLMap<CategoryResponse> = [
  'id', //
  'name',
  'image',
  'ogImage',
  'description',
  'boost',
];

const categories: GQLMap<OpenApiCategoryModel> = [
  'id', //
  'name',
  'image',
  'boost',
];

const reactions: GQLMap<OpenApiReactionResponse> = [
  'reaction', //
  'count',
  'pressState',
];

const audio: GQLMap<AudioType> = [
  'url', //
  'name',
  'duration',
  'wave',
];

const audioModel: GQLMap<OpenApiAudioModel> = [
  'url', //
  'name',
  'duration',
  'wave',
];

const authorSwellcast: GQLMap<OpenApiAuthorSwellcastModel> = [
  'id', //
  'name',
  'image',
  'description',
  'podcastSubType',
  'podcastType',
];

const author: GQLMap<OpenApiAuthorModel> = [
  'id', //
  'firstName',
  'lastName',
  'image',
  'alias',
  'followingState',
  { swellcast: authorSwellcast },
];

const replies: GQLMap<OpenApiReplyResponse> = [
  'id', //
  'message',
  { author },
  'keywords',
  { audio },
  'createdOn',
  { reactions },
  'snippet',
  { articles },
  'isParent',
  'parentId',
  'promptId',
];

const owner: GQLMap<OpenApiOwnerModel> = [
  'id', //
  'image',
  'alias',
  'firstName',
  'lastName',
  'status',
];

const swellcastOwner: GQLMap<OpenApiSwellcastOwnerModel> = [
  'id', //
  'image',
  'alias',
  'firstName',
  'lastName',
];

const swellcast: GQLMap<OpenApiSwellSwellcastModel> = [
  'id', //
  'name',
  'description',
  'image',
  'ogImage',
  { owner: swellcastOwner },
];

const faces: GQLMap<OpenApiSwellcastSwellFacesResponse> = [
  'url', //
  'alias',
];

const sectionSwellSwellcast: GQLMap<OpenApiSwellcastInfoResponse> = [
  'description', //
  'id',
  'image',
  'name',
  'capabilities',
  'podcastSubType',
  'podcastType',
];

const swells: GQLMap<OpenApiSwellcastSwellResponse> = [
  'id', //
  'title',
  'description',
  { audio: audioModel },
  { author },
  { reactions },
  'createdOn',
  'lastRepliedOn',
  'keywords',
  'canonicalId',
  { articles },
  { categories }, // OpenApiCategoryModel
  'pinState',
  'hidden',
  'repliesCount',
  { faces },
  { swellcastOwner },
  { swellcast: sectionSwellSwellcast },
  'subscriptionOnly',
  'snippet',
];

const actionParam: GQLMap<HomePageActionParamsModel> = [
  'id', //
  'canonicalId',
  'searchKey',
  'tag',
  'doReply',
  'url',
  'title',
  'description',
  'imageUrl',
  'categoryId',
  'channels',
  'alias',
  'type',
  'isPanel',
  'isPremium',
  'languageCode',
  'promptId',
];
//
//
// commands
//
//
const loadSwellById: GQLMap<OpenApiSwellResponse> = [
  'snippet', //
  'id',
  'title',
  'description',
  'ogImage',
  'swellCount',
  'keywords',
  'subscriptionOnly',
  { categories: categoriesResponse },
  'languages',
  { audio }, // AudioType
  { author },
  { replies },
  { reactions },
  { articles },
  { swellcast },
  'createdOn',
  'lastRepliedOn',
  'canonicalId',
  'accessUntil',
  'unlockBtnTxt',
  'configuredPriceTxt',
  'isQAndA',
  'canReply',
  'isPanel',
  { faces },
  'promptId',
  'promptSlug',
];

const getSwellcast: GQLMap<OpenApiSwellcastResponse> = [
  'id', //
  'name',
  'description',
  'image',
  'ogImage',
  'createdOn',
  'swellCount',
  { owner }, // OpenApiOwnerModel
  { swells },
  'followersCount',
  'subscriptionState',
  'followingState',
  'podcastSubType',
  'podcastType',
];

const getSwellcastMarketing: GQLMap<OpenApiSwellcastResponse> = [
  'id', //
  { owner: ['alias', 'image'] },
  'name',
  'description',
  'image',
  'ogImage',
  { swells: ['canonicalId', 'title', { author: ['alias'] }] },
];

/*
const useMarketingSwellcast = () => {
  const gql = `{
    getSwellcast(limit: 5, offset: 0, alias: "arish", hashtagFilter: "") {
        id
        owner {
        id
        alias
        image
        }
        subscriptionState
        followingState
        canSubscribe
        name
        image
        swells {
        id
        canonicalId
        title
        }
    }
    }`;
};
*/

const getSwellcastOwner: GQLMap<OpenApiSwellcastResponse> = [{ owner }];

const getSwellcastSubscriptionState: GQLMap<OpenApiSwellcastResponse> = [
  'id', //
  { owner: ['id', 'alias'] },
  'subscriptionState',
  'followingState',
  'canSubscribe',
  'name',
];

const getSwellSubscriptionState: GQLMap<OpenApiSwellResponse> = [
  'unlockBtnTxt', //
  'accessUntil',
  'configuredPriceTxt',
];

const getSearchPage: GQLMap<OpenApiSearchPageResponse> = [
  { promosForWeb }, //
  { trendingSwells: swells },
  { featured: swells },
  { swells },
  { postsFromWorldOver: swells },
  { topSpeakers: author },
  { popularStations: categoriesResponse },
  { premiumSwellcasts: getSwellcast },
  { worldwidePremiumSwellcasts: getSwellcast },
];

const swellcastWithSwellResponse: GQLMap<OpenApiSwellcastWithSwellResponse> = [
  'blockingState', //
  'cohostState',
  'cohostingState',
  'cohostsCount',
  'createdOn',
  'description',
  'followersCount',
  'followingState',
  'id',
  'image',
  'name',
  'onlyAdminCanPost',
  { owner: swellcastOwner },
  'subscribersCount',
  'subscriptionState',
  'swellCount',
  { swells },
  'type',
  'unheardCount',
];

const listSwellSuggestions: GQLMap<OpenApiSwellcastSectionsResponse> = [
  {
    sections: [
      'label', //
      'name',
      { param: actionParam },
      'sectionType',
      { swellcast: swellcastWithSwellResponse },
      'type',
    ],
  },
  'swellId',
];

const stationInfo: GQLMap<StationInfoResponse> = [
  'id', //
  'type',
  'name',
  'image',
  'ogImage',
  'description',
];

const getStation: GQLMap<OpenApiStationResponse> = [
  'id', //
  'type',
  'name',
  'image',
  'ogImage',
  'description',
  { firstTimePosts: swells },
  { trendingSwells: swells },
  { topAuthors: <AUTHORS>
  { posts: swells },
  { postsFromWorldOver: swells },
  { newUsers: author },
  { featured: swells },
];

const listStationSwells: GQLMap<OpenApiStationSwellsResponse> = [
  'id', //
  'type',
  'name',
  'image',
  'description',
  { swells },
  'hasMore',
];

const search: GQLMap<OpenApiSwellcastResponse> = [
  'id', //
  'name',
  'description',
  'image',
  'ogImage',
  'createdOn',
  'swellCount',
  { owner },
  { swells },
];

const getLatestVideos: GQLMap<OpenApiLatestVideoResponse> = [
  'videoUrl', //
  'siteUrl',
  'thumbnail',
  'swellId',
  'swellCanonicalId',
  'replyId',
  'createdOn',
  'alias',
];

const pageSectionParamsModel: GQLMap<HomePageSectionParamsModel> = [
  'id', //
  'type',
  'name',
  'seeAllUrl',
];

// same shit as swells = OpenApiSwellcastSwellResponse
const sectionSwell: GQLMap<OpenApiHomePageSwellResponse> = [
  'id', //
  'title',
  'description',
  { audio: audioModel },
  { author },
  { reactions },
  'createdOn',
  'lastRepliedOn',
  'keywords',
  'canonicalId',
  { articles },
  { categories },
  'pinState',
  'hidden',
  'repliesCount',
  { faces },
  { swellcastOwner },
  'subscriptionOnly',
  'snippet',
  'isQAndA',
  'subscriptionOnly',
  { swellcast: sectionSwellSwellcast },
  { swellcastOwner },
  'homePageSectionType',
  { cardInfo: ['message', 'cardStyle'] },
  'promptId',
  'promptSlug',
];

const sectionPrompt: GQLMap<OpenApiPromptResponse> = [
  'id',
  'createdOn',
  'promptTitle',
  'promptText',
  'promptImageUrl',
  'promptLink',
  'altText', //
  { swellcast: ['id', { owner: ['alias'] }] },
  { credit: author },
  'postCount',
  'swellId',
  'ogImage',
  'slug',
  //   { swellcast: getSwellcast },
];

// NOTE:  there are 2 identical types
const sections: GQLMap<OpenApiHomePageSectionResponse & OpenApiSeeAllSectionResponse> = [
  'id',
  'label',
  'type',
  'sectionDataType',
  'sectionType',
  { swells: sectionSwell },
  { stations: stationInfo },
  'emptyMessage',
  { swellcasts: getSwellcast },
  { prompts: sectionPrompt },
  'hashtags',
  { images: ['image', 'label', 'url'] },
  { promos: homePageHeaderModel },
  { sectionParams: pageSectionParamsModel },
  { authors: author }, //
];

const getSeeAll: GQLMap<OpenApiSeeAllWithSectionResponse> = [
  'hasMore', //
  'lastId',
  { sections },
  // unique properties
  'title',
  'description',
  'image',
];

const getSearchResultsPage: GQLMap<OpenApiSearchWithSectionsResponse> = [
  'hasMore', //
  'lastSectionId',
  { sections },
];

const promoWithSections: GQLMap<OpenApiPromoWithSectionsResponse> = [
  'hasMore', //
  'lastSectionId',
  { sections },
  // unique properties
  { promos: promosForWeb },
];

const getStationPage: GQLMap<OpenApiStationWithSectionsResponse> = [
  'id', //
  'type',
  'name',
  'image',
  'ogImage',
  'description',
  { promos: promosForWeb },
  { sections },
  'lastSectionId',
  'hasMore',
];

const podcastData: GQLMap<PodcastDataResponse> = [
  'coverImage', //
  'description',
  'rssVersion',
  'filterByTag',
  'omitDefaultSwell',
  'includeReplies',
  { apps: ['id', 'imageUrl', 'name', 'url'] },
];

const getProfilePage: GQLMap<OpenApiProfileWithSectionsResponse> = [
  'id', //
  'podcastType',
  'podcastSubType',
  { podcastData },
  'name',
  'description',
  'image',
  'ogImage',
  'createdOn',
  'swellCount',
  { owner },
  //   { swells },
  'followersCount',
  'subscriptionState',
  'canSubscribe',
  'followingState',
  'canPost',
  'capabilities',
  'preferredStations',
  { sections },
  'lastSectionId',
  'hasMore',
];
//
//
// RSS commands
//
//
const RSS_getSwellcast: GQLMap<OpenApiSwellcastResponse> = [
  'id', //
  'name',
  'description',
  'ogImage',
  { owner },
  { swells: ['canonicalId'] },
];

const RSS_loadSwellById: GQLMap<OpenApiSwellResponse> = [
  'id', //
  'title',
  'description',
  'ogImage',
  'languages',
  { audio: ['url', 'duration'] },
  { replies: ['id', { audio: ['url', 'duration'] }] },
  'createdOn',
  'lastRepliedOn',
  'canonicalId',
];

const getPromptPage: GQLMap<OpenApiPromptWithSectionsResponse> = [
  'id', //
  'createdOn',
  'promptTitle',
  'promptText',
  'promptImageUrl',
  'ogImage',
  'promptLink',
  'isEditorsPick',
  'altText',
  { swellcast: getSwellcast },
  { credit: author },
  'postCount',
  'swellId',
  { sections },
  'lastSectionId',
  'hasMore',
  'allowedResponseTypes',
  'slug',
];

const popularSpeakers: GQLMap<OpenAPIPopularAuthorModel> = [
  'id', //
  'firstName',
  'lastName',
  'image',
  { audio },
  'alias',
  'badge',
  'swellcastId',
  'swellcastDescription',
  'swellcastImage',
  'followingCount',
];

const popularStations: GQLMap<OpenAPICategoryModel> = [
  'id', //
  'image',
  'name',
];

const getPopularInfo: GQLMap<OpenApiPopularInfoResponse> = [
  'popularTopics', //
  { popularSpeakers },
  { popularStations },
];
//
//
// indicate which fields are flag type for renderGqlQuery
//
//
const queryFlags = [
  'flags', //
  'type',
];

const queryEnums = [
  'sectionType', //
];
//
//
// commands
//
//
const gglCommands = {
  getPopularInfo: { cmd: 'getPopularInfo', body: getPopularInfo },
  getSwellcastMarketing: { cmd: 'getSwellcast', body: getSwellcastMarketing },
  getPromptPage: { cmd: 'getPromptPage', body: getPromptPage },
  getStationPage: { cmd: 'getStationPage', body: getStationPage },
  getHomePageV2: { cmd: 'getHomePageV2', body: promoWithSections },
  getCommunitiesPage: { cmd: 'getCommunitiesPage', body: promoWithSections },
  getBrowsePage: { cmd: 'getBrowsePage', body: promoWithSections },
  getProfilePage: { cmd: 'getProfilePage', body: getProfilePage },
  getSeeAll: { cmd: 'getSeeAll', body: getSeeAll },
  getSearchResultsPage: { cmd: 'getSearchResultsPage', body: getSearchResultsPage },
  listSwellSuggestions: { cmd: 'listSwellSuggestions', body: listSwellSuggestions },
  loadSwellById: { cmd: 'loadSwellById', body: loadSwellById }, //
  getSwellcast: { cmd: 'getSwellcast', body: getSwellcast },
  getSwellcastOwner: { cmd: 'getSwellcast', body: getSwellcastOwner },
  getSwellcastSubscriptionState: { cmd: 'getSwellcast', body: getSwellcastSubscriptionState },
  getSwellSubscriptionState: { key: 'swell', cmd: 'loadSwellById', body: getSwellSubscriptionState },
  getSearchPage: { cmd: 'getSearchPage', body: getSearchPage },
  getStation: { cmd: 'getStation', body: getStation },
  listCategory: { cmd: 'listCategory', body: categories },
  listStationSwells: { cmd: 'listStationSwells', body: listStationSwells },
  getLatestVideos: { cmd: 'getLatestVideos', body: getLatestVideos },
  search: { cmd: 'search', body: search },
  RSS_loadSwellById: { cmd: 'loadSwellById', body: RSS_loadSwellById },
  RSS_getSwellcast: { cmd: 'getSwellcast', body: RSS_getSwellcast },
};
//
//
// helper functions
//
//
function isObject(o: unknown) {
  return typeof o === 'object' && !Array.isArray(o) && o !== null;
}

const renderGqlBody = (e: unknown): string => {
  if (isObject(e)) {
    const k = Object.keys(e)[0];
    return `${k} { ${e[k].map(renderGqlBody).join(' ')} }`;
  }
  return e as string;
};

function renderQueryFlags(flags: string | string[]) {
  if (Array.isArray(flags)) {
    return '[' + flags.join(',') + ']';
  }
  return flags;
}

function renderGqlQuery<Q>(query: Q) {
  if (query && Object.keys(query).length > 0) {
    return (
      '(' +
      Object.keys(query)
        .reduce((a, k) => {
          if (query[k] !== undefined) {
            const val = queryFlags.includes(k) ? renderQueryFlags(query[k]) : queryEnums.includes(k) ? query[k] : JSON.stringify(query[k]);
            return [...a, val ? `${k}:${val}` : ''];
          }
          return a;
        }, [])
        .join(' ') +
      ')'
    );
  }
  return '';
}

const cmdKey = '__CMD__';

export function renderGql<Q = Record<string, string | number>>(cmd: keyof typeof gglCommands, query?: Q) {
  let res = renderGqlBody({ [cmdKey]: gglCommands[cmd].body });
  res = res.replace(cmdKey, `${gglCommands[cmd].cmd}${renderGqlQuery<Q>(query)}`);
  return `{${res}}`;
}

// Experimental request combining
// I think requests will cache better if I don't combine requests
// export function renderGqlFragment(name: string, body: (string | Record<string, unknown>)[]) {
//   let res = renderGqlBody({ body });
//   res = res.replace('body', ``);
//   return `fragment ${name} ${res}`;
// }

// const stripCurlys = (s: string) => s.replace(/^\{/, '').replace(/\}$/, '');
/*
Example:

renderMultiQuery([
    { key: 'swellcast', cmd: 'getSwellcastSubscriptionState', query: { limit: 0, offset: 0, alias: params.listId, hashtagFilter: null } },
    { key: 'swell', cmd: 'getSwellSubscriptionState', query: { id: params.canonicalId } },
]),

*/
// export const renderMultiQuery = (queries: { key: string; cmd: keyof typeof gglCommands; query?: Record<string, string | number> }[]) => {
//   const body = queries.map((q) => `${q.key}: ${stripCurlys(renderGql(q.cmd, q.query))}`);
//   return `{${body}}`;
// };
