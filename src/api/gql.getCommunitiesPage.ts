import { DefinedInitialDataInfiniteOptions, InfiniteData, QueryKey, keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetCommunitiesPageArgs } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'getCommunitiesPage';
type QueryArgs = QueryGetCommunitiesPageArgs;
type PageArgs = Pick<QueryArgs, 'sinceSectionId'>;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryData = InfiniteData<HookResponse, PageArgs>;
type QueryOptions = Partial<DefinedInitialDataInfiniteOptions<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>>;

const defaultParams: QueryArgs = {};

export function useCommunitiesPage(params: QueryArgs = {}, options: QueryOptions = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  params = {
    ...defaultParams,
    countryCode: settings.countryCode,
    ...params,
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, InfiniteData<HookResponse, PageArgs>, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, params],
    queryFn: async (props) => {
      const query = renderGql<QueryArgs>(ApiKey, props.pageParam);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: { sinceSectionId: '' },
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore && lastPage?.lastSectionId) {
        return { sinceSectionId: lastPage.lastSectionId };
      }
      return undefined;
    },
    placeholderData: keepPreviousData,
    enabled: !!(options?.enabled ?? true),
    ...options,
  });
}
