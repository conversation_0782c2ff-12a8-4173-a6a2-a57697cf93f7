import { InfiniteData, QueryKey, keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { useAuth } from '../components/login/useAuth';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetHomePageV2Args } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { removeEmptyProps } from '../utils/Utils';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'getHomePageV2';
type QueryArgs = QueryGetHomePageV2Args;
type PageArgs = QueryArgs;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryData = InfiniteData<HookResponse, PageArgs>;

const defaultParams: QueryArgs = {
  sinceSectionId: '',
  countryCode: '',
};

export function useHomePage(params: QueryArgs = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();
  const auth = useAuth(); // TODO: this forces refresh on login - but all APIs should have this feature

  params = {
    ...defaultParams,
    countryCode: settings.countryCode,
    ...params,
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, auth.loggedIn, params],
    queryFn: async (props) => {
      const cleanParams = removeEmptyProps(props.pageParam);
      const query = renderGql<QueryArgs>(ApiKey, cleanParams);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: params,
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore && lastPage?.lastSectionId) {
        return { ...params, sinceSectionId: lastPage.lastSectionId };
      }
      return undefined;
    },
    placeholderData: keepPreviousData,
    enabled: true,
  });
}
