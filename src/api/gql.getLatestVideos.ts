import { useSettings } from '../components/settings/useSettings';
import { OpenApiLatestVideoResponse, QueryGetLatestVideosArgs } from '../generated/graphql';
import { useSwellAPI } from './gql.common';
import { renderGql } from './gql.fragments';

export function useLatestVideos() {
  const settings = useSettings();

  return useSwellAPI<OpenApiLatestVideoResponse[]>(
    ['getLatestVideos', settings.stage], //
    renderGql<QueryGetLatestVideosArgs>('getLatestVideos', { countryCode: settings.countryCode }),
    (raw) => raw.data.getLatestVideos,
    { enabled: true, refetchOnMount: true },
  );
}
