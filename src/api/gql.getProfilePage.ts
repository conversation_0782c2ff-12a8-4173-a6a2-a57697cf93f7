import { DefinedInitialDataInfiniteOptions, InfiniteData, QueryKey, UseQueryResult, useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetProfilePageArgs } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { removeEmptyProps } from '../utils/Utils';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'getProfilePage';
type QueryArgs = QueryGetProfilePageArgs;
type PageArgs = QueryArgs; //Pick<QueryArgs, 'sinceSectionId'>;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryData = InfiniteData<HookResponse, PageArgs>;
type QueryOptions = Partial<DefinedInitialDataInfiniteOptions<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>>;

const defaultParams: QueryArgs = {
  sinceSectionId: '',
  countryCode: '',
  hashtagFilter: '',
  alias: '',
  id: '',
};

export function useProfilePage(params: QueryArgs, options: QueryOptions = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  params = {
    ...defaultParams,
    ...removeEmptyProps(params),
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, params.alias, params],
    queryFn: async (props) => {
      const cleanParams = removeEmptyProps(props.pageParam);
      const query = renderGql<QueryArgs>(ApiKey, cleanParams);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: params,
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore && lastPage?.lastSectionId) {
        return { ...params, sinceSectionId: lastPage.lastSectionId };
      }
      return undefined;
    },
    ...options,
    enabled: !!(options?.enabled ?? true) && !!params?.alias,
  });
}

export function useProfile(params: QueryArgs) {
  // check for cached data
  const queryClient = useQueryClient();
  const cachedData = queryClient.getQueriesData<QueryData>({ queryKey: [ApiKey, params.alias] });
  const dataFromCache = cachedData?.[0]?.[1]?.pages?.[0];
  const isCachedDataAvailable = !!dataFromCache;

  // conditionally get fresh data
  const res = useProfilePage({ ...params, alias: params.alias?.toLowerCase() }, { enabled: !isCachedDataAvailable });
  const freshData = res.data?.pages?.[0];

  // resolve data
  const data = isCachedDataAvailable ? dataFromCache : freshData;

  return { ...res, isSuccess: isCachedDataAvailable ? true : res.isSuccess, data } as unknown as UseQueryResult<HookResponse, OpenAPIError>;
}
