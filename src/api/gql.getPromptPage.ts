import { DefinedInitialDataInfiniteOptions, InfiniteData, QueryKey, keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetPromptPageArgs } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { removeEmptyProps } from '../utils/Utils';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'getPromptPage';
type QueryArgs = QueryGetPromptPageArgs;
type PageArgs = Pick<QueryArgs, 'sinceSectionId' | 'id'>;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryOptions = Partial<DefinedInitialDataInfiniteOptions<HookResponse, OpenAPIError, InfiniteData<HookResponse, PageArgs>, QueryKey, PageArgs>>;

const defaultParams: QueryArgs = {
  id: '',
  sinceSectionId: '',
  countryCode: '',
};

export function usePromptPage(params: QueryArgs, options: QueryOptions = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  params = {
    ...defaultParams,
    countryCode: settings.countryCode,
    ...params,
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, InfiniteData<HookResponse, PageArgs>, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, params],
    queryFn: async (props) => {
      const cleanParams = removeEmptyProps(props.pageParam);
      const query = renderGql<QueryArgs>(ApiKey, cleanParams as QueryArgs);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: params,
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore && lastPage?.lastSectionId) {
        return { ...params, sinceSectionId: lastPage.lastSectionId };
      }
      return undefined;
    },
    placeholderData: keepPreviousData,
    ...options,
    enabled: !!(options?.enabled ?? true) && !!params?.id,
  });
}
