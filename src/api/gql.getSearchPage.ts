import { keepPreviousData } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { OpenApiSearchPageResponse, OpenApiSwellcastSwellResponse, QueryGetSearchPageArgs } from '../generated/graphql';
import { useSwellAPI } from './gql.common';
import { renderGql } from './gql.fragments';

const sortByDate = (a: OpenApiSwellcastSwellResponse, b: OpenApiSwellcastSwellResponse) => (a.createdOn! > b.createdOn! ? -1 : a.createdOn! < b.createdOn! ? 1 : 0);

function processResponse(input: OpenApiSearchPageResponse) {
  const output = { ...input };
  output.swells = output?.swells?.sort(sortByDate) ?? [];
  output.postsFromWorldOver = output?.postsFromWorldOver?.sort(sortByDate) ?? [];
  return output;
}

export function useSearchPage() {
  const settings = useSettings();
  return useSwellAPI<OpenApiSearchPageResponse>(
    ['getSearchPage', settings.stage], //
    renderGql<QueryGetSearchPageArgs>('getSearchPage', { countryCode: settings.countryCode }),
    (raw) => processResponse(raw.data.getSearchPage),
    { placeholderData: keepPreviousData },
  );
}
