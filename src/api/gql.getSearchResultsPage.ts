import { DefinedInitialDataInfiniteOptions, InfiniteData, QueryKey, useInfiniteQuery } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetSearchResultsPageArgs } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { removeEmptyProps } from '../utils/Utils';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'getSearchResultsPage';
type QueryArgs = QueryGetSearchResultsPageArgs;
type PageArgs = Pick<QueryArgs, 'sinceSectionId' | 'searchTerm'>;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryData = InfiniteData<HookResponse, PageArgs>;
type QueryOptions = Partial<DefinedInitialDataInfiniteOptions<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>>;

const defaultParams: QueryArgs = {
  sinceSectionId: '',
  countryCode: '',
  searchTerm: '',
};

export function useSearchResultsPage(params: QueryArgs, options: QueryOptions = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();
  params = {
    ...defaultParams,
    countryCode: settings.countryCode,
    ...params,
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, InfiniteData<HookResponse, PageArgs>, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, params.searchTerm, params.countryCode, params.sinceSectionId],
    queryFn: async (props) => {
      const cleanParams = removeEmptyProps(props.pageParam);
      const query = renderGql<QueryArgs>(ApiKey, cleanParams as QueryArgs);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: params,
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore && lastPage?.lastSectionId) {
        return { searchTerm: params.searchTerm, sinceSectionId: lastPage.lastSectionId };
      }
      return undefined;
    },
    ...options,
    enabled: !!(options?.enabled ?? true) && !!params?.searchTerm && params?.searchTerm !== 'undefined' && params?.searchTerm !== 'null',
  });
}
