import { DefinedInitialDataInfiniteOptions, InfiniteData, QueryKey, keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetSeeAllArgs, SeeAllSectionType } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';
import { SWELLCAST_DEFAULT_LIMIT } from './gql.getSwellcast';

const ApiKey = 'getSeeAll';
type QueryArgs = QueryGetSeeAllArgs;
type PageArgs = Pick<QueryArgs, 'sinceId' | 'sectionType' | 'params' | 'limit'>;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryOptions = Partial<DefinedInitialDataInfiniteOptions<HookResponse, OpenAPIError, InfiniteData<HookResponse, PageArgs>, QueryKey, PageArgs>>;

const defaultParams: QueryArgs = {
  limit: SWELLCAST_DEFAULT_LIMIT,
  sinceId: '',
  sectionType: SeeAllSectionType.BrowseSwells,
  params: '',
};

export function useSeeAll(params: QueryArgs, options: QueryOptions = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  params = {
    ...defaultParams,
    ...params,
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, InfiniteData<HookResponse, PageArgs>, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, params],
    queryFn: async (props) => {
      const query = renderGql<QueryArgs>(ApiKey, { ...params, ...props.pageParam });
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: { ...defaultParams, ...params },
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore && lastPage?.lastId) {
        return { ...params, sinceId: lastPage.lastId };
      }
      return undefined;
    },
    placeholderData: keepPreviousData,
    ...options,
    enabled: !!(options?.enabled ?? true) && !!params?.sectionType,
  });
}
