import { DefinedInitialDataInfiniteOptions, InfiniteData, QueryKey, UseQueryResult, keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetStationPageArgs, StationType } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'getStationPage';
type QueryArgs = Partial<QueryGetStationPageArgs>;
type PageArgs = QueryArgs;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryData = InfiniteData<HookResponse, PageArgs>;
type QueryOptions = Partial<DefinedInitialDataInfiniteOptions<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>>;

const defaultParams: QueryArgs = { id: '', type: StationType.Category, countryCode: 'US' };

export function useStationPage(params: QueryArgs, options: QueryOptions = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  params = {
    ...defaultParams,
    countryCode: settings.countryCode,
    ...params,
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, params],
    queryFn: async (props) => {
      const query = renderGql<QueryArgs>(ApiKey, props.pageParam);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: { ...defaultParams, ...params },
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore && lastPage?.lastSectionId) {
        return { ...params, sinceSectionId: lastPage.lastSectionId };
      }
      return undefined;
    },
    placeholderData: keepPreviousData,
    enabled: 'id' in params,
    ...options,
  });
}

// convenience function when we need just the station data
export function useStationData(params: QueryArgs, options: QueryOptions = {}) {
  const res = useStationPage(params, options);
  const data = res.data?.pages?.[0];
  return { ...res, data } as unknown as UseQueryResult<HookResponse>;
}
