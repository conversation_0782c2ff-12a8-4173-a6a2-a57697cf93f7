import { DefinedInitialDataInfiniteOptions, InfiniteData, QueryKey, useInfiniteQuery } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { OpenApiHomePageSwellResponse, OpenApiSwellcastResponse, OpenApiSwellcastSwellResponse, Query, QueryGetSwellcastArgs } from '../generated/graphql';
import { MasterRef, OpenAPIError } from '../models/models';
import { removeEmptyProps } from '../utils/Utils';
import { QueryErrors, useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';
// TODO: move these
export const SWELLCAST_DEFAULT_LIMIT = 24;
// TODO: migrate away from enhanced types
export type OpenApiSwellcastResponseEnhanced = Omit<OpenApiSwellcastResponse, 'swells'> & {
  masterRef: MasterRef;
  swells: OpenApiSwellcastSwellResponseEnhanced[];
} & QueryErrors;

export type OpenApiSwellcastSwellResponseEnhanced = OpenApiSwellcastSwellResponse & Partial<Pick<OpenApiHomePageSwellResponse, 'homePageSectionType' | 'cardInfo'>> & { masterRef: MasterRef; isReply: boolean; isPinned: boolean };

const ApiKey = 'getSwellcast';
type QueryArgs = QueryGetSwellcastArgs;
type PageArgs = Pick<QueryArgs, 'limit' | 'offset'>; //Pick<QueryArgs, 'sinceSectionId'>;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];
type QueryData = InfiniteData<HookResponse, PageArgs>;
type QueryOptions = Partial<DefinedInitialDataInfiniteOptions<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>>;

const defaultParams: QueryArgs = {
  limit: SWELLCAST_DEFAULT_LIMIT,
  offset: 0,
};

export function useSwellcast(_params: Partial<QueryArgs>, options: QueryOptions = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  const params = {
    ...defaultParams,
    ...removeEmptyProps(_params),
  };

  return useInfiniteQuery<HookResponse, OpenAPIError, QueryData, QueryKey, PageArgs>({
    queryKey: [ApiKey, settings.stage, params.alias, params],
    queryFn: async (props) => {
      const query = renderGql<QueryArgs>(ApiKey, props.pageParam);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    initialPageParam: params,
    getNextPageParam: (lastPage) => {
      if (lastPage?.swells?.length ?? 0 > 0) {
        return { ...params, offset: params.offset + params.limit } as QueryArgs;
      }
      return undefined;
    },
    ...options,
    enabled: !!(options?.enabled ?? true) && !!params?.alias,
  });
}
