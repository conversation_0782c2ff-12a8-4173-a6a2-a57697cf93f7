import { UseQueryOptions, keepPreviousData, useQuery } from '@tanstack/react-query';
import { useSettings } from '../components/settings/useSettings';
import { Query, QueryGetSwellcastArgs } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { removeEmptyProps } from '../utils/Utils';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'getSwellcast';
type QueryArgs = QueryGetSwellcastArgs;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = FetchResponse['data'][typeof ApiKey];

const defaultParams: QueryArgs = {
  limit: 5,
  offset: 0,
};

export function useSwellcastMarketing(params: Partial<QueryArgs>, options: Partial<UseQueryOptions> = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  params = {
    ...defaultParams,
    ...params,
  };

  return useQuery<HookResponse, OpenAPIError>({
    queryKey: [ApiKey, settings.stage, 'marketing', params],
    queryFn: async () => {
      const query = renderGql<QueryArgs>('getSwellcastMarketing', removeEmptyProps(params) as QueryArgs);
      const raw = (await fetchSwell(query)) as FetchResponse;
      return raw.data[ApiKey];
    },
    placeholderData: keepPreviousData,
    enabled: !!(options?.enabled ?? true) && !!params?.alias,
  });
}
