import { OpenApiCategoryModel } from '../generated/graphql';
import { useSwellAPI } from './gql.common';
import { renderGql } from './gql.fragments';

function processResponse(res: OpenApiCategoryModel[]): OpenApiCategoryModel[] {
  // remove internal stations
  const output = res.filter((station) => station.id.indexOf('T-Internal') !== 0 && station.id.indexOf('T-EditorsPicks') !== 0);
  return output;
}

export function useCategory() {
  return useSwellAPI<OpenApiCategoryModel[]>(
    ['listCategory'], //
    renderGql('listCategory'),
    (raw) => processResponse(raw.data.listCategory!),
    { enabled: true },
  );
}
