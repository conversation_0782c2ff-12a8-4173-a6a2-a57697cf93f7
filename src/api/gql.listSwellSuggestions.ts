import { UndefinedInitialDataOptions, keepPreviousData, useQuery } from '@tanstack/react-query';
import { NormalSection } from '../components/sections/InfiniteSectionsView';
import { EMPTY_SECTION } from '../components/sections/sections-models';
import { useSettings } from '../components/settings/useSettings';
import { HomePageRenderType, HomePageSectionDataType, OpenApiHomePageSwellResponse, Query, QueryListSwellSuggestionsArgs, SwellcastSectionType } from '../generated/graphql';
import { OpenAPIError } from '../models/models';
import { useSwellFetch } from './gql.common';
import { renderGql } from './gql.fragments';

const ApiKey = 'listSwellSuggestions';
type QueryArgs = QueryListSwellSuggestionsArgs;
type FetchResponse = { data: Pick<Query, typeof ApiKey> };
type HookResponse = { sections: NormalSection[] };
type QueryOptions = Partial<UndefinedInitialDataOptions>;

export function useSuggestions(params: QueryArgs, options: Partial<QueryOptions> = {}) {
  const fetchSwell = useSwellFetch<FetchResponse>();
  const settings = useSettings();

  return useQuery<HookResponse, OpenAPIError>({
    queryKey: [ApiKey, settings.stage, params],
    queryFn: async () => {
      const query = renderGql<QueryArgs>(ApiKey, params);
      const raw = (await fetchSwell(query)) as FetchResponse;
      const sections: NormalSection[] = [];

      for (let i = 0; i < raw.data.listSwellSuggestions.sections.length; i++) {
        const section = raw.data.listSwellSuggestions.sections[i];

        switch (section.sectionType) {
          case SwellcastSectionType.AuthorSwells:
          case SwellcastSectionType.CategorySwells:
          case SwellcastSectionType.CountrySwells:
          case SwellcastSectionType.HashtagSwells:
          case SwellcastSectionType.LanguageSwells:
          case SwellcastSectionType.OwnerSwells:
          case SwellcastSectionType.PopularSwells:
          case SwellcastSectionType.RecommendationSwells:
            sections.push({
              ...EMPTY_SECTION,
              swells: section.swellcast.swells as unknown as OpenApiHomePageSwellResponse[], //
              type: HomePageRenderType.Horizontal,
              label: section.name,
              id: `${i}`,
              sectionDataType: HomePageSectionDataType.Swells,
            });
            break;
        }
      }

      return { sections };
    },
    placeholderData: keepPreviousData,
    enabled: !!(options?.enabled ?? true),
  });
}
