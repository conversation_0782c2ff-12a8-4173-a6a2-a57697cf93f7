// import AudioSubscribe from '../assets/audio/subscribe.mp3';
import { keepPreviousData } from '@tanstack/react-query';
import { useAuth } from '../components/login/useAuth';
import { useAudioMode } from '../framework/useAudioMode';
import { OpenApiAuthorModel, OpenApiReplyResponse, OpenApiSwellResponse, OpenApiSwellSwellcastModel } from '../generated/graphql';
import { MasterRef, RouteParams, SwellListType } from '../models/models';
import { decompressWave, slugify } from '../utils/Utils';
import { getAudioId, getInterstitial, getPostRoll } from '../utils/swell-utils';
import { IQueryOptions, useSwellAPI } from './gql.common';
import { renderGql } from './gql.fragments';

export type OpenApiSwellResponseEnhanced = Omit<OpenApiSwellResponse, 'replies'> &
  OpenApiReplyResponseEnhanced & {
    replies: OpenApiReplyResponseEnhanced[];
  };

export type OpenApiReplyResponseEnhanced = OpenApiReplyResponse & { index: number; placeholder?: boolean; masterRef: MasterRef; description?: string };

function getInterstitialIndex(canonicalId: string, repliesCount: number) {
  const test0 = repliesCount > 2;
  const test1 = /[135]/g.test(canonicalId);
  const insertAt = 2 + (parseInt(canonicalId.replace(/[^0-9.]/g, '') + 0) % (repliesCount * 2));
  const test2 = insertAt < repliesCount;
  return test0 && test1 && test2 ? insertAt : -1;
}

function processResponse(res: OpenApiSwellResponse, query: RouteParams, loggedIn = false): OpenApiSwellResponseEnhanced {
  const output = { ...res } as OpenApiSwellResponseEnhanced;
  const hackId = !loggedIn && res?.subscriptionOnly; // or log in won't refresh because ids are same and audio file doesn't update

  output.masterRef = {
    isMaster: true,
    params: {
      ...query, //
      slug: slugify(res.title ?? ''),
      listId: res.swellcast?.owner?.alias?.toLowerCase() ?? '',
      swellId: res.id,
      listType: SwellListType.SWELLCAST,
      replyId: res.id,
    },
    id: hackId ? getAudioId(res) : res.id,
    // id: getAudioId(res), // res.id,
    // trackId: getAudioId(res), // res.id,
    owner: res.swellcast?.owner ?? {},
    title: res.title ?? '',
    description: res?.description ?? '',
    author: (res.swellcast?.owner ?? {}) as unknown as OpenApiAuthorModel,
    countryCode: res.countryCode ?? '',
    languages: res?.languages ?? [],
    categories: res?.categories ?? [],
    swellcast: res.swellcast as OpenApiSwellSwellcastModel,
    articleImage: res?.articles?.[0]?.image ?? '',
    swellcastImage: res?.ogImage ?? res?.swellcast?.image ?? '',
    subscriptionOnly: res?.subscriptionOnly ?? false,
    canReply: res?.canReply ?? false,
    isPanel: res?.isPanel ?? false,
  };
  output.index = 0;
  output.faces = output?.faces ?? [];
  output.audio = { ...output.audio, wave: decompressWave(output.audio?.wave ?? []).map((i) => i * 0.66) };
  output.id = hackId ? getAudioId(res) : res.id;
  output.replies = (output?.replies ?? []).map((reply, i) => ({
    ...reply,
    // id: getAudioId(reply),
    id: hackId ? getAudioId(reply) : reply.id,
    // trackId: getAudioId(reply),
    description: reply.message, // normalize swell master so we can access the description in the same way - why are these different anyways?!
    masterRef: {
      ...output.masterRef,
      isMaster: false,
      params: { ...output.masterRef.params, replyId: reply.id },
      author: reply.author,
      articleImage: reply?.articles?.[0]?.image ?? output.masterRef.articleImage,
    },
    index: i + 1,
    audio: { ...reply.audio, wave: decompressWave(reply.audio?.wave ?? []).map((i) => i * 0.66) },
  })) as OpenApiSwellResponseEnhanced['replies'];

  if (!loggedIn) {
    // start: add interstitial
    const insertAt = getInterstitialIndex(output?.canonicalId ?? '', output.replies.length);

    if (insertAt > -1) {
      const interstitial = insertAt >= output.replies.length - 1 ? getPostRoll(output.masterRef.params, output) : getInterstitial(output.masterRef.params, output);
      output.replies.splice(insertAt, 0, interstitial as OpenApiReplyResponseEnhanced);
    }
  }

  output.replies.forEach((r, i) => (r.index = i + 1));
  // end: add interstitial
  return output;
}

export function isValidCanonicalId(input: unknown) {
  return input !== undefined && input !== null && typeof input === 'string' && input.trim().length > 5;
}

export function useSwell(params: RouteParams, initialData?: OpenApiSwellResponseEnhanced) {
  const auth = useAuth();
  const options: IQueryOptions<OpenApiSwellResponseEnhanced> = {};
  options.enabled = isValidCanonicalId(params?.canonicalId);
  if (initialData) options.initialData = initialData;
  if (!params.canonicalId) options.placeholderData = keepPreviousData;

  return useSwellAPI<OpenApiSwellResponseEnhanced>(
    ['loadSwellById', params.canonicalId], //
    renderGql('loadSwellById', { id: params.canonicalId }),
    (raw) => processResponse(raw.data?.loadSwellById as OpenApiSwellResponse, params, auth.loggedIn),
    options,
  );
}

export const useCurrentSwell = () => useAudioMode().swell;
