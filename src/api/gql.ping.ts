import { isServer, useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { useDebug } from '../components/debug/useDebug';
import { useSettings } from '../components/settings/useSettings';
import { formatAMPM } from '../utils/formatAMPM';

export function usePing() {
  const { debug } = useDebug('downtime');
  const [searchParams] = useSearchParams({ maintenanceend: new Date(Date.now() + 1000 * 30).toISOString() });
  const settings = useSettings();

  return useQuery({
    queryKey: ['ping', settings.stage, debug],
    queryFn: async () => {
      let res = await fetch(settings.apiUrl, {
        method: 'POST',
        body: JSON.stringify({ query: '{ping}' }),
        headers: { 'content-type': 'application/json' },
      });

      if (debug) {
        res = new Response(`{"data": {"ping": "OpenAPI Pinged at: Tue Nov 26 2024 13:07:42 GMT+0000 (Coordinated Universal Time)"}}`, {
          status: 510,
          headers: {
            'x-swell-maintenanceend': searchParams.has('maintenanceend') ? searchParams.get('maintenanceend')!.toString() : '',
          },
        });
      }

      const DEFAULT_END = new Date(Date.now() + 1000 * 60 * 60);
      const maintenanceend = res.headers.has('x-swell-maintenanceend') ? res.headers.get('x-swell-maintenanceend')!.toString() : '';
      const isValidDate = Date.parse(maintenanceend);
      const maintenanceEndDate = isValidDate ? new Date(Date.parse(maintenanceend)) : DEFAULT_END;
      const showTime = isValidDate && res.status !== 200 ? true : false;

      let block = false;
      let message = '';

      switch (res.status) {
        case 200:
          break;

        case 510:
          block = true;
          if (showTime) {
            const sameDay = maintenanceEndDate.getDate() === new Date().getDate();
            const hhmm = formatAMPM(maintenanceEndDate);
            const endTime = sameDay ? `at ${hhmm}` : `on ${maintenanceEndDate.toLocaleDateString()} at ${hhmm}`;
            message = `We are undergoing routine maintenance and estimate coming back online ${endTime}`;
          } else {
            message = `We are undergoing routine maintenance and we will be coming back online soon.`;
          }
          break;

        case 503:
          block = true;
          message = `Our servers are experiencing unusually high traffic. Please come back later...`;
          break;

        case 500:
          block = true;
          message = `We are experiencing some technical issues and will be back online soon.`;
          break;

        default:
      }

      return {
        status: res.status,
        maintenanceend,
        maintenanceEndDate,
        message,
        showMessage: block,
      };
    },
    enabled: !isServer,
  });
}
