import { OpenApiSwellcastResponse, QuerySearchArgs, SwellFlags } from '../generated/graphql';
import { IPaginate, RouteParams } from '../models/models';
import { useSwellAPI } from './gql.common';
import { renderGql } from './gql.fragments';
import { SWELLCAST_DEFAULT_LIMIT } from './gql.getSwellcast';

type QuerySearchArgsExt = Omit<QuerySearchArgs, 'limit' | 'offset'> & { limit?: number; offset?: number };
type ISearchParams = Partial<IPaginate> & RouteParams;

export function useSearch(params: ISearchParams & { flags?: SwellFlags[] }) {
  params = { offset: 0, limit: SWELLCAST_DEFAULT_LIMIT, enabled: true, search: '', flags: [], ...params };
  return useSwellAPI<OpenApiSwellcastResponse>(
    ['search', params.search, params.offset, params.limit, params.flags], //
    renderGql<QuerySearchArgsExt>('search', { limit: params.limit, offset: params.offset, term: params.search, flags: params.flags }),
    (raw) => raw.data.search!,
    { enabled: params.enabled && !!params.search && params.search.trim().length > 1 && params.search !== 'undefined', refetchOnWindowFocus: false },
  );
}
