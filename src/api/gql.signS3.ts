import { Mutation } from '../generated/graphql';
import { FetchOptions, fetchAPI } from './gql.common';

export async function getSignS3(props: { filePath: string; fileType: string; swellcastId: string }, options: Omit<FetchOptions, 'token'>) {
  const token = await window.sw_ah();
  const signS3Query = `mutation{
    ss(request:{
        swellcastId:"${props.swellcastId}"
        filePath:"${props.filePath}"
        fileType:"${props.fileType}"
    }){
        code
        message
        endpointUrl
        encryptedUrl
        canonicalId
        params{
        key
        value
        }
    }
}`;
  const res = await fetchAPI(signS3Query, { apiUrl: options.apiUrl, token });
  return (res.data as Mutation).ss;
}
