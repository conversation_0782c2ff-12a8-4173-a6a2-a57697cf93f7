$spacer: 1rem;

@use '~bootstrap/scss/bootstrap' with (
  $line-height-base: 1.3,
  $dark: #000,
  $border-radius: 0.375rem,
  $border-radius-sm: 0.5rem,
  $border-radius-lg: 1rem,
  $border-radius-xl: 1.5rem,
  $border-radius-xxl: 2rem,
  $font-size-base: 0.9rem,
  $font-weight-normal: 400,
  $font-family-base: (
    Inter,
    Helvetica Neue,
    sans-serif,
  ),
  $display-font-family: (
    'DM Sans',
    Arial,
    sans-serif,
  ),
  $display-font-sizes: (
    6: 34px,
    5: 38px,
    4: 42px,
    3: 46px,
    2: 50px,
    1: 54px,
  ),
  $display-font-weight: 700,
  $headings-font-family: (
    'D<PERSON> Sans',
    Inter,
    Arial,
    sans-serif,
  ),
  $headings-font-weight: 700,
  $h1-font-size: 25.6px,
  $h2-font-size: 17.6px,
  $h3-font-size: 14.04px,
  $h4-font-size: 12.8px,
  $h5-font-size: 11.2px,
  $h6-font-size: 9.6px,
  $spacers: (
    0: 0,
    1: $spacer * 0.25,
    2: $spacer * 0.5,
    3: $spacer,
    4: $spacer * 2,
    5: $spacer * 3,
  ),
  $spinner-border-width: 3px
);

* {
  text-rendering: geometricPrecision;
}
:root {
  --bs-nav-link-font-size: 16px;
}
[data-bs-theme='dark'] {
  --bs-body-bg: #000;
}
.pointer {
  cursor: pointer;
}
.lh-normal {
  line-height: normal !important;
}
@each $key, $val in bootstrap.$grid-breakpoints {
  @media (min-width: $val) {
    .rounded-#{$key}-bottom {
      border-bottom-left-radius: 2rem;
      border-bottom-right-radius: 2rem;
      overflow: hidden;
    }
    .bg-#{$key}-cover {
      background-size: cover;
    }
    .bg-#{$key}-contain {
      background-size: contain;
    }
  }
}
// these bg- classes are not used *yet* but they could be useful
.bg-cover {
  background-size: cover;
}
.bg-contain {
  background-size: contain;
}
.bg-center {
  background-position: center;
}
