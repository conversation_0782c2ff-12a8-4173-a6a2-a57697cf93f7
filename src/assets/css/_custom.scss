@use 'sass:map';
@use 'variable' as *;
@use 'boot' as *;
/*

color mode

*/
.spinner-border {
  color: map.get($theme-colors, 'squash');
}
html,
body {
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: auto;
}
body {
  overflow-x: hidden;
}
[data-bs-theme='dark'] .bg-mode {
  background-color: #000;
}
[data-bs-theme='light'] .bg-mode {
  background-color: #fff;
}
[data-bs-theme='dark'] .bg-mode-reverse {
  background-color: #fff;
}
[data-bs-theme='light'] .bg-mode-reverse {
  background-color: #000;
}
[data-bs-theme='dark'] .border-mode {
  border-color: #000;
}
[data-bs-theme='light'] .border-mode {
  border-color: #fff;
}
[data-bs-theme='dark'] .text-mode {
  color: #fff;
}
[data-bs-theme='light'] .text-mode {
  color: #000;
}
[data-bs-theme='dark'] .text-mode-reverse {
  color: #000;
}
[data-bs-theme='light'] .text-mode-reverse {
  color: #fff;
}
[data-bs-theme='light'] .link-fix a {
  @extend .text-blue;
}
[data-bs-theme='light'] {
  --wave-fill: #000;
}
[data-bs-theme='dark'] {
  --wave-fill: #fff;
}

hr.thin {
  border: 0;
  height: 0;
  border-top: 1px solid rgba(128, 128, 128, 0.5);
}
.border-top {
  border-top: 1px solid rgba(128, 128, 128, 0.3);
}
.border-bottom {
  border-bottom: 1px solid rgba(128, 128, 128, 0.3);
}
/*

swell logo

*/
[data-bs-theme='dark'] .swell-logo-nav > path {
  fill: #fff;
}
[data-bs-theme='light'] .swell-logo-nav > path {
  fill: #000;
}
/*

general

*/
.btn:focus {
  outline: 0;
  box-shadow: none !important;
}
small {
  font-size: 11px;
  font-weight: 400;
  color: map.get($theme-colors, 'grey-dark');
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
/*

Aspect ratio tricks: https://www.trysmudford.com/blog/minimum-aspect-ratio/

*/
.min-aspect-square {
  --aspect-padding-top: 100%;
}
.min-aspect-landscape {
  --aspect-padding-top: 56.25%;
}
.min-aspect-portrait {
  --aspect-padding-top: 140%;
}
.min-aspect::before,
.min-aspect-square::before,
.min-aspect-landscape::before,
.min-aspect-portrait::before {
  padding-top: var(--aspect-padding-top);
  content: '';
  display: block;
  float: left;
}
.min-aspect::after,
.min-aspect-square::after,
.min-aspect-landscape::after,
.min-aspect-portrait::after {
  content: '';
  display: table;
  clear: both;
}
/*

visit swell.life screen

*/
.borealis-effect-1 {
  animation: undulate 3.5s infinite;
}
.borealis-effect-2 {
  animation: undulate 5.5s infinite;
}
.borealis-effect-3 {
  animation: undulate 8.5s infinite;
}
.borealis-effect-4 {
  animation: undulate 11.5s infinite;
}
@keyframes undulate {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
.fade-in {
  animation: fadein 0.5s ease-in;
}
.fade-in-fast {
  animation: fadein 0.25s ease-in;
}
@keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* https://stackoverflow.com/questions/2781549/removing-input-background-colour-for-chrome-autocomplete */
input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition: background-color 600000s 0s, color 600000s 0s;
}
input[data-autocompleted] {
  background-color: transparent !important;
}
.sr-only {
  display: none;
}
.no-select {
  -webkit-user-select: none; /* Chrome all / Safari all */
  -moz-user-select: none; /* Firefox all */
  -ms-user-select: none; /* IE 10+ */
  user-select: none; /* Likely future */
}
.text-hover-underline:hover {
  text-decoration: underline !important;
}
pre {
  background-color: rgba(0, 0, 0, 0.95); // #000;
  color: #ff9966 !important;
  font-size: 11px !important;
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}
.btn-reset {
  all: unset;
  display: inline-block;
  cursor: pointer;
}
a {
  font-size: inherit;
}
a.fix,
a.fix:hover {
  @extend .text-reset;
  text-decoration: inherit;
}
a.hover:hover {
  text-decoration: underline;
}
/*

bruno backgrounds
Mobile doesn't render backdrop filters so skip the blur and make it solid in that case

*/
// created using test/GradientBuilder.tsx
.swell-cloud-colors {
  background: #293d60;
  background: radial-gradient(60% 77% at 96% 97%, rgb(160, 101, 101) 0%, transparent 78%), radial-gradient(23% 52% at -2% 98%, rgb(207, 143, 47) 0%, transparent 271%), radial-gradient(26% 71% at 97% 8%, rgb(111, 65, 131) 0%, transparent 120%), radial-gradient(33% 73% at 69% 106%, rgb(43, 155, 141) 0%, transparent 148%), linear-gradient(180deg, rgba(5, 25, 56, 1) 0%, rgba(118, 135, 161, 1) 100%);
}
.popup-colors {
  background: #0b2e47;
  background: radial-gradient(60% 77% at 96% 97%, rgb(160, 101, 101) 0%, transparent 0%), radial-gradient(circle at 113% -19%, rgb(207, 143, 47) 0%, transparent 73%), radial-gradient(26% 71% at 97% 8%, rgb(111, 65, 131) 0%, transparent 0%), radial-gradient(circle at -27% 111%, rgb(43, 155, 141) 0%, transparent 73%), linear-gradient(0deg, rgb(5, 25, 56) 0%, rgb(5, 25, 56) 100%);
}
.bruno-colors {
  background-image: linear-gradient(1.5708rad, rgba(252, 185, 80, 0.796) -16.63%, rgba(43, 187, 181, 0.714) 35.7%, rgba(238, 90, 143, 0.157) 113.3%);
}
.bruno-colors-dark {
  background-image: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), linear-gradient(1.5708rad, rgba(252, 185, 80, 0.796) -16.63%, rgba(43, 187, 181, 0.714) 35.7%, rgba(238, 90, 143, 0.157) 113.3%);
}
.player-colors {
  background: rgb(30, 34, 41);
  background: radial-gradient(circle at 50% 70%, rgba(30, 34, 41, 1) 0%, rgba(0, 125, 120, 1) 70%, rgba(133, 141, 93, 1) 120%);
}
.recorder-colors {
  background: rgb(24, 24, 25);
  background: linear-gradient(0deg, rgba(24, 24, 25, 1) 0%, rgba(8, 7, 11, 1) 100%);
}
.player-colors {
  background: rgb(30, 34, 41);
  background: radial-gradient(circle at 50% 70%, rgb(30, 34, 41) 0%, rgb(0, 125, 120) 130%);
}
.backdrop-blur-player {
  @extend .player-colors;
  background-color: #000;
  position: relative;
}
.backdrop-blur-recorder {
  @extend .recorder-colors;
  position: relative;
}
[data-bs-theme='light'] .backdrop-blur-player {
  backdrop-filter: blur(80px) brightness(30%);
  -webkit-backdrop-filter: blur(80px) brightness(30%);
}
.backdrop-blur {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}
.backdrop-blur-darken {
  backdrop-filter: blur(8px) brightness(50%);
  -webkit-backdrop-filter: blur(8px) brightness(50%);
  background: rgba(100, 100, 100, 0.25);
}
.backdrop-blur-lighten {
  backdrop-filter: blur(5px) brightness(300%);
  -webkit-backdrop-filter: blur(5px) brightness(150%);
  background-color: rgba(128, 128, 128, 0.5);
}
.backdrop-blur-card {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
.backdrop-blur-player {
  background-color: transparent;
}
.text-shadow {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}
[data-bs-theme='dark'] .text-shadow-mode {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}
[data-bs-theme='light'] .text-shadow-mode {
  text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.7);
}
.text-shadow-sm {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}
.landscape {
  aspect-ratio: 1280/720;
  width: 100%;
}
.portrait {
  aspect-ratio: 100/140;
  width: 100%;
}
.square {
  aspect-ratio: 1/1;
  width: 100%;
}
img.cover {
  object-fit: cover;
  object-position: center;
}
img.contain {
  object-fit: contain;
  object-position: center;
}
*:not(img).cover {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
div.contain {
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
/*

search

*/
[data-bs-theme='light'] .search-container {
  background-color: rgba(90, 90, 90, 0.2);
}
[data-bs-theme='dark'] .search-container {
  background-color: rgba(90, 90, 90, 0.5);
}
.absolute-fill {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}
.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.absolute-se {
  position: absolute;
  bottom: 0;
  right: 0;
}

button.chromeless {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
}
/*

header background

*/
.header-background {
  position: absolute;
  inset: -8px;
  height: inherit;
  background-size: cover;
  background-position: center;
  filter: blur(8px) contrast(0.5) opacity(0.8);
  transform: scale(1);
  transform-origin: center;
}
