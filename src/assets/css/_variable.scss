@use 'sass:math';
@use 'sass:color';

$mobile-width: 900px;
$grid-col-width: 360;
$grid-gap: 10;
$grid-pad: 20;

$swell-card-width: 272;
$swellview-width: 600px;
$card-padding: 25px;

$col-break-1: ($grid-col-width * 1) + (2 * $grid-gap);
$col-break-2: ($grid-col-width * 2) + (3 * $grid-gap);
$col-break-3: ($grid-col-width * 3) + (4 * $grid-gap);
$col-break-4: ($grid-col-width * 4) + (5 * $grid-gap);
$col-break-5: ($grid-col-width * 5) + (6 * $grid-gap);

.grid-gap {
  gap: calc(var(--grid-gap) * 1px);
}

.grid-pad {
  padding-left: calc(var(--grid-pad) * 1px);
  padding-right: calc(var(--grid-pad) * 1px);
  padding-top: calc(var(--grid-pad) * 1px);
  padding-bottom: calc(var(--grid-pad) * 1px);
}
.grid-pad-x {
  padding-left: calc(var(--grid-pad) * 1px);
  padding-right: calc(var(--grid-pad) * 1px);
}

$theme-colors: (
  'transparent': rgba(0, 0, 0, 0),
  'white': #fff,
  'green': #4c8031,
  'black': #000,
  'primary': #fcb950,
  'dark': #050505,
  'squash-light': #fdc66c,
  'squash': #fcb950,
  'squash-dark': #ce9c4b,
  'squash-darker': #a07023,
  'grey-lighter': #f5f5f5,
  'grey-light': #ededed,
  'grey': #808080,
  'grey-dark': #4a4a4a,
  'grey-darker': #232323,
  'grey-darkest': #131313,
  'blue': #007aff,
  'red': #e46f38,
  'record-red': #df4747,
  'notification-red': #f44238,
  'recording': #df4747,
  'premium-blue': #5cb8b4,
  'pink': #ee5a8f,
);

body {
  @each $theme-key, $theme-color in $theme-colors {
    .bg-#{$theme-key} {
      background-color: $theme-color !important;
    }
    .icon-#{$theme-key} {
      --icon-color: #{$theme-color};
    }
    @for $i from 0 through 9 {
      $alpha: calc($i / 10);
      .bg-#{$theme-key}-#{$i * 10} {
        background-color: color.change($theme-color, $alpha: $alpha) !important;
      }
      .bg-hover-#{$theme-key}-#{$i * 10}:hover {
        background-color: color.change($theme-color, $alpha: $alpha) !important;
      }
      .text-#{$theme-key}-#{$i * 10} {
        $alpha: calc($i / 10);
        color: color.change($theme-color, $alpha: $alpha);
      }
      .border-#{$theme-key}-#{$i * 10} {
        $alpha: calc($i / 10);
        border: 1px solid color.change($theme-color, $alpha: $alpha);
      }
    }
    .text-#{$theme-key} {
      color: $theme-color !important;
    }
    .text-hover-#{$theme-key}:hover {
      color: $theme-color !important;
    }
    .border-#{$theme-key} {
      border: 1px solid $theme-color;
    }
    .outline-#{$theme-key} {
      outline: 1px solid $theme-color;
    }
  }
  @for $i from 1 through 12 {
    .line-clamp-#{$i} {
      display: -webkit-box;
      line-clamp: #{$i};
      -webkit-line-clamp: #{$i};
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
    }
  }
  .btn-gradient {
    background: rgb(96, 102, 115);
    background: linear-gradient(180deg, rgba(96, 102, 115, 0.24) 0%, rgba(0, 0, 0, 0.8) 100%);
  }
  .fade-to-black {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
  }
  .bg-dark-trans {
    background-color: rgba(30, 30, 30, 0.54) !important;
  }
  .bg-white-trans {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
  .bg-grey-solid {
    background-color: #373942;
  }
}
