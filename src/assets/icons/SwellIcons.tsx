import React, { CSSProperties } from 'react';
import AddSVG from './bruno_icon_add.svg';
import BinSVG from './bruno_icon_bin.svg';
import BlockSVG from './bruno_icon_block.svg';
import CheckSVG from './bruno_icon_check.svg';
import ChevronLeftSVG from './bruno_icon_chevron_left.svg';
import ChevronRightSVG from './bruno_icon_chevron_right.svg';
import CloseSVG from './bruno_icon_close.svg';
import EditSVG from './bruno_icon_edit.svg';
import FindSVG from './bruno_icon_find.svg';
import HeartSVG from './bruno_icon_heart.svg';
import HelpSVG from './bruno_icon_help.svg';
import HomeSVG from './bruno_icon_home.svg';
import MenuSVG from './bruno_icon_menu.svg';
import MicSVG from './bruno_icon_mic.svg';
import MoreHorizSVG from './bruno_icon_more_horiz.svg';
import MoreVertSVG from './bruno_icon_more_vert.svg';
import NotificationSVG from './bruno_icon_notification.svg';
import PauseSVG from './bruno_icon_pause.svg';
import PeopleSVG from './bruno_icon_people.svg';
import PersonSVG from './bruno_icon_person.svg';
import PinSVG from './bruno_icon_pin.svg';
import PlaySVG from './bruno_icon_play.svg';
import PodcastsSVG from './bruno_icon_podcasts.svg';
import ProSVG from './bruno_icon_pro2.svg';
import QuestionMarkSVG from './bruno_icon_questionmark.svg';
import Replay10SVG from './bruno_icon_replay_10.svg';
import ShareSVG from './bruno_icon_share.svg';
import SkipNextSVG from './bruno_icon_skip_next.svg';
import SkipPreviousSVG from './bruno_icon_skip_previous.svg';
import SpeakSVG from './bruno_icon_speak.svg';
import SpotifySVG from './bruno_icon_spotify.svg';
import StarSVG from './bruno_icon_star.svg';
import UpdateSVG from './bruno_icon_update.svg';
import UploadSVG from './bruno_icon_upload.svg';

import classNames from 'classnames';
import { slugify } from '../../utils/Utils';

// import './swell-icons.scss';

type IconStyle = CSSProperties & {
  '--icon-color'?: string; //
  '--icon-fill'?: string;
  '--icon-width'?: string;
  '--icon-height'?: string;
};

type IconProps = Omit<React.SVGProps<SVGSVGElement>, 'style'> & { style?: IconStyle };
/*

If you want to preserve the viewBox attribute, you need to put it in below manually.
@svgr/webpack is being an absolute donut hole and not allowing viewBox to stay.
Google and ChatGPT are full of lies about this issue.

Crop SVG using https://svgcrop.com/

*/

export const SwellIcons = () => {
  return <></>;
};

function toPx(value: string | number) {
  if (typeof value === 'number') {
    return `${value}px`;
  } else if (typeof value === 'string') {
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue)) {
      return `${numericValue}px`;
    }
  }
  return value;
}

function buildIcon(name: string, Svg: React.FC<React.SVGProps<SVGSVGElement>>) {
  return (props: IconProps) => <SwellIconBase Icon={Svg} {...props} className={classNames(`swell-icon swell-icon-${slugify(name)}`, props.className)} />;
}

const SwellIconBase = ({ Icon, ...rest }: IconProps & { Icon: React.FC<React.SVGProps<SVGSVGElement>> | string }) => {
  const baseStyle: IconStyle = {}; // '--icon-width': rest?.width ? `${rest?.width}` : 'var(--icon-width)' };

  if (rest?.width) {
    baseStyle['--icon-width'] = toPx(rest.width);
  }
  if (rest?.height) {
    baseStyle['--icon-height'] = toPx(rest.height);
  }
  if (rest?.style?.color) {
    baseStyle['--icon-color'] = rest.style.color;
  }
  if (rest?.style?.stroke) {
    baseStyle['--icon-color'] = rest.style.stroke;
  }
  if (rest?.style?.backgroundColor) {
    baseStyle['--icon-fill'] = rest.style.backgroundColor;
  }
  if (rest?.style?.fill) {
    baseStyle['--icon-fill'] = rest.style.fill;
  }
  if (rest?.style?.width) {
    baseStyle['--icon-width'] = rest.style.fill;
  }
  const style = { ...baseStyle, ...rest.style, color: undefined, backgroundColor: undefined };
  return <Icon data-component='swellicon' {...rest} style={style} />;
};

SwellIcons.Upload = buildIcon('Upload', UploadSVG);
SwellIcons.Add = buildIcon('Add', AddSVG);
SwellIcons.Bin = buildIcon('Bin', BinSVG);
SwellIcons.Block = buildIcon('Block', BlockSVG);
SwellIcons.Check = buildIcon('Check', CheckSVG);
SwellIcons.ChevronLeft = buildIcon('ChevronLeft', ChevronLeftSVG);
SwellIcons.ChevronRight = buildIcon('ChevronRight', ChevronRightSVG);
SwellIcons.Close = buildIcon('Close', CloseSVG);
SwellIcons.Edit = buildIcon('Edit', EditSVG);
SwellIcons.Find = buildIcon('Find', FindSVG);
SwellIcons.Heart = buildIcon('Heart', HeartSVG);
SwellIcons.Help = buildIcon('Help', HelpSVG);
SwellIcons.Home = buildIcon('Home', HomeSVG);
SwellIcons.Mic = buildIcon('Mic', MicSVG);
SwellIcons.MoreHoriz = buildIcon('MoreHoriz', MoreHorizSVG);
SwellIcons.MoreVert = buildIcon('MoreVert', MoreVertSVG);
SwellIcons.Notification = buildIcon('Notification', NotificationSVG);
SwellIcons.Pause = buildIcon('Pause', PauseSVG);
SwellIcons.People = buildIcon('People', PeopleSVG);
SwellIcons.Person = buildIcon('Person', PersonSVG);
SwellIcons.Pin = buildIcon('Pin', PinSVG);
SwellIcons.Play = buildIcon('Play', PlaySVG);
SwellIcons.Podcasts = buildIcon('Podcasts', PodcastsSVG);
SwellIcons.QuestionMark = buildIcon('QuestionMark', QuestionMarkSVG);
SwellIcons.Replay10 = buildIcon('Replay10', Replay10SVG);
SwellIcons.Share = buildIcon('Share', ShareSVG);
SwellIcons.SkipNext = buildIcon('SkipNext', SkipNextSVG);
SwellIcons.SkipPrevious = buildIcon('SkipPrevious', SkipPreviousSVG);
SwellIcons.Speak = buildIcon('Speak', SpeakSVG);
SwellIcons.Spotify = buildIcon('Spotify', SpotifySVG);
SwellIcons.Star = buildIcon('Star', StarSVG);
SwellIcons.Update = buildIcon('Update', UpdateSVG);
SwellIcons.Menu = buildIcon('Menu', MenuSVG);
SwellIcons.Pro = buildIcon('Pro', ProSVG);
