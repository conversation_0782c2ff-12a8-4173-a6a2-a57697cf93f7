import { OpenApiStationWithSectionsResponse } from '../../generated/graphql';
import { toBgUrl } from '../../utils/Utils';
import { getStationBackgroundImage } from '../../utils/getStationImage';
import { ShareStationButton } from '../embed-code/ShareStationButton';
import { SwellDescription } from '../swell-card/CardDescription';

export const CommunityHeader = ({ station }: { station: OpenApiStationWithSectionsResponse }) => (
  <div style={{ height: 'min(40vh, 360px)' }}>
    <div
      className='fade-mask-bottom absolute-fill'
      style={{
        height: 'inherit',
        backgroundImage: toBgUrl(getStationBackgroundImage(station)), //
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        filter: 'blur(5px) contrast(0.5) opacity(0.8)',
        transform: 'scale(1)',
        transformOrigin: 'center',
      }}
    />
    <div className='max-width-sections mx-auto position-relative' style={{ height: 'inherit' }}>
      <div
        className='fade-mask-bottom absolute-fill'
        style={{
          height: 'inherit',
          backgroundImage: toBgUrl(getStationBackgroundImage(station)), //
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      ></div>

      <div className='absolute-fill position-relative d-flex flex-column w-100 justify-content-between grid-pad'>
        <div className='d-flex w-100 gap-2 justify-content-between'>
          <div className='d-flex gap-2'>{/* Future home of station share button */}</div>
          <div className='d-flex gap-2'>
            <ShareStationButton stationId={station.id} type={station.type} />
          </div>
        </div>
        <div className='d-flex flex-column gap-3'>
          <h1 className='text-mode text-shadow-mode text-break lh-1'>{station.name}</h1>
          {station?.description && station.description.trim() !== '' ? <SwellDescription description={station.description} className={`fw-light text-break mb-1 text-mode`} /> : null}
        </div>
      </div>
    </div>
  </div>
);
