import React, { useEffect, useState } from 'react';
import { usePing } from '../../api/gql.ping';
import swellColors from '../../assets/images/swell-colors.png';
import { formatDuration, toBgUrl } from '../../utils/Utils';
import { SwellLogo } from './SwellLogo';

const useCountdown = (endDate: Date) => {
  const [duration, setDuration] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const clock = formatDuration(duration * 0.001);

  useEffect(() => {
    let t: NodeJS.Timeout;
    if (endDate.getTime() > Date.now()) {
      t = setInterval(() => {
        const dif = endDate.getTime() - Date.now();
        if (dif > 0) {
          setDuration(dif);
        } else {
          setIsComplete(true);
          clearInterval(t);
        }
      }, 1000);
    }

    return () => {
      if (t) {
        clearInterval(t);
      }
    };
  }, [endDate]);

  return { duration, isComplete, clock };
};

const DowntimeScreen = (data: ReturnType<typeof usePing>['data']) => {
  const doReload = () => window.location.reload();
  const countdown = useCountdown(data?.maintenanceEndDate ?? new Date());

  // reload when countdown finishes
  useEffect(() => {
    if (countdown.isComplete) {
      doReload();
    }
  }, [countdown.isComplete]);

  return (
    <div
      className='fade-in flex-center bg-grey'
      style={{
        position: 'fixed',
        inset: 0,
        backgroundImage: toBgUrl(swellColors),
        backgroundSize: 'cover',
      }}
    >
      <div className='text-center p-4'>
        <div className='text-shadow-sm text-white d-flex flex-column align-items-center gap-3'>
          <SwellLogo style={{ width: 200 }} id='Downtime' />
          <h3>Sorry, we&apos;ll be back soon.</h3>
          <p>{data?.message}</p>
          <button className='btn btn-lg btn-primary rounded-1 shadow px-5 fit-content' onClick={doReload}>
            {countdown.duration > 0 ? (
              <>
                Try in... <span style={{ fontFamily: 'monospace' }}>{countdown.clock}</span>
              </>
            ) : (
              'Try Now'
            )}
          </button>
          <p className='text-red'>({data?.status})</p>
        </div>
      </div>
    </div>
  );
};

export const Downtime = ({ children }: { children: React.ReactNode }) => {
  const query = usePing();

  if (query.isSuccess && query.data?.showMessage === true) {
    return <DowntimeScreen {...query.data} />;
  }

  return children;
};
