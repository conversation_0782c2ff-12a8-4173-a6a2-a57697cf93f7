import classNames from 'classnames';
import React, { useEffect, useId, useRef, useState } from 'react';
import { ThemeColors } from '../../assets/css/ThemeColors';
import torso from '../../assets/images/avatar-torso-opt.svg?url';
import { isTextSwell } from '../../utils/swell-utils';
import { toBgUrl } from '../../utils/Utils';
import { useAudioData } from '../player/AudioPlayer/useAudioData';
import { useAudioTrack } from '../player/AudioPlayer/useAudioTrack';
import { useAudioTrackTime } from '../player/AudioPlayer/useAudioTrackTime';
import { ProSwellBadge } from './ProSwellBadge';

interface ISwellCircularProgress {
  size?: number;
  thickness?: number;
  progress?: number;
  color?: string;
  isLoading?: boolean;
}

interface ISwellMugshot extends ISwellCircularProgress {
  image: string | null;
  onClick: React.MouseEventHandler<HTMLDivElement>;
  alt: string;
  className: string;
  pieType: 'svg' | 'cvs' | 'css';
  isPro: boolean;
}

export const Mugshot = ({
  size = 40, //
  thickness = 3,
  image = torso,
  onClick,
  progress = 0,
  color = ThemeColors.squash,
  alt = 'Swell user mugshot',
  className = '',
  pieType = 'cvs',
  style,
  isPro = false,
}: Partial<ISwellMugshot> & React.HTMLAttributes<HTMLDivElement>) => {
  const width = size + thickness + thickness;
  const allClassName = classNames('rounded-circle position-relative', className);
  const Pie = pieType === 'cvs' ? PieCVS : pieType === 'svg' ? PieSVG : PieCSS;
  // use hi-res image when size warrants it
  const highImage = image === torso ? image : image?.replace('SWELL_LOW', 'SWELL_MEDIUM') ?? '';
  const src = size > 120 ? highImage : image ?? '';

  return (
    <div
      title={alt}
      onClick={onClick}
      style={{
        width,
        overflow: 'visible',
        aspectRatio: '1/1',
        transition: 'background 0.5s',
        ...style,
      }}
      data-component='mugshot'
      className={allClassName}
    >
      <Pie progress={progress} color={color} size={width} style={{ position: 'absolute', top: 0, left: 0 }} />
      <div
        style={{
          backgroundImage: image ? [toBgUrl(src), toBgUrl(torso)].join(',') : toBgUrl(torso),
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundColor: 'rgb(180,180,180)',
          inset: thickness,
        }}
        className='rounded-circle position-absolute'
      />
      {isPro ? (
        <ProSwellBadge
          style={{
            position: 'absolute', //
            top: '76.5%',
            left: '76.5%',
            transform: 'translate(-50%, -50%)',
          }}
        />
      ) : null}
    </div>
  );
};

const PieCSS = ({ progress, color, size, style, ...props }: { progress: number; color: string; size: number } & React.HTMLAttributes<HTMLDivElement>) => {
  const percent = progress * 100;
  return <div {...props} className='rounded-circle' style={{ ...style, width: size, height: size, background: `conic-gradient(${color} 0%, ${color} ${percent}%, transparent ${percent}%)` }} />;
};

function drawPieCVS(canvas: HTMLCanvasElement, progress: number, color: string) {
  progress = Math.max(0, Math.min(1, progress));
  const ctx = canvas.getContext('2d');

  if (ctx) {
    // Clear the canvas before drawing
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.imageSmoothingEnabled = true;

    // Convert progress from percentage to angle (0-2π radians)
    const progressAngle = progress * 2 * Math.PI;

    // Define the center and radius of the circle
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 0; // Padding of 10px

    // Draw the progress arc (for the filled portion)
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, -0.5 * Math.PI, progressAngle - 0.5 * Math.PI);
    ctx.lineTo(centerX, centerY);
    ctx.fillStyle = color;
    ctx.fill();
  }
}

const PieCVS = ({ progress, color, size, ...props }: { progress: number; color: string; size: number } & React.CanvasHTMLAttributes<HTMLCanvasElement>) => {
  const $canvas = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if ($canvas.current) {
      drawPieCVS($canvas.current, progress, color);
    }
  }, [progress, $canvas.current, color]);

  return <canvas ref={$canvas} {...props} width={size} height={size}></canvas>;
};

function drawPieSVG(progress: number, radius: number) {
  // Center and radius for the SVG circle
  const cx = radius;
  const cy = radius;
  const r = radius;

  // Convert progress to radians (starting from -90 degrees)
  const startAngle = -Math.PI / 2;
  const endAngle = startAngle + progress * 2 * Math.PI;

  // Calculate the start and end points for the arc
  const startX = cx + r * Math.cos(startAngle);
  const startY = cy + r * Math.sin(startAngle);
  const endX = cx + r * Math.cos(endAngle);
  const endY = cy + r * Math.sin(endAngle);

  // Determine if the arc is greater than 180 degrees
  const largeArcFlag = progress > 0.5 ? 1 : 0;

  // Construct the SVG path for the progress arc
  const pathData = [
    `M ${cx} ${cy}`, // Move to the center of the circle
    `L ${startX} ${startY}`, // Move to start point
    `A ${r} ${r} 0 ${largeArcFlag} 1 ${endX} ${endY}`, // Draw the arc
  ].join(' ');

  return pathData;
}

const PieSVG = ({ progress, color, size, ...props }: { progress: number; color: string; size: number } & React.SVGProps<SVGSVGElement>) => {
  const id = useId();
  const [pathData, setPathData] = useState('');
  const radius = size * 0.5;

  useEffect(() => {
    if (progress > 0) {
      setPathData(drawPieSVG(progress, radius));
    }
  }, [progress]);

  return (
    <svg id={id} viewBox={`0 0 ${size} ${size}`} {...props}>
      {progress === 1 ? <circle cx={radius} cy={radius} r={radius} fill={color} /> : progress === 0 ? '' : <path d={pathData} fill={color} />}
    </svg>
  );
};

export const DynamicMugshot = ({ active = false, image, size = 60, onClick, alt = 'Swell user mugshot' }: { active?: boolean } & Partial<ISwellMugshot>) => {
  const track = useAudioTrack();
  const { progress } = useAudioTrackTime(active);
  return <Mugshot image={image} size={size} isLoading={track.isLoading} progress={progress} onClick={onClick} alt={alt} />;
};

// show currently speaking mugshot
export const ActiveMugshot = ({ size = 60 }: { size?: number }) => {
  const data = useAudioData();
  const isText = data ? isTextSwell(data) : false;
  const authorImage = data?.author?.image;
  return <DynamicMugshot active={!isText} size={size} image={authorImage} />;
};

// export default Pie maker
export const Pie = PieCVS;
