// import SwellAppIcon from '../../assets/images/swell-app-icon-square.svg';
// import React, { CSSProperties } from 'react';
// import { Link } from 'react-router-dom';
// import { SwellIcons } from '../../assets/icons/SwellIcons';
// import { Mugshot } from './Mugshot';

export interface IPromptInfo {
  label: string;
  title: string;
  url?: string;
  color?: string;
  style?: React.CSSProperties;
  alias?: string;
  mugshotImage: string;
}
// // TODO: these should be rendered in HTML
// export const PromptCard = ({ title, mugshotImage, alias = '' }: Partial<IPromptInfo>) => {
//   return (
//     <Link data-component='promptcard' to={''} className='fix min-aspect-portrait flex-center swell-card rounded-2'>
//       <div className='swell-card-content-container'>
//         <div className='swell-card-content'>
//           <div className='w-100 d-flex flex-column p-4 gap-2 justify-content-between'>
//             <h2>{title}</h2>
//             <div className='d-flex flex-column gap-3 justify-content-center'>
//               <div className='flex-center icon-fill-mode'>
//                 <SwellIcons.Mic style={{ width: 42 } as CSSProperties} />
//               </div>
//               <small className='text-mode'>Record my answer</small>
//             </div>
//             <div className='d-flex gap-2 w-100 justify-content-between'>
//               <div className='d-flex gap-4 justify-content-between align-items-center'>
//                 <Mugshot image={mugshotImage} size={40} />
//                 <p className='h4-normal text-truncate fw-bold d-flex'>{alias}</p>
//               </div>
//               <div className='flex-center'>
//                 <SwellAppIcon className='rounded-1' id='prompt' style={{ width: 40 }} />
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </Link>
//   );
// };
