import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useDebug } from '../debug/useDebug';
import { BounceLoaderPage } from './bounceloader/BounceLoaderPage';

export const RefreshPage = () => {
  const { debug } = useDebug('reload');
  const [searchParams] = useSearchParams();
  const [redirectTo, setReciredtTo] = useState('');

  useEffect(() => {
    const url = new URL(window.location.href);
    if (url.searchParams.has('reload')) {
      return setReciredtTo('/');
    } else {
      url.searchParams.append('reload', '1');
    }
    return setReciredtTo(url.href);
  }, []);

  useEffect(() => {
    if (!debug) {
      if (redirectTo) {
        window.location.href = redirectTo;
      }
    }
  }, [debug, redirectTo]);

  if (debug) {
    return <pre className='p-3'>{JSON.stringify({ debug, searchParams, redirectTo }, null, 2)}</pre>;
  }

  return <BounceLoaderPage />;
};
