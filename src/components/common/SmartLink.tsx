import classNames from 'classnames';
import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useState } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import { buildUrl } from '../../framework/buildUrl';
import { useEnv } from '../../framework/useEnv';
import { useOrchestration } from '../../framework/useOrchestration';
import { appendUTM } from '../../utils/UTM.class';
import { useConfirm } from '../../utils/useConfirm';
import { useIsIframed } from '../../utils/useIsIframed';
import { useUTMParams } from '../../utils/useUTMParams';
import { ExitRecordMessage } from '../player/AudioRecorder/popups/ExitRecordMessage';
import { useSettings } from '../settings/useSettings';
// TODO: compare to data-option branch to verify
export const SmartLink = ({ children = null, to, className = '', utm = false, asReferrer = false, searchParams = {}, ...args }: { utm?: boolean; asReferrer?: boolean; searchParams?: Record<string, string | null> | null } & LinkProps & React.HTMLProps<HTMLAnchorElement>) => {
  const utmParams = useUTMParams();
  const env = useEnv();
  const { isWidget } = useSettings();
  const isFramed = useIsIframed();
  const target = isWidget || isFramed ? '_blank' : '_self';
  const { blockNavigation, close } = useOrchestration();
  const { showConfirm } = useConfirm();
  const [href, setHref] = useState(to);

  useEffect(() => {
    // Handle case where CANONICAL_DOMAIN is not set (default to prod)
    const canonicalDomain = env.CANONICAL_DOMAIN || 'swellcast.com';

    const base = to.toString().startsWith('http') ? to.toString() : buildUrl({ host: canonicalDomain, pathname: to.toString(), searchParams });

    // Handle case where buildUrl returns empty string
    if (!base) {
      // SmartLink: buildUrl returned empty string
      setHref(to.toString());
      return;
    }

    const r = utm ? appendUTM(base, utmParams, { asReferrer }) : base;
    setHref(r);
  }, [to, searchParams, utm, asReferrer, env.CANONICAL_DOMAIN, utmParams]);

  const onClick: MouseEventHandler<HTMLAnchorElement> = (e) => {
    // kill same page linking
    if (window.location.pathname === href && target === '_self') {
      e.preventDefault();
      e.stopPropagation();
      return;
    }

    if (blockNavigation) {
      const href = e.currentTarget.href;
      const target = e.currentTarget.target;
      e.preventDefault();
      e.stopPropagation();

      showConfirm(<ExitRecordMessage />, { confirmLabel: 'Got it!' }).then((confirmed) => {
        if (confirmed) {
          close(true);
          args?.onClick?.(e);
          // need a delay for OrchestrationProvider to update closed status and disable the beforeunload event to avoid double popups
          setTimeout(() => {
            window.open(href, target);
          }, 50);
        }
      });
    } else {
      args?.onClick?.(e);
      e.stopPropagation();
    }
  };

  return (
    <Link
      data-component='smartlink' //
      to={href}
      className={classNames(className, 'fix', 'pointer')}
      target={target}
      {...args}
      onClick={onClick}
    >
      {children}
    </Link>
  );
};
