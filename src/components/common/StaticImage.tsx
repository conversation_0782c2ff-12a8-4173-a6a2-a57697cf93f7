import { CanvasHTMLAttributes, CSSProperties, ImgHTMLAttributes, useEffect, useRef, useState } from 'react';
import PixelImage from '../../assets/images/pixel.png';
import { isValidImageSrc } from '../../utils/isValidImageSrc';

type StaticImageProps = ImgHTMLAttributes<HTMLImageElement> &
  CanvasHTMLAttributes<HTMLCanvasElement> & {
    imageFallback?: string;
    onUseFallback?(): void;
    src: string; // make src required for clarity
  };

export const StaticImage = ({ imageFallback = PixelImage, onUseFallback, src, ...props }: StaticImageProps) => {
  // Detect if the incoming “src” is (probably) a .gif URL.
  const isGif = /^(?!data:).*\.gif$/i.test(src);

  // We’ll keep track of which URL we’re actually trying to load:
  // — start with the incoming `src`
  // — if that errors, swap to fallback
  const [resolvedSrc, setResolvedSrc] = useState<string>(src);

  // Track whether the <img> has errored or loaded
  const [isError, setIsError] = useState<boolean>(false);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  // Refs for drawing the GIF onto canvas
  const $canvas = useRef<HTMLCanvasElement>(null);
  const $image = useRef<HTMLImageElement>(null);

  // Whenever `src` changes, reset everything:
  useEffect(() => {
    setResolvedSrc(src);
    setIsError(false);
    setIsLoaded(false);
  }, [src]);

  // Whenever resolvedSrc switches to fallback, call onUseFallback()
  const fallback = isValidImageSrc(imageFallback) ? imageFallback : PixelImage;
  useEffect(() => {
    if (resolvedSrc !== src && resolvedSrc === fallback) {
      onUseFallback?.();
    }
  }, [resolvedSrc, src, fallback, onUseFallback]);

  // When the <img> element loads successfully, mark isLoaded
  const handleImgLoad = () => setIsLoaded(true);

  // When <img> errors, mark error so we can swap to fallback
  const handleImgError = () => setIsError(true);

  // If isError becomes true, swap `resolvedSrc` to the fallback URL
  useEffect(() => {
    if (isError) {
      setResolvedSrc(fallback);
    }
  }, [isError, fallback]);

  // Draw onto canvas whenever:
  //   • it’s a GIF (`isGif === true`),
  //   • the image has loaded (`isLoaded === true`),
  //   • we have refs to both <img> and <canvas>.
  useEffect(() => {
    if (!isGif || !isLoaded) {
      return;
    }

    const $img = $image.current;
    const $cvs = $canvas.current;
    if (!$img || !$cvs) {
      return;
    }

    const w = $img.naturalWidth;
    const h = $img.naturalHeight;

    $cvs.width = w;
    $cvs.height = h;
    const ctx = $cvs.getContext('2d');
    if (!ctx) return;
    ctx.clearRect(0, 0, w, h);
    ctx.drawImage($img, 0, 0, w, h);
  }, [isGif, isLoaded, resolvedSrc]);

  // Styles to hide/show the two elements:
  // — if it’s not a gif or if we’re in fallback/errored mode, hide the canvas
  // — if it’s a gif && no error, hide the <img> (we draw it on canvas instead)
  const baseStyle: CSSProperties = (props.style as CSSProperties) || {};
  const showCanvas = isGif && !isError;
  const canvasStyle: CSSProperties = { ...baseStyle, display: !showCanvas ? 'none' : undefined };
  const imgStyle: CSSProperties = { ...baseStyle, display: showCanvas ? 'none' : undefined };

  return (
    <>
      {/* Canvas for GIFs. We keep it in the DOM whenever isGif===true */}
      <canvas ref={$canvas} {...(props as CanvasHTMLAttributes<HTMLCanvasElement>)} style={canvasStyle} />
      {/* <img> for everything else (regular image or fallback). */}
      <img ref={$image} src={resolvedSrc} onLoad={handleImgLoad} onError={handleImgError} {...(props as ImgHTMLAttributes<HTMLImageElement>)} style={imgStyle} />
    </>
  );
};
