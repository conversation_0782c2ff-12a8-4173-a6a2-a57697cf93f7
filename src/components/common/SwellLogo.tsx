import { SVGProps, useId, useRef } from 'react';

/*

Why you ask? Well, multiple SVGs on the same react space coming ang going
seem to interact and do bad stuff to the SVG. The id's are duplicated
across instances on an SVG and react/browser (something) can't handle it.
The result was the Swell "S" icon disappearing after a popup with the same SVG component appeared and disappeared.

*/
export const SwellLogo = (props: SVGProps<SVGSVGElement> & { id: string }) => {
  const uid = useId();
  const salt = useRef(`swelllogo_${uid}_${props.id}`);
  const id = (name: string) => `${salt.current}_${name}`;
  const url = (name: string) => `url(#${id(name)})`;

  return (
    <svg xmlns='http://www.w3.org/2000/svg' fill='none' className='swell-logo' viewBox='0 0 381 103' {...props}>
      <defs>
        <filter id={id('e')} width={131.906} height={130.668} x={5.228} y={8.847} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={13.344} />
        </filter>
        <filter id={id('g')} width={149.466} height={85.448} x={-31.136} y={-61.342} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={11.541} />
        </filter>
        <filter id={id('i')} width={72.462} height={170.313} x={61.065} y={-48.896} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={11.541} />
        </filter>
        <filter id={id('j')} width={131.898} height={103.914} x={-1.657} y={36.964} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={13.705} />
        </filter>
        <filter id={id('k')} width={79.889} height={78.647} x={-51.726} y={52.022} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={10.098} />
        </filter>
        <filter id={id('l')} width={156.564} height={98.852} x={-61.88} y={48.518} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={16.951} />
        </filter>
        <filter id={id('m')} width={100.918} height={79.078} x={-12.231} y={-12.796} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={10.098} />
        </filter>
        <filter id={id('n')} width={39.056} height={43.713} x={-7.308} y={41.238} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={4.688} />
        </filter>
        <filter id={id('o')} width={50.243} height={50.884} x={42.14} y={-6.95} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={6.492} />
        </filter>
        <filter id={id('p')} width={121.703} height={166.25} x={-61.33} y={-55.749} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
          <feGaussianBlur result='effect1_foregroundBlur_2925_377147' stdDeviation={15.688} />
        </filter>
        <linearGradient id={id('a')} x1={308.702} x2={377.969} y1={-167.807} y2={-39.398} gradientUnits='userSpaceOnUse'>
          <stop offset={0.12} stopColor='#FFD12D' />
          <stop offset={0.383} stopColor='#FCB950' />
          <stop offset={0.631} stopColor='#88D9D6' />
          <stop offset={0.906} stopColor='#00FCF1' />
        </linearGradient>
        <linearGradient id={id('b')} x1={308.702} x2={377.969} y1={-167.807} y2={-39.398} gradientUnits='userSpaceOnUse'>
          <stop offset={0.12} stopColor='#FFD12D' />
          <stop offset={0.383} stopColor='#FCB950' />
          <stop offset={0.631} stopColor='#88D9D6' />
          <stop offset={0.906} stopColor='#00FCF1' />
        </linearGradient>
        <linearGradient id={id('c')} x1={308.702} x2={377.969} y1={-167.807} y2={-39.398} gradientUnits='userSpaceOnUse'>
          <stop offset={0.12} stopColor='#FFD12D' />
          <stop offset={0.383} stopColor='#FCB950' />
          <stop offset={0.631} stopColor='#88D9D6' />
          <stop offset={0.906} stopColor='#00FCF1' />
        </linearGradient>
        <linearGradient id={id('f')} x1={62.343} x2={112.969} y1={41.671} y2={110.984} gradientUnits='userSpaceOnUse'>
          <stop offset={0.376} stopColor='#31BCB1' />
          <stop offset={0.733} stopColor='#97ECF1' />
        </linearGradient>
        <linearGradient id={id('h')} x1={62.169} x2={32.339} y1={-18.619} y2={-3.15} gradientUnits='userSpaceOnUse'>
          <stop stopColor='#65D4D2' />
          <stop offset={1} stopColor='#A0DDBD' />
        </linearGradient>
      </defs>
      <path
        fill='#fff'
        d='M25.544 102.799c13.838 0 27.117-7.269 27.117-23.763 0-12.58-9.086-17.053-17.053-19.709-9.365-3.075-12.72-5.032-12.72-7.827 0-2.097 2.237-3.774 6.43-3.774 6.989 0 11.741 3.075 16.913 6.29h1.678l-.839-19.85c-4.054-2.096-9.226-4.332-18.451-4.332-13.838 0-26.139 8.247-26.139 22.225 0 8.806 5.452 15.795 16.075 19.569 9.365 3.355 13.14 4.613 13.14 7.828 0 2.795-2.657 4.053-6.85 4.053-7.129 0-13.559-3.215-18.311-7.268H5.416L.663 96.089c5.87 4.194 14.118 6.71 24.88 6.71ZM265.417 102.785h22.647V.2h-22.647v102.585ZM194.16 102.799c9.645 0 17.332-1.678 27.816-6.99l.839-19.01h-2.656c-8.247 7.13-18.451 8.108-25.021 8.108-9.085 0-17.053-4.613-18.73-12.86h51.019v-6.29c0-17.612-10.763-35.923-35.084-35.923-19.989 0-36.203 15.655-36.203 35.923 0 20.967 16.633 37.042 38.02 37.042Zm-17.053-45.01c1.957-6.01 7.688-10.343 15.236-10.343 6.849 0 12.3 3.495 13.978 10.344h-29.214ZM235.102 102.8h22.647V.215h-22.647V102.8ZM84.42 102.788h5.172l17.299-41.504 17.367 41.504h5.172l30.931-72.945h-22.645l-12.374 33.678-13.485-33.678h-9.785L88.44 63.521 76.213 29.843H53.57l30.852 72.945Z'
      />
      <mask
        id={id('d')}
        width={69}
        height={103}
        x={0}
        y={0}
        maskUnits='userSpaceOnUse'
        style={{
          maskType: 'alpha',
        }}
      >
        <path fill={url('a')} d='M.617 68.793v.035c0 18.762 15.209 33.971 33.97 33.971 9.45 0 18-3.859 24.157-10.086L.617 63.095v5.698Z' />
        <path fill={url('b')} d='M.617 34.176a16.093 16.093 0 0 0 8.787 14.34l57.898 29.5a34.001 34.001 0 0 0 1.257-9.188v-.105h.001a17.66 17.66 0 0 0-9.643-15.735L2.147 24.062a33.96 33.96 0 0 0-1.53 10.114Z' />
        <path fill={url('c')} d='M68.56 34.214v4.63L11.146 9.59C17.242 3.775 25.498.205 34.588.205c18.761 0 33.97 15.21 33.97 33.971v.038h.002Z' />
      </mask>
      <g mask={url('d')} transform='translate(310)'>
        <path fill='#FDD02F' d='M-18.817-16.667h119.293v119.293H-18.817z' />
        <g filter={url('e')}>
          <path fill={url('f')} d='M64.514 35.538c12.229 0 31.604-.383 45.932 7.158v70.131H32.76c8.514-7.507 14.952-15.302 10.558-26.703-4.378-11.358-14.351-25.33-10.558-35.131 4.534-11.713 26.717-15.455 31.754-15.455Z' />
        </g>
        <g filter={url('g')}>
          <path fill={url('h')} d='M66.812.432C47.776 3.872 8.97-8.525-8.054-15.152v-23.109H95.249C93.702-26.796 85.847-3.007 66.812.432Z' />
        </g>
        <g filter={url('i')}>
          <path fill='#F06A9E' d='M84.578 36.26c-2.957-26.194 10.03-52.297 16.893-62.074h8.975l-2.64 124.15c-6.511-9.778-20.272-35.881-23.228-62.075Z' />
        </g>
        <g filter={url('j')}>
          <path fill='#DF9BEF' d='M25.885 113.468c-1.1-8.226 4.431-33.677 25.025-34.874 6.393-.371 11.628 0 19.602 0 6.638 0 30.453-15.887 32.228-14.077 1.762 1.795-22.48 22.643-22.48 34.587 0 6.153 3.685 7.296 4.42 9.877l-58.795 4.487Z' />
        </g>
        <g filter={url('k')}>
          <path fill='#F94B73' d='M7.967 101.459c-6.68-13.892-21.722-28.61-33.251-29.24l-6.245 38.253 39.496-9.013Z' />
        </g>
        <g filter={url('l')}>
          <path fill='#FE0C5E' d='M60.784 107.989c-26.06-6.077-75.963-25.058-84.265-25.57l-4.497 31.049 88.762-5.479Z' />
        </g>
        <g filter={url('m')}>
          <path fill='#FFB43A' d='M12.611 43.907c-7.21-7.267-4.446-24.102-2.163-31.612C12.244 4.244 19.1 8.298 33.16 9.388c14.06 1.09 35.33-6.177 35.33 2.907s-15.14 4.36-29.922 11.628C23.787 31.19 21.624 52.992 12.611 43.908Z' />
        </g>
        <g filter={url('n')}>
          <path fill='#FFD531' d='M2.069 58.447c5.833.206 11.14-5.136 13.066-7.832-1.476 6.753 3.575 18.281 6.718 24.032.194.313.368.622.518.927a53.182 53.182 0 0 1-.518-.927c-3.63-5.858-14.533-13.15-19.784-16.2Z' />
        </g>
        <g filter={url('o')}>
          <path fill='#FFD531' d='M55.123 11.233c5.834.173 14.988-2.493 16.897-5.2-1.478 6.954 3.976 18.918 7.121 24.468.1.158.187.308.258.45l-.258-.45c-2.271-3.567-11.834-11.024-17.088-7.429 0 0-4.478-10.433-6.93-11.839Z' />
        </g>
        <g filter={url('p')}>
          <path fill='#fff' d='M.817 7.39c1.748-11.663 17.1-22.25 28.179-22.506l-47.839-9.256-11.11 103.457c7.035.63 20.154-6.291 26.454-20.434C4.376 40.974-1.367 21.97.817 7.391Z' />
        </g>
      </g>
    </svg>
  );
};
