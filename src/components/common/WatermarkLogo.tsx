import { useState } from 'react';
import { SwellLogo } from './SwellLogo';

export const WatermarkLogo = () => {
  const [opacity, setOpacity] = useState(0.75);
  return (
    <a
      href='https://swell.life'
      target='_blank'
      onPointerEnter={() => setOpacity(1)}
      onPointerLeave={() => setOpacity(0.75)}
      style={{
        position: 'fixed', //
        top: 10,
        right: 10,
        zIndex: 999,
        width: 'min(90px, 25vw)',
        height: `min(${90 * 0.27}px, ${25 * 0.27}vw)`,
        opacity: opacity,
      }}
      rel='noreferrer'
    >
      <SwellLogo id='WatermarkLogo' />
    </a>
  );
};
