.bounce-loader-ball {
  --duration: 0.7s;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ccc;
  animation: sinY var(--duration);
  animation-timing-function: cubic-bezier(0.5, 0, 0.5, 1);
  animation-iteration-count: infinite;
  animation-direction: alternate;
}
[data-bs-theme="light"] .bounce-loader-ball {
  background: rgba(0, 0, 0, 0.3);
}
[data-bs-theme="dark"] .bounce-loader-ball {
  background: rgba(255, 255, 255, 0.3);
}

.bounce-loader-ball:nth-child(even) {
  animation-delay: var(--duration);
}

@keyframes sinY {
  to {
    transform: translateY(120%);
  }
}
