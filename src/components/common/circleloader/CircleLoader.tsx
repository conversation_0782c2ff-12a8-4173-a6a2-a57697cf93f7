import classNames from 'classnames';
import { CSSProperties } from 'react';

export const CircleLoader = ({ size, color, className }: { size?: number; color?: string; className?: string }) => {
  const thickness = 3;
  return (
    <div
      className={classNames(className, 'spinner-border')}
      role='status'
      style={
        {
          ...(color ? { color } : {}),
          ...(size
            ? {
                '--bs-spinner-width': `${size}px`, //
                '--bs-spinner-height': `${size}px`,
                '--bs-spinner-vertical-align': `${-thickness * 2}px`,
                '--bs-spinner-border-width': `${thickness}px`,
              }
            : {}),
        } as CSSProperties
      }
    >
      <span className='visually-hidden'>Loading...</span>
    </div>
  );
};
