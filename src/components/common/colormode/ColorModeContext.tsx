import { createContext } from 'react';
import { DEFAULT_COLOR_MODE } from '../../../framework/settings/DEFAULT_COLOR_MODE';
import { ColorMode } from '../../../models/models';

interface ColorModeContextType {
  mode: ColorMode;
  userMode: ColorMode;
  setMode: (mode: ColorMode) => void;
}

export const ColorModeContext = createContext<ColorModeContextType>({ mode: DEFAULT_COLOR_MODE, userMode: DEFAULT_COLOR_MODE, setMode: (c: ColorMode) => c });
