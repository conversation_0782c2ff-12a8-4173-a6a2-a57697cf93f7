import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DEFAULT_COLOR_MODE } from '../../../framework/settings/DEFAULT_COLOR_MODE';
import { ColorMode } from '../../../models/models';
import { applyColorMode, useSystemColorMode } from '../../../utils/colorMode';
import { ColorModeContext } from './ColorModeContext';

const isValidColorMode = (value: string | null): value is ColorMode => {
  return !!value && Object.values(ColorMode).includes(value as ColorMode);
};

interface ColorModeProviderProps {
  children: React.ReactNode;
  lockMode?: ColorMode;
}

export const ColorModeProvider = ({ children, lockMode }: ColorModeProviderProps) => {
  const [search] = useSearchParams();
  const nav = useNavigate();
  const systemMode = useSystemColorMode();

  const [userMode, setUserMode] = useState<ColorMode | null>(DEFAULT_COLOR_MODE);
  const [isResolved, setIsResolved] = useState(false);

  // Helper to calculate the current color mode
  const calculateMode = (): ColorMode => {
    if (lockMode) {
      return lockMode; // Use locked mode if provided
    }

    const searchMode = search.get('colormode');
    const resolvedSearchMode = searchMode && isValidColorMode(searchMode) ? (searchMode === ColorMode.AUTO ? systemMode : (searchMode as ColorMode)) : null;

    return resolvedSearchMode || (userMode === ColorMode.AUTO ? systemMode : userMode || systemMode);
  };

  const mode = calculateMode();

  // Load user mode from localStorage on mount
  useEffect(() => {
    if (lockMode) {
      return; // Skip loading localStorage if mode is locked
    }

    try {
      const localMode = localStorage.getItem('colormode');
      setUserMode(isValidColorMode(localMode) ? (localMode as ColorMode) : null);
    } catch {
      console.error('Failed to access localStorage');
      setUserMode(DEFAULT_COLOR_MODE);
    }
    setIsResolved(true);
  }, [lockMode]);

  // Sync user mode with localStorage when resolved
  useEffect(() => {
    if (lockMode || !isResolved) {
      return; // Skip syncing if mode is locked or not resolved
    }

    try {
      localStorage.setItem('colormode', userMode || DEFAULT_COLOR_MODE);
    } catch {
      console.error('Failed to save to localStorage');
    }
  }, [isResolved, userMode, lockMode]);

  // Apply the calculated mode
  useEffect(() => {
    applyColorMode(mode);
  }, [mode]);

  // Function to change the mode
  const changeMode = (newMode: ColorMode) => {
    if (lockMode) {
      // Color mode is locked and cannot be changed
      return;
    }

    setUserMode(newMode);

    if (search.has('colormode')) {
      const newSearchParams = new URLSearchParams(search);
      newSearchParams.delete('colormode');
      nav(`?${newSearchParams.toString()}`, { replace: true });
    }
  };

  return (
    <ColorModeContext.Provider
      value={{
        mode,
        userMode: lockMode || userMode || ColorMode.AUTO,
        setMode: changeMode,
      }}
    >
      {children}
    </ColorModeContext.Provider>
  );
};
