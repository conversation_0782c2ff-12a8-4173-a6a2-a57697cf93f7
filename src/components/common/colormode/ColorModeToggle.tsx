import { DarkMode, LightMode } from '@mui/icons-material';
import classNames from 'classnames';
import { CSSProperties, useEffect, useState } from 'react';
import { DEFAULT_COLOR_MODE } from '../../../framework/settings/DEFAULT_COLOR_MODE';
import { ColorMode } from '../../../models/models';
import { stopPropagation } from '../../../utils/stopPropagation';
import { useColorMode } from './useColorMode';

export const ColorModeToggle = () => {
  const { mode, setMode } = useColorMode();
  const [style, setStyle] = useState<CSSProperties>({});
  const [uiMode, setUIMode] = useState(DEFAULT_COLOR_MODE);

  useEffect(() => {
    if (mode === ColorMode.DARK) {
      setStyle({ left: '100%', transform: 'translateX(-100%)' });
    } else {
      setStyle({ left: 0, transform: '' });
    }
  }, [mode]);

  useEffect(() => {
    setUIMode(mode);
  }, [mode]);

  const toggleMode = () => {
    if (mode === ColorMode.DARK) {
      setUIMode(ColorMode.LIGHT);
      setMode(ColorMode.LIGHT);
    } else {
      setUIMode(ColorMode.DARK);
      setMode(ColorMode.DARK);
    }
  };

  return (
    <div onClick={stopPropagation}>
      <div className='d-flex align-items-center gap-3'>
        <div className='d-none'>{uiMode}</div>
        <div title={uiMode}>Dark Mode</div>
        <div className={classNames('rounded-pill p-1', uiMode === ColorMode.DARK ? 'bg-squash' : 'bg-grey-30')} style={{ width: 60 }} onClick={toggleMode}>
          <div className='position-relative d-flex gap-3'>
            <div className={classNames('rounded-circle position-relative', uiMode === ColorMode.DARK ? 'bg-black' : 'bg-white')} style={{ ...style, transition: 'all 0.3s', width: 27, height: 27 }}>
              <DarkMode className='absolute-center text-white' style={{ width: '70%', display: uiMode === ColorMode.DARK ? 'block' : 'none' }} />
              <LightMode className='absolute-center text-black' style={{ width: '70%', display: uiMode === ColorMode.LIGHT ? 'block' : 'none' }} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
