import React, { useEffect, useMemo, useState } from 'react';
import QRCode from 'react-qr-code';
import AppleImg from '../../../assets/images/appstore/apple-app-store-black.svg?url';
import GoogleImg from '../../../assets/images/appstore/google-play-black.svg?url';
import { AppLink } from '../../../framework/settings/AppLink';
import { OS } from '../../../models/models';
import { trackClickDownloadApp } from '../../../tracking/Tracking';
import { useOS } from '../../../utils/useOS';
import { useUTMParams } from '../../../utils/useUTMParams';
import { appendUTM } from '../../../utils/UTM.class';
import { PopupContainer, PopupMessage } from '../../popup/PopupContainer';
import { useDynamicDownloadPopup } from './useDynamicDownloadPopup';
// import './download-button.scss';

const DefaultMessage = () => (
  <p className='fw-normal'>
    Start your own Swellcast.
    <br />
    Reply, react, follow and subscribe to others.
  </p>
);

const DefaultQRCodeMessage = () => (
  <p className='fw-normal'>
    Customize your listening experience, <br />
    join conversations and <br />
    start your own Swellcast.
  </p>
);

const ButtonImage = {
  [OS.IOS]: AppleImg,
  [OS.ANDROID]: GoogleImg,
};

// this first automatically when the user has listened to some set time of audio has passed
// so on mobile we get a popup that can't go directly to the app store - as that would be rude

export const DynamicDownloadPopup = ({ close, message = <DefaultMessage />, context }: { close(): void; message?: React.ReactNode; context: string }) => {
  const os = useOS();
  const utmParams = useUTMParams();
  let $inner: React.ReactNode;

  switch (os) {
    case OS.ANDROID:
      $inner = (
        <PopupMessage
          showLogo={true} //
          title='Download the Swell App'
          description={message}
        >
          <GenericStoreButtonUI url={appendUTM(AppLink[os], utmParams, { asReferrer: true })} image={ButtonImage[os]} context={context} />
        </PopupMessage>
      );

      //   $inner = (
      //     <div className='d-flex flex-column gap-3 text-center'>
      //       <h3>Download the Swell App</h3>
      //       {message}
      //       <GenericStoreButtonUI url={UTM.modifyUrl(AppLink[os], true)} image={ButtonImage[os]} context={context} />
      //     </div>
      //   );
      break;

    case OS.IOS:
      $inner = (
        <PopupMessage
          showLogo={true} //
          title='Download the Swell App'
          description={message}
        >
          <GenericStoreButtonUI url={appendUTM(AppLink[os], utmParams, { asReferrer: false })} image={ButtonImage[os]} context={context} />
        </PopupMessage>
      );

      //   $inner = (
      //     <div className='d-flex flex-column gap-3 text-center'>
      //       <h3>Download the Swell App</h3>
      //       {message}
      //       <GenericStoreButtonUI url={UTM.modifyUrl(AppLink[os], false)} image={ButtonImage[os]} context={context} />
      //     </div>
      //   );
      break;

    case OS.UNKNOWN:
    case OS.WINDOWS_PHONE:
      $inner = <SwellQRCode message={message} context={context} />;
      break;

    case OS.SERVER:
      $inner = <div />;
      break;
  }
  return <PopupContainer close={close}>{$inner}</PopupContainer>;
};

export const DownloadAppButton = ({ showQRCodeInline = false, context }: { showQRCodeInline?: boolean; context: string }) => {
  const qrCodePopup = useDynamicDownloadPopup(context);
  const utmParams = useUTMParams();
  const os = useOS();
  const button = useMemo(() => {
    switch (os) {
      case OS.ANDROID:
        return <GenericStoreButtonUI url={appendUTM(AppLink[os], utmParams, { asReferrer: true })} image={ButtonImage[os]} context={context} />;
      case OS.IOS:
        return <GenericStoreButtonUI url={appendUTM(AppLink[os], utmParams, { asReferrer: false })} image={ButtonImage[os]} context={context} />;
      case OS.UNKNOWN:
      case OS.WINDOWS_PHONE:
        return showQRCodeInline ? (
          <SwellQRCode context={context} />
        ) : (
          <GenericDownloadButtonUI
            onClick={(e) => {
              e.stopPropagation();
              trackClickDownloadApp(AppLink[os], context);
              qrCodePopup.show();
            }}
          />
        );
      default:
        return null;
    }
  }, [os, showQRCodeInline, utmParams, context, qrCodePopup]);

  return <>{button}</>;
};

export const StoreAction = ({ children, context, className = '' }: { children: React.ReactNode; context: string; className?: string }) => {
  const os = useOS();
  const utmParams = useUTMParams();
  const isPopup = os !== OS.ANDROID && os !== OS.IOS;
  const [url, setUrl] = useState(AppLink.unknown);
  const qrCodePopup = useDynamicDownloadPopup(context);

  useEffect(() => {
    const asReferrer = os === OS.ANDROID ? true : false;
    setUrl(appendUTM(AppLink[os], utmParams, { asReferrer }));
  }, [os, utmParams]);

  const onClick: React.MouseEventHandler<HTMLAnchorElement> = (e) => {
    if (isPopup) {
      e.preventDefault();
      trackClickDownloadApp(AppLink[os], context);
      qrCodePopup.show();
    }
  };

  return (
    <a href={url} onClick={onClick} className={className}>
      {children}
    </a>
  );
};

const GenericStoreButtonUI = ({ url, image, context }: { url: string; image: string; context: string }) => (
  <a
    href={url}
    onClick={(e) => {
      e.stopPropagation();
      trackClickDownloadApp(url, context);
    }}
    target='_blank'
    rel='noreferrer'
    className='download-button'
    aria-label='download app'
    tabIndex={0}
    style={{
      backgroundImage: `url(${image})`,
      width: 155,
      height: 50,
    }}
  >
    &nbsp;
  </a>
);

const GenericDownloadButtonUI = ({ onClick }: { onClick?: React.MouseEventHandler<HTMLDivElement> }) => (
  <div className='download-button d-flex flex-column Xflex-center justify-content-center' onClick={onClick} role='button' aria-label='download app' tabIndex={0}>
    <span className='h5-normal'>Download&nbsp;the</span>
    <div className='fs-3 fw-bold'>Swell app</div>
  </div>
);

const SwellQRCode = ({ message = <DefaultQRCodeMessage />, context }: { message?: React.ReactNode; context: string }) => {
  const utmParams = useUTMParams();
  const [url, setUrl] = useState('');

  useEffect(() => {
    setUrl(appendUTM(AppLink.unknown, utmParams, { asReferrer: false }));
  }, [utmParams]);

  return (
    <a className='d-inline-block fix' href={url} onClick={() => trackClickDownloadApp(AppLink.unknown, context)} target='_blank' rel='noreferrer'>
      <div className='d-flex gap-3'>
        <div style={{ flex: '1 1 120px', borderRadius: 5, border: '1px solid black', height: 'min-content' }} className='p-3 bg-white'>
          <QRCode value={url} size={110} />
        </div>
        <div className='d-flex flex-column justify-content-center gap-2 text-start'>
          <h2 className='m-0'>
            Scan to download <br />
            the Swell App
          </h2>
          {message}
        </div>
      </div>
    </a>
  );
};
