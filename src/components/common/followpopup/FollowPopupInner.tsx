import { useEffect } from 'react';
import { useSwell } from '../../../api/gql.loadSwellById';
import { FollowingStatus } from '../../../generated/graphql';
import { RouteParams } from '../../../models/models';
import { getAuthorFullname } from '../../../utils/swell-utils';
import { PopupContainer, PopupMessage } from '../../popup/PopupContainer';
import { usePopup } from '../../popup/usePopup';
import { SwellButton } from '../../swellbutton/SwellButton';
import { CircleLoader } from '../circleloader/CircleLoader';
import { FollowAction } from '../swellcastui/FollowButton';

export const FollowPopupInner = ({ params }: { params: RouteParams }) => {
  const swell = useSwell(params);
  const popup = usePopup();
  const ownerAlias = swell.isSuccess ? `@${swell.data.swellcast?.owner?.alias}` : '';
  const ownerName = swell.isSuccess ? getAuthorFullname(swell.data.swellcast?.owner) : '';

  useEffect(() => {
    if (swell.isError || (!swell.isSuccess && !swell.isFetching)) {
      popup.close();
    }
  }, [swell.isError, swell.isSuccess, swell.isFetching, popup]);

  if (swell.isSuccess) {
    return (
      <PopupContainer close={popup.close}>
        <PopupMessage
          showLogo={true}
          title='Like what you hear?'
          description={
            <>
              Get updated when {ownerName} <strong>{ownerAlias}</strong> releases new&nbsp;content.
            </>
          }
        >
          <FollowAction alias={swell.data.swellcast?.owner?.alias}>
            {(followState) => (
              <SwellButton.Primary className='px-4 py-2 fs-4'>
                {followState === FollowingStatus.Unfollowed ? 'Follow' : 'Unfollow'} {ownerAlias}
              </SwellButton.Primary>
            )}
          </FollowAction>
        </PopupMessage>
      </PopupContainer>
    );
  }

  return (
    <PopupContainer>
      <div style={{ width: 250, height: 250 }} className='flex-center'>
        <CircleLoader />
      </div>
    </PopupContainer>
  );
};
