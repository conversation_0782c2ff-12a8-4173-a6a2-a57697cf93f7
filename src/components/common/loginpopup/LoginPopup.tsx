import { useEffect } from 'react';
import { useEnv } from '../../../framework/useEnv';
import { useLocalStorage } from '../../../utils/useLocalStorage';
import { useAuth } from '../../login/useAuth';
import { usePopup } from '../../popup/usePopup';
import { LoginPopupInner } from './LoginPopupInner';

export const useLoginPopup = () => {
  const auth = useAuth();
  const env = useEnv();
  const popup = usePopup();
  const store = useLocalStorage<{ popped: boolean; timestamp: number }>('login-popup', { popped: false, timestamp: 0 });
  const TIME_BETWEEN_LOGIN_POPUP = parseInt(env?.TIME_BETWEEN_LOGIN_POPUP ?? '86400000');
  const TIME_BEFORE_LOGIN_POPUP = parseInt(env?.TIME_BEFORE_LOGIN_POPUP ?? '10000');

  useEffect(() => {
    if (auth.isReady && !auth.loggedIn) {
      const lastPop = Date.now() - store.data.timestamp;
      if (lastPop > TIME_BETWEEN_LOGIN_POPUP) {
        const timer = setTimeout(() => {
          store.setData({ popped: true, timestamp: Date.now() });
          if (!auth.loggedIn) {
            popup.showPopup(<LoginPopupInner />);
          }
        }, TIME_BEFORE_LOGIN_POPUP);

        return () => {
          clearTimeout(timer);
        };
      }
    }
  }, [auth.isReady]);
};
