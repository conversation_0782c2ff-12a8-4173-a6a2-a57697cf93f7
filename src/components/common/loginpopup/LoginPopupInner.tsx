import { dataLayer } from '../../../tracking/Tracking';
import { PopupContainer, PopupMessage } from '../../popup/PopupContainer';
import { SwellLink } from '../../swellbutton/SwellButton';

export const LoginPopupInner = () => {
  const onClick: React.MouseEventHandler<HTMLAnchorElement> = (e) => {
    e.stopPropagation();
    dataLayer({ event: 'signin', context: 'popup' });
  };

  return (
    <PopupContainer close={() => null}>
      <PopupMessage showLogo={true} description='Sign up to follow or subscribe to any Swellcast and receive email when there is new content.'>
        <SwellLink.Primary className='px-4 btn-lg' to='/me/' onClick={onClick} reloadDocument>
          Sign Up
        </SwellLink.Primary>
      </PopupMessage>
    </PopupContainer>
  );
};
