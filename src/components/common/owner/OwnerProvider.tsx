import { ReactNode } from 'react';
import { OwnerContext } from './OwnerContext';
/*

The sole purpose of this is that we do not want to show the Pinned icon on swell cards that are not in the context of the owner's swellcast.
So, we wrap this provider around the swellcast view only and each card checks for this.

*/
export const OwnerProvider = ({ alias, children }: { alias: string; children: ReactNode }) => {
//   const mode = useAudioMode();

//   useEffect(() => {
//     if (alias) {
//       mode.setTrackingData({ swellcastAlias: alias });
//     }
//   }, [alias]);

  return <OwnerContext.Provider value={{ alias }}>{children}</OwnerContext.Provider>;
};
