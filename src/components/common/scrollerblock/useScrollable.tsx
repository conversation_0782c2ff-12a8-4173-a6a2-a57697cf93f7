import { throttle } from 'lodash';
import { RefObject, useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollRestoreCache } from './ScrollRestoreCache';

const getClosestIndex = (scrollLeft: number, rects: number[]) => {
  if (rects.length === 0) return 0;

  let closestIndex = -1;
  let closestDistance = Infinity;

  for (let i = 0; i < rects.length; i++) {
    const distance = Math.abs(rects[i] - scrollLeft);
    if (distance < closestDistance) {
      closestDistance = distance;
      closestIndex = i;
    }
  }
  return closestIndex;
};

export const useScrollable = ($scroller: RefObject<HTMLDivElement>) => {
  const uid = $scroller?.current?.id ?? '';
  const [canScroll, setCanScroll] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [contentUpdated, setContentUpdated] = useState(0);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [childPositions, setChildPositions] = useState<number[]>([]);

  const scrollTo = (i: number) => {
    if (!$scroller.current) return;
    if (i === 0) {
      $scroller.current.scroll({ left: 0, behavior: 'smooth' });
    } else if (i === childPositions.length - 1) {
      $scroller.current.scroll({ left: $scroller.current.scrollWidth, behavior: 'smooth' });
    } else {
      const child = $scroller.current.children[i] as HTMLElement;
      $scroller.current.scrollTo({ left: child.offsetLeft, behavior: 'smooth' });
    }
  };

  const updateChildren = useCallback(() => {
    if (!$scroller.current) return;
    const children = Array.from($scroller.current.children) as HTMLDivElement[];
    setChildPositions(children.map((child) => child.offsetLeft));
  }, [$scroller]);

  const updateContainer = useCallback(() => {
    if (!$scroller.current) return;

    const { scrollLeft, scrollWidth, offsetWidth } = $scroller.current;

    const newCanScroll = scrollWidth > offsetWidth;
    const newCanScrollLeft = scrollLeft > 0;
    const newCanScrollRight = scrollLeft + offsetWidth < scrollWidth - 1;
    const newIndex = getClosestIndex(scrollLeft, childPositions);

    setCanScroll((prev) => (prev !== newCanScroll ? newCanScroll : prev));
    setCanScrollLeft((prev) => (prev !== newCanScrollLeft ? newCanScrollLeft : prev));
    setCanScrollRight((prev) => (prev !== newCanScrollRight ? newCanScrollRight : prev));
    setCurrentIndex((prev) => (prev !== newIndex ? newIndex : prev));

    if (uid) {
      ScrollRestoreCache[uid] = scrollLeft;
    }
  }, [$scroller, uid, childPositions]);

  const handleMutation = (mutationsList: MutationRecord[]) => {
    const found = mutationsList.find((mutation) => mutation.type === 'childList');
    if (found) {
      setContentUpdated((n) => n + 1);
    }
  };

  const handleResize = () => {
    setContentUpdated((n) => n + 1);
  };

  const handleScroll = useMemo(() => throttle(updateContainer, 100), [updateContainer]);

  useEffect(() => {
    if (!$scroller.current) return;
    const observer = new MutationObserver(handleMutation);
    observer.observe($scroller.current, { childList: true });
    return () => observer.disconnect();
  }, [$scroller]);

  useEffect(() => {
    if (!$scroller.current) return;
    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe($scroller.current);
    return () => resizeObserver.disconnect();
  }, [$scroller]);

  useEffect(() => {
    const container = $scroller.current;
    if (!container) return;

    if (uid) {
      if (!(uid in ScrollRestoreCache)) {
        ScrollRestoreCache[uid] = 0;
      }
      container.scrollLeft = ScrollRestoreCache[uid];
    }

    container.addEventListener('scroll', handleScroll);
    handleScroll(); // initialize

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [$scroller, handleScroll, uid]);

  useEffect(() => {
    handleScroll(); // update scroll state after content changes
  }, [contentUpdated, handleScroll]);

  useEffect(() => {
    const raf = requestAnimationFrame(updateChildren);
    return () => cancelAnimationFrame(raf);
  }, [contentUpdated, updateChildren]);

  const onClickLeft = () => {
    if ($scroller.current) {
      const i = getClosestIndex($scroller.current.scrollLeft - $scroller.current.offsetWidth, childPositions);
      scrollTo(i);
    }
  };

  const onClickRight = () => {
    if ($scroller.current) {
      const i = getClosestIndex($scroller.current.scrollLeft + $scroller.current.offsetWidth, childPositions);
      scrollTo(i);
    }
  };

  return {
    onClickLeft,
    onClickRight,
    canScroll,
    canScrollLeft,
    canScrollRight,
    currentIndex,
    scrollTo,
  };
};
