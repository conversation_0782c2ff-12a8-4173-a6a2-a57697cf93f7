import { dataLayer } from '../../../tracking/Tracking';
import { useIsCurrentUser } from '../../../utils/useIsCurrentUser';
import { SwellButton } from '../../swellbutton/SwellButton';
import { SmartLink } from '../SmartLink';

export const AccountButton = ({ alias }: { alias?: string | null }) => {
  const isCurrentUser = useIsCurrentUser(alias);

  const trackClick = () => {
    dataLayer({
      event: 'view_profile',
      context: 'swellcast',
      swellcastAlias: alias,
      userAlias: alias,
    });
  };

  if (!alias) {
    return null;
  }

  if (isCurrentUser) {
    return (
      <SmartLink onClick={trackClick} to='/me/' reloadDocument={true}>
        <SwellButton.Secondary>Account</SwellButton.Secondary>
      </SmartLink>
    );
  }

  return null;
};
