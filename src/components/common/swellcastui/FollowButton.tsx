import { ReactNode, useCallback } from 'react';
import { useProfile } from '../../../api/gql.getProfilePage';
import { useSwell } from '../../../api/gql.loadSwellById';
import { FollowingStatus } from '../../../generated/graphql';
import { dataLayer } from '../../../tracking/Tracking';
import { useIsCurrentUser } from '../../../utils/useIsCurrentUser';
import { SwellButton } from '../../swellbutton/SwellButton';
import { SmartLink } from '../SmartLink';

export const FollowAction = ({ alias: manualAlias = '', canonicalId, children }: { alias?: string | null; canonicalId?: string; children?: (followingState: FollowingStatus | null) => ReactNode }) => {
  const swell = useSwell({ canonicalId });
  const alias = canonicalId ? swell.data?.swellcast?.owner?.alias ?? '' : manualAlias;
  const profile = useProfile({ alias });
  const isCurrentUser = useIsCurrentUser(alias);
  const followingState = profile.isSuccess ? profile.data.followingState : null;
  const action = followingState === FollowingStatus.Followed ? 'following' : followingState === FollowingStatus.Unfollowed ? 'follow' : null;
  const searchParams = { op: action, sa: alias };

  const trackClick = useCallback(() => {
    if (profile.isSuccess) {
      dataLayer({
        event: `swellcast-${action}`,
        swellcastId: profile.data.id,
        swellcastAlias: profile.data.name,
      });
    }
  }, [profile.isSuccess]);

  if (!alias) {
    return null;
  }

  if (profile.isError) {
    return null;
  }

  if (isCurrentUser) {
    return null;
  }

  if (followingState === FollowingStatus.Followed) {
    return children ? children(followingState) : <SwellButton.Following />;
  }

  if (followingState === FollowingStatus.Unfollowed) {
    return (
      <SmartLink onClick={trackClick} to='/me/' searchParams={searchParams} reloadDocument={true} title={`Follow @${alias}`}>
        {children ? children(followingState) : <SwellButton.Follow />}
      </SmartLink>
    );
  }

  return <SwellButton.Loading />;
};

export const FollowButton = ({ alias, canonicalId }: { alias?: string | null; canonicalId?: string }) => {
  if (!alias) return null;
  return (
    <FollowAction alias={alias} canonicalId={canonicalId}>
      {(followState) => (followState === FollowingStatus.Unfollowed ? <SwellButton.Follow /> : <SwellButton.Following />)}
    </FollowAction>
  );
};
