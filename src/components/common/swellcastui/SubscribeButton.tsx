import { useCallback } from 'react';
import { useProfile } from '../../../api/gql.getProfilePage';
import { useSwell } from '../../../api/gql.loadSwellById';
import { SwellIcons } from '../../../assets/icons/SwellIcons';
import { buildUrl } from '../../../framework/buildUrl';
import { SubscriptionStatus } from '../../../generated/graphql';
import { dataLayer } from '../../../tracking/Tracking';
import { useIsCurrentUser } from '../../../utils/useIsCurrentUser';
import { useIsWidget } from '../../../utils/useIsWidget';
import { SwellButton } from '../../swellbutton/SwellButton';
import { SmartLink } from '../SmartLink';

export const SubscribeButton = ({ alias: manualAlias, canonicalId }: { alias?: string | null; canonicalId?: string }) => {
  const swell = useSwell({ canonicalId });
  const alias = (canonicalId ? swell.data?.swellcast?.owner?.alias : manualAlias) ?? '';
  const profile = useProfile({ alias });
  const isCurrentUser = useIsCurrentUser(alias);
  const subscriptionState = profile?.data?.subscriptionState ?? SubscriptionStatus.Disabled;
  const canSubscribe = profile?.data?.canSubscribe ?? false;
  const searchParams = { op: 'subscribe', sa: alias };
  const unlockBtnTxt = swell?.data?.unlockBtnTxt;
  const isWidget = useIsWidget();

  const onClickSubscribe = useCallback(() => {
    if (profile.isSuccess) {
      dataLayer({
        event: 'swellcast-subscribe',
        swellcastId: profile.data.id,
        swellcastAlias: profile.data.name,
      });
    }
  }, [profile.isSuccess]);

  const onClickUnlock = useCallback(() => {
    if (swell.isSuccess) {
      dataLayer({
        event: `swellcast-unlock`, //
        swellcastId: swell.data.swellcast?.id,
        swellcastAlias: swell.data.swellcast?.name,
      });
      const url = buildUrl({
        href: window.location.origin,
        pathname: `/me/`,
        searchParams: { op: 'subscribe', sa: alias ?? '', lid: canonicalId ?? '' },
      });
      if (isWidget) {
        window.open(url, '_blank');
      } else {
        window.location.href = url;
      }
    }
  }, [swell.isSuccess, isWidget, alias]);

  if (!alias) {
    return null;
  }

  if (profile.isError) {
    return null;
  }

  if (isCurrentUser) {
    return null;
  }

  if (unlockBtnTxt) {
    return <SwellButton.Unlock onClick={onClickUnlock}>{unlockBtnTxt}</SwellButton.Unlock>;
  }

  if (!canSubscribe) {
    return null;
  }

  if (subscriptionState === SubscriptionStatus.Disabled) {
    return null;
  }

  if (subscriptionState === SubscriptionStatus.Subscribed) {
    return (
      <SmartLink onClick={onClickSubscribe} to='/me/' searchParams={searchParams} reloadDocument={true} title={`Unsubscribe from @${alias}`}>
        <SwellButton.Blue Icon={SwellIcons.Check}>Subscribed</SwellButton.Blue>
      </SmartLink>
    );
  }

  if (subscriptionState === SubscriptionStatus.NotSubscribed || subscriptionState === SubscriptionStatus.Enabled) {
    return (
      <SmartLink onClick={onClickSubscribe} to='/me/' searchParams={searchParams} reloadDocument={true} title={`Subscribe to @${alias}`}>
        <SwellButton.Blue Icon={SwellIcons.Star}>Subscribe</SwellButton.Blue>
      </SmartLink>
    );
  }

  return null;
};
