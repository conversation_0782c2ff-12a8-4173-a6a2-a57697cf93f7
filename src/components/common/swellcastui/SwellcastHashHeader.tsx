import { useMemo } from 'react';
import { useProfile } from '../../../api/gql.getProfilePage';
import { getOwnerLink } from '../../../utils/swell-utils';
import { safeDecodeURIComponent, useRouteParams } from '../../../utils/useRouteParams';
import { HashtagLink } from '../../swell-card/CardDescription';
import { HR } from '../HR';
import { SmartLink } from '../SmartLink';

export const SwellcastHashHeader = ({ alias }: { alias: string }) => {
  const params = useRouteParams();
  const profile = useProfile({ alias });
  const hash = useMemo(() => safeDecodeURIComponent(params?.hash || ''), [params]);

  if (!params?.hash) return null;

  if (profile.isSuccess) {
    const swellcast = profile.data;

    return (
      <div className='mx-auto grid-pad-x d-flex flex-column max-width-sections'>
        <HR className='my-4' />
        <p className='mb-1'>
          Swells by{' '}
          <SmartLink to={getOwnerLink(swellcast)} className='fix text-hover-underline h5'>
            @{swellcast.owner?.alias}
          </SmartLink>{' '}
          tagged
        </p>
        <div>
          <HashtagLink to={params.hash} text={'#' + hash} className='fix text-hover-underline h2' title={`Search Swell for ${params?.hash ?? ''}`} />
        </div>
        <br />
      </div>
    );
  }
};
