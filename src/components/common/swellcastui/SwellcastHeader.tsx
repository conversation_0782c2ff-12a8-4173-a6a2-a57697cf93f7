import torso from '@images/avatar-torso-opt.svg?url';
import { useMemo, useState } from 'react';
import { useProfile } from '../../../api/gql.getProfilePage';
import { OpenApiProfileWithSectionsResponse, PodcastAppsResponse, PodcastSubType } from '../../../generated/graphql';
import { dataLayer } from '../../../tracking/Tracking';
import { formatFollowers, toBgUrl } from '../../../utils/Utils';
import { getAuthorFullname, getOwnerLink, getSwellcastImage } from '../../../utils/swell-utils';
import { ShareSwellcastButton } from '../../embed-code/ShareSwellcastButton';
import { NewSwellButton } from '../../player/AudioRecorder/buttons/NewSwellButton';
import { SwellDescription } from '../../swell-card/CardDescription';
import { Mugshot } from '../Mugshot';
import { SmartLink } from '../SmartLink';
import { AccountButton } from './AccountButton';
import { FollowButton } from './FollowButton';
import { SubscribeButton } from './SubscribeButton';

const SwellcastHeaderWidth = 700;

const SwellcastMugshot = ({ swellcast }: { swellcast: OpenApiProfileWithSectionsResponse }) => (
  <SmartLink to={getOwnerLink(swellcast)} className='position-relative'>
    <Mugshot size={110} image={swellcast?.owner?.image ?? torso} alt={`@${swellcast.owner?.alias}`} thickness={0} className='shadow' isPro={swellcast?.podcastSubType === PodcastSubType.Pro} />
  </SmartLink>
);

const FollowersCount = ({ alias }: { alias?: string | null }) => {
  const profile = useProfile({ alias });
  const followersCount = profile.data?.followersCount ?? 0;
  const showFollowersCount = followersCount > 0;

  if (!alias) {
    return null;
  }

  if (showFollowersCount) {
    return (
      <div className='d-flex flex-column justify-content-between'>
        <div className='fs-3'>{formatFollowers(followersCount)}</div>
        <div className='fs-5 fw-bold'>Followers</div>
      </div>
    );
  }

  return null;
};

export const SwellcastHeader = ({ alias }: { alias: string }) => {
  const profile = useProfile({ alias });

  if (profile.isError) {
    return null;
  }

  if (profile.isSuccess) {
    const swellcast = profile.data;

    return (
      <div className='position-relative'>
        <div>
          <div
            className='fade-mask-bottom absolute-fill'
            style={{
              height: 'inherit',
              backgroundImage: toBgUrl(getSwellcastImage(swellcast)), //
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              filter: 'blur(5px) contrast(0.5) opacity(0.8)',
              transform: 'scale(1)',
              transformOrigin: 'center',
            }}
          />
          <div className='max-width-sections mx-auto position-relative' style={{ height: 'inherit' }}>
            <div
              className='fade-mask-bottom absolute-fill'
              style={{
                height: 'inherit',
                backgroundImage: toBgUrl(getSwellcastImage(swellcast)), //
                backgroundSize: 'cover',
              }}
            ></div>

            <div className='absolute-fill position-relative d-flex flex-column gap-3 w-100 justify-content-between grid-pad' style={{ minHeight: 'min(40vh, 360px)' }}>
              <div className='d-flex flex-wrap w-100 gap-2 justify-content-between'>
                <FollowButton alias={alias} />
                <SubscribeButton alias={alias} />
                <AccountButton alias={alias} />
                <div className='flex-grow-1' />
                <ShareSwellcastButton alias={alias} />
              </div>
              <div className='d-flex gap-3'>
                <div>
                  <SwellcastMugshot swellcast={swellcast} />
                </div>
                <div className='pt-2 d-flex flex-column gap-2'>
                  <SmartLink to={getOwnerLink(swellcast)}>
                    <div className='fs-2 fw-bold Xtext-mode text-shadow-mode text-break'>{getAuthorFullname(swellcast?.owner)}</div>
                    <div className='fs-4 Xtext-mode text-shadow-mode fw-light opacity-75'>{swellcast?.name}</div>
                  </SmartLink>
                  <div className='d-flex flex-wrap gap-3'>
                    <FollowersCount alias={swellcast.owner?.alias} />
                  </div>
                  <SwellDescription description={swellcast.description} username={swellcast.owner?.alias} className='text-break text-mode mb-1' />
                  <NewSwellButton alias={alias} postIn={swellcast.id} />
                </div>
              </div>
            </div>
          </div>
        </div>
        <PodcastIcons alias={alias} />
      </div>
    );
  }
};

const PodcastIcons = ({ alias }: { alias?: string | null }) => {
  const profile = useProfile({ alias });
  const podcasts: PodcastAppsResponse[] = useMemo(() => {
    if (profile.isSuccess && profile.data?.podcastData) {
      return profile.data.podcastData
        .filter((p) => p?.apps) // Ensure apps is not null or undefined
        .flatMap((p) => p.apps ?? []); // Flatten the apps arrays, defaulting to empty array if null or undefined
    }
    return [];
  }, [profile.isSuccess, profile.data]);

  if (!podcasts.length) {
    return null;
  }

  if (!alias) {
    return null;
  }

  return (
    <div className='mx-auto p-3 position-relative' style={{ width: SwellcastHeaderWidth, maxWidth: '100%' }}>
      <div className='d-flex justify-content-center align-items-center gap-3'>
        <p className='fw-bold mb-0'>Listen&nbsp;on</p>
        <div className='d-flex gap-2 flex-wrap'>
          {podcasts.map((a) => (
            <PodcastIcon key={`${a.name}-podcast-${a.id}`} a={a} swellcastAlias={alias} />
          ))}
        </div>
      </div>
    </div>
  );
};

const PodcastIcon = ({ a, swellcastAlias }: { a: PodcastAppsResponse; swellcastAlias: string }) => {
  const [hasError, setHasError] = useState(false);
  const onError = () => {
    setHasError(true);
  };
  const onClick = () => {
    dataLayer({ event: 'podcast-swellcast', context: a.name, swellcastAlias });
  };
  if (hasError) {
    return null;
  }

  return (
    <SmartLink to={a.url} target='_blank' title={a.name} className='p-2 bg-grey-10 rounded-1' onClick={onClick}>
      <img className='podcast-icon-img' onError={onError} src={a.imageUrl} />
    </SmartLink>
  );
};
