@use '../../../assets/css/base' as *;

@media only screen and (max-width: 767px) {
  [data-component='swellcastheader'] {
    .swellcastheader-content {
      border-radius: 0 !important;
      aspect-ratio: 920/720;
    }
  }
}

@media only screen and (min-width: 768px) {
  [data-component='swellcastheader'] {
    .swellcastheader-content {
      aspect-ratio: 1360/720;
    }
  }
}

.swellcastheader-content {
  @extend .rounded-md-bottom, .bg-black;
  position: relative;
  overflow: hidden;
}

[data-bs-theme="dark"] .swellcastheader-content {
  background: #000;
}

[data-bs-theme="light"] .swellcastheader-content {
  background: #fff !important;
}

.swellcastheader-bgimage {
  position: absolute;
  inset: 0;
  background-size: cover;
  background-position: center;
  -webkit-mask-image: linear-gradient(black 0%, transparent 99%);
  mask-image: linear-gradient(black 0%, transparent 99%);
  opacity: 0.75;
}

.expand-buttons button,
.expand-buttons a {
  width: 100%;
}

.bg-blur {
  backdrop-filter: blur(5px) brightness(150%);
  -webkit-backdrop-filter: blur(5px) brightness(150%);
}

.podcast-icon-img {
  height: 30px;
}
