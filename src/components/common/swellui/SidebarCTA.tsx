import { SwellButton } from '../../swellbutton/SwellButton';
import { StoreAction } from '../downloadapp/DownloadAppButton';

export const SidebarCTA = ({context}:{context:string}) => {
  return (
    <div className='text-center' aria-label='complementary' role='complementary'>
      <div className='bg-grey-10 rounded-3 p-3' style={{ maxWidth: 300 }}>
        <h2>Get the App</h2>
        <p className='fs-4'>Download the Swell app for editing, moderation, and additional features.</p>
        <StoreAction context={context} className='fix text-center'>
          <SwellButton.Secondary className='mx-auto'>Download&nbsp;the&nbsp;App</SwellButton.Secondary>
        </StoreAction>
      </div>
    </div>
  );
};
