import { useEffect, useMemo, useRef } from 'react';
import { OpenApiSwellResponseEnhanced } from '../../../api/gql.loadSwellById';
import { ReactState } from '../../../generated/graphql';
import { ReactType } from '../../../models/models';
import { useTextFile } from '../../../utils/loadTextFile';
import { getElementY, scrollWindowTo } from '../../../utils/scrollWindowTo';
import { getAuthorLink, getSwellContentType, hasArticleImage, isTextSwell } from '../../../utils/swell-utils';
import { isVideoFile } from '../../../view/sound/media/hooks/isVideoFile';
import { ShareMasterButton } from '../../embed-code/ShareMasterButton';
import { ImageViewer } from '../../imageviewer/ImageViewer';
import { useAuth } from '../../login/useAuth';
import { AudioQuery } from '../../player/AudioPlayer/IAudioPlayer';
import { ReplyButton } from '../../player/AudioRecorder/buttons/ReplyButton';
import { DynamicWave } from '../../player/WaveUI/WaveRenderer';
import { useTrack } from '../../player/WaveUI/useTrack';
import { FormatCopy, SwellDescription } from '../../swell-card/CardDescription';
import { EditButton } from '../../swell-card/EditButton';
import { LikeButton } from '../../swell-card/LikeButton';
import { SwellCategories } from '../../swell-card/SwellCategories';
import { TrackCopy } from '../../swell-card/TrackInfo';
import { Snippet } from '../../swell-card/snippet/Snippet';
import { SwellButton } from '../../swellbutton/SwellButton';
import { DynamicMugshot } from '../Mugshot';
import { SmartLink } from '../SmartLink';
import { FollowButton } from '../swellcastui/FollowButton';
import { SubscribeButton } from '../swellcastui/SubscribeButton';
import { SwellUnlockMessage } from './SwellUnlockMessage';

export const SwellMaster = ({ swell }: { swell: OpenApiSwellResponseEnhanced }) => {
  const auth = useAuth();
  const canonicalId = swell.canonicalId ?? '';
  const swellId = swell.id ?? '';
  const authorLink = getAuthorLink(swell.author);
  const duration = swell.audio?.duration ?? 0;
  const hasImages = hasArticleImage(swell) || isVideoFile(swell.audio!.url!);
  const totalReactions = swell?.reactions?.reduce((n, r) => n + (r.count ?? 0), 0) ?? 0;
  const pressState = swell?.reactions?.find((r) => r.reaction === ReactType.HEART)?.pressState ?? ReactState.Notpressed;
  const showEditButton = auth.loggedIn && auth.user?.alias.toLowerCase() === swell.author?.alias?.toLowerCase();
  const audioQuery: AudioQuery = { playlistId: canonicalId, trackId: swellId };
  const playToggle = useTrack(audioQuery);
  const isText = isTextSwell(swell);
  const $el = useRef<HTMLDivElement>(null);
  const contentType = useMemo(() => getSwellContentType(swell), [swell]);

  useEffect(() => {
    if ($el.current && playToggle.active) {
      scrollWindowTo(getElementY($el.current));
    }
  }, [playToggle.active]);

  return (
    <div ref={$el} data-swellid={swellId} data-component='swellmaster' className={`d-flex flex-column gap-3 card-pad`} onClick={playToggle.onClick}>
      <div className='d-flex w-100 align-items-center justify-content-between gap-2'>
        <SmartLink to={authorLink}>
          <DynamicMugshot active={playToggle.active} image={swell.author?.image} size={80} alt={`@${swell.author?.alias}`} />
        </SmartLink>
        <div className='d-flex flex-wrap w-100 align-items-center justify-content-between gap-2'>
          <div className='flex-grow-1 overflow-hidden'>
            <TrackCopy author={swell.author} createdOn={swell.createdOn} duration={duration} />
          </div>
          <div className='d-flex flex-wrap gap-2'>
            <FollowButton alias={swell.author?.alias} />
            <SubscribeButton alias={swell.author?.alias} />
          </div>
        </div>
      </div>

      <div className='fs-3 fw-bold text-break d-flex align-items-center gap-2'>
        {swell?.masterRef?.subscriptionOnly ? <SwellButton.Premium /> : null}
        {swell?.isPanel ? <SwellButton.Panel /> : null}
        <FormatCopy text={swell.title} />
      </div>

      <SwellCategories swell={swell} />

      <SwellUnlockMessage canonicalId={canonicalId} />

      {hasImages ? <ImageViewer swell={swell} className='rounded' /> : null}

      {isText ? null : <DynamicWave active={playToggle.active} audioQuery={audioQuery} />}

      {isText ? null : <Snippet snippet={swell.snippet} className='fs-4' />}

      {isText ? <TextSwellContent swell={swell} /> : null}

      {isText ? null : <SwellDescription description={swell.description} />}

      {/* <GeneralPlayToggle canonicalId={swell.canonicalId} replyId={swell.id} /> */}

      <div className='d-flex justify-content-between'>
        <div className='d-flex gap-2'>
          <LikeButton
            totalReactions={totalReactions} //
            canonicalId={canonicalId}
            pressState={pressState}
            tracking={{
              event: 'swell-like', //
              swellId: swellId,
              swellcastAlias: swell.swellcast?.owner?.alias ?? '',
              promptId: swell?.promptId ?? '',
              type: contentType,
            }}
          />
          <SwellButton.Prompt
            alias={swell.swellcast?.owner?.alias ?? ''}
            promptId={swell?.promptId ?? ''}
            slug={swell?.promptSlug ?? ''}
            tracking={{
              swellId: swell.id,
              swellcastAlias: swell.swellcast?.owner?.alias ?? '',
            }}
          />
        </div>
        <div className='d-flex gap-2'>
          {showEditButton ? <EditButton canonicalId={canonicalId} /> : null}
          <ShareMasterButton canonicalId={canonicalId} />
          <ReplyButton context='swell' canonicalId={canonicalId} />
        </div>
      </div>
    </div>
  );
};

const TextSwellContent = ({ swell }: { swell: OpenApiSwellResponseEnhanced }) => {
  const data = useTextFile(swell.audio?.url);

  if (data.isSuccess) {
    if (data.data) {
      return <SwellDescription description={data.data} />;
    }
  }

  if (data.isLoading) {
    return (
      <div className='borealis-effect-1'>
        <p className='fs-4 rounded-pill bg-grey-40 d-block mb-1'>&nbsp;</p>
        <p className='fs-4 rounded-pill bg-grey-40 d-block mb-1'>&nbsp;</p>
        <p className='fs-4 rounded-pill bg-grey-40 d-block mb-1' style={{ width: '33%' }}>
          &nbsp;
        </p>
      </div>
    );
  }

  return null;
};
