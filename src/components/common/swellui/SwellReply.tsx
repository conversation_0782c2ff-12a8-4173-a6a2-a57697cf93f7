import { useEffect, useMemo, useRef } from 'react';
import { OpenApiReplyResponseEnhanced, OpenApiSwellResponseEnhanced } from '../../../api/gql.loadSwellById';
import { ReactState } from '../../../generated/graphql';
import { PlayerStatus, ReactType } from '../../../models/models';
import { getElementY, scrollWindowTo } from '../../../utils/scrollWindowTo';
import { getAuthorLink, getSwellContentType, hasArticleImage } from '../../../utils/swell-utils';
import { isVideoFile } from '../../../view/sound/media/hooks/isVideoFile';
import { ShareReplyButton } from '../../embed-code/ShareReplyButton';
import { ImageViewer } from '../../imageviewer/ImageViewer';
import { useAuth } from '../../login/useAuth';
import { AudioQuery } from '../../player/AudioPlayer/IAudioPlayer';
import { ActivePlayButton, PlayButton } from '../../player/AudioPlayerUI/PlayButton';
import { InfoButton } from '../../player/AudioRecorder/buttons/InfoButton';
import { ReplyButton } from '../../player/AudioRecorder/buttons/ReplyButton';
import { DynamicWave } from '../../player/WaveUI/WaveRenderer';
import { useTrack } from '../../player/WaveUI/useTrack';
import { SwellDescription } from '../../swell-card/CardDescription';
import { EditButton } from '../../swell-card/EditButton';
import { LikeButton } from '../../swell-card/LikeButton';
import { TrackCopy } from '../../swell-card/TrackInfo';
import { Snippet } from '../../swell-card/snippet/Snippet';
import { SwellButton } from '../../swellbutton/SwellButton';
import { DynamicMugshot } from '../Mugshot';
import { SmartLink } from '../SmartLink';

function isInterstitial(swell: OpenApiReplyResponseEnhanced) {
  return swell.id?.indexOf('swell-postroll') === 0 || swell.id?.indexOf('swell-interstitial') === 0;
}

export const SwellReply = ({ reply, swell }: { reply: OpenApiReplyResponseEnhanced; swell: OpenApiSwellResponseEnhanced }) => {
  const auth = useAuth();
  const canonicalId = reply.masterRef.params.canonicalId ?? '';
  const replyId = reply.id ?? '';
  const trackId = replyId;
  const playlistId = canonicalId;
  const duration = reply.audio?.duration ?? 0;
  const authorLink = getAuthorLink(reply.author);
  const hasImages = hasArticleImage(reply) || isVideoFile(swell.audio!.url!);
  const totalReactions = reply?.reactions?.reduce((n, r) => n + (r.count ?? 0), 0) ?? 0;
  const pressState = reply?.reactions?.find((r) => r.reaction === ReactType.HEART)?.pressState ?? ReactState.Notpressed;
  const showInfoButton = reply.masterRef.canReply && reply.isParent;
  const showEmbedButton = !isInterstitial(reply) && reply.masterRef.params?.replyId;
  const showEditButton = auth.loggedIn && auth.user?.alias && auth.user.alias?.toLowerCase() === reply.author?.alias?.toLowerCase();
  const $el = useRef<HTMLDivElement>(null);
  const audioQuery: AudioQuery = { playlistId, trackId };
  const playToggle = useTrack(audioQuery);
  const contentType = useMemo(() => getSwellContentType(swell), [swell]);
  const promptId = swell?.promptId ?? reply?.promptId ?? '';
  const promptSlug = swell?.promptSlug ?? '';

  useEffect(() => {
    if ($el.current && playToggle.active) {
      scrollWindowTo(getElementY($el.current));
    }
  }, [playToggle.active]);

  return (
    <div ref={$el} data-swellid={replyId} data-component='swellreply' className={playToggle.active ? '' : 'pointer'} onClick={playToggle.onClick}>
      <div className={`d-flex flex-column gap-3 bg-grey-10 rounded-4 card-pad ${playToggle.active ? 'border-squash' : 'border-squash-0'}`} style={{ transition: 'border 0.5s, outline 0.5s' }}>
        <div className='d-grid gap-2 w-100 align-items-center' style={{ gridTemplateColumns: 'min-content auto min-content' }}>
          <SmartLink to={authorLink}>
            <DynamicMugshot active={playToggle.active} image={reply.author?.image} size={46} alt={`@${reply.author?.alias}`} />
          </SmartLink>
          <div className='overflow-hidden'>
            <TrackCopy author={reply.author} createdOn={reply.createdOn} duration={duration} />
          </div>
          <div>
            {playToggle.active ? ( //
              <ActivePlayButton className='play-button-squash' style={{ width: 46, height: 46 }} />
            ) : (
              <PlayButton status={PlayerStatus.NONE} className='play-button-squash' style={{ width: 46, height: 46 }} />
            )}
          </div>
          {/* <GeneralPlayToggle canonicalId={swell.masterRef.params.canonicalId} replyId={swell.id}/> */}
        </div>

        <DynamicWave active={playToggle.active} audioQuery={audioQuery} />

        <SwellDescription description={reply.description} />

        <Snippet snippet={reply.snippet} className='fs-4' />

        {hasImages ? <ImageViewer swell={reply} /> : null}

        <div className='d-flex justify-content-between'>
          <div className='d-flex gap-2'>
            <LikeButton
              totalReactions={totalReactions} //
              canonicalId={canonicalId}
              replyId={replyId}
              pressState={pressState}
              tracking={{
                event: 'reply-like',
                swellId: replyId,
                // swellcastAlias: reply.author?.alias ?? '',
                swellcastAlias: swell.swellcast?.owner?.alias ?? '', // change requested by Shalini
                promptId,
                type: contentType,
              }}
            />
            <SwellButton.Prompt
              alias={swell.swellcast?.owner?.alias ?? ''}
              promptId={promptId}
              slug={promptSlug}
              tracking={{
                swellId: reply.id,
                swellcastAlias: swell.swellcast?.owner?.alias ?? '',
              }}
            />
          </div>
          <div>
            <div className='d-flex gap-2'>
              {showEditButton ? <EditButton canonicalId={reply.masterRef.params.canonicalId ?? ''} replyId={reply.id ?? ''} /> : null}
              {showEmbedButton ? <ShareReplyButton canonicalId={reply.masterRef.params.canonicalId ?? ''} replyId={reply.id ?? ''} /> : null}
              {showInfoButton ? <QAndAInfoButton /> : null}
              <ReplyButton canonicalId={reply.masterRef.params.canonicalId ?? ''} replyId={reply.id ?? ''} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const QAndAInfoButton = () => (
  <InfoButton
    message={
      <>
        <h2>This is a Question</h2>
        <p>Welcome to the Q&amp;A. This is a question.</p>
        <p>If you see an Answer button, simply press the button to answer.</p>
        <p>If you do not see an answer button, it may be a specific person has been @mentioned, so only they can answer.</p>
      </>
    }
  />
);
