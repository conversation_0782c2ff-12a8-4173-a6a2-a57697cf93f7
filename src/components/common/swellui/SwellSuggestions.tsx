import { useEffect, useState } from 'react';
import { useSuggestions } from '../../../api/gql.listSwellSuggestions';
import { SectionsView } from '../../sections/SectionsView';
import { BounceLoader } from '../bounceloader/BounceLoader';

export const SwellSuggestions = ({ canonicalId }: { canonicalId: string }) => {
  const [enabled, setEnabled] = useState(false);
  const query = useSuggestions({ id: canonicalId }, { enabled });

  useEffect(() => {
    setEnabled(true);
  }, []);

  if (query.isError) {
    return null;
  }

  if (query.isSuccess) {
    return (
      <div className='sections-container'>
        <SectionsView sections={query.data.sections} />
      </div>
    );
  }

  return (
    <div className='flex-center' style={{ height: 300 }}>
      <BounceLoader />
    </div>
  );
};
