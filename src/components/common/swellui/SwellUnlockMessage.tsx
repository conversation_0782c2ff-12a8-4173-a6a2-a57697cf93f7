import { useSwell } from '../../../api/gql.loadSwellById';
import { formatDateTime } from '../../../utils/Utils';

export const SwellUnlockMessage = ({ canonicalId }: { canonicalId: string }) => {
  const swell = useSwell({ canonicalId });
  const text = [];

  if (swell.data && swell.data.accessUntil) {
    const d = new Date(Date.parse(swell.data.accessUntil));
    const dateStr = formatDateTime(d);
    text.push(<p className='text-premium-blue fw-bold'>Unlocked until {dateStr}</p>);
  }

  if (swell.data?.configuredPriceTxt) {
    text.push(<p>{swell.data?.configuredPriceTxt}</p>);
  }

  return <>{text}</>;
};
