import { BrunoWaveCard } from '../../../framework/settings/settings';
import { OpenApiSwellSwellcastModel } from '../../../generated/graphql';
import { getOwnerLink } from '../../../utils/swell-utils';
import { SwellDescription } from '../../swell-card/CardDescription';
import { SmartLink } from '../SmartLink';
import { FollowButton } from '../swellcastui/FollowButton';
import { SubscribeButton } from '../swellcastui/SubscribeButton';

export const SwellcastMiniHeader = ({ swellcast }: { swellcast: OpenApiSwellSwellcastModel }) => {
  const ownerLink = getOwnerLink(swellcast);

  return (
    <div className='bg-grey-10' style={{ display: 'flex', justifyContent: 'center' }}>
      <div className='card-pad swellview-width' style={{ width: '100%' }}>
        <div className='fix overflow-hidden'>
          <div className='d-grid' style={{ gridTemplateColumns: 'min(155px,33vw) auto', columnGap: 10 }}>
            <SmartLink to={ownerLink}>
              <img className='landscape rounded-3 bg-grey-70' src={swellcast?.image ?? BrunoWaveCard} alt='swellcast image' />
            </SmartLink>
            <div className='d-flex flex-wrap w-100 align-items-center justify-content-between gap-2'>
              <div className='d-flex flex-column gap-1 align-items-start justify-content-center'>
                <SmartLink to={ownerLink} className='h3 m-0'>
                  {swellcast.name}
                </SmartLink>
                <SwellDescription description={swellcast.description} className='m-0' />
              </div>
              <div className='d-flex flex-wrap gap-2'>
                <FollowButton alias={swellcast.owner?.alias} />
                <SubscribeButton alias={swellcast.owner?.alias} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
