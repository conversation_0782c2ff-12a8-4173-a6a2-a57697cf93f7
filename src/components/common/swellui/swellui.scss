@use '../../../assets/css/base' as *;

[data-component='swellreply'],
[data-component='swellmaster'] {
  scroll-margin-top: 75px;
}
/*

website swell view

*/
.swellview-width {
  max-width: #{$swellview-width};
}
[data-component='swellview'] {
  .swellview {
    display: grid;
    grid-template-columns: 1fr minmax(0, $swellview-width) 1fr;
  }
  .swell-cta-bottombar {
    display: none;
  }
}

@media (max-width: $mobile-width) {
  [data-component='swellview'] {
    .swellview {
      grid-template-columns: 1fr;

      .swellview-left {
        display: none;
      }
      .swellview-right {
        display: none;
      }
    }
    .swell-cta-bottombar {
      display: block;
    }
  }
}
