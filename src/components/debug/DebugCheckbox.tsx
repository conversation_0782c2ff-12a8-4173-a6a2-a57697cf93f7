import { useDebug } from './useDebug';

export const DebugCheckbox = ({ name }: { name: string }) => {
  const { debug, toggleDebug } = useDebug(name);

  return (
    <div className='d-flex gap-1' style={{ height: 'min-content' }}>
      <input id={`cb-debug-${name}`} type='checkbox' checked={debug} onChange={() => toggleDebug()} />
      <label htmlFor='cb-debug-settings'>{name}</label>
    </div>
  );
};
