import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';

import { isServer } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { useLocalStorage } from 'usehooks-ts';
import './debug.scss';
import { DebugContext } from './DebugContext';

function resolveLocalStorageKeys() {
  if (isServer) return {};
  const str = localStorage.getItem('debug') ?? '{}';
  let data = {};
  try {
    data = JSON.parse(str);
  } catch {
    //
  }
  return data;
}

export const DebugProvider = ({ children }: { children: ReactNode }) => {
  const [searchParams] = useSearchParams({ debug: '' });
  const [storageKeys, setValue, removeValue] = useLocalStorage<Record<string, boolean>>('debug', resolveLocalStorageKeys());
  const [keys, setKeys] = useState<Record<string, boolean>>(storageKeys);
  const pending = useRef<Set<string>>(new Set());
  const [pendingCount, setPendingCount] = useState(0);

  const addKey = (key: string) => {
    const _key = key.toLowerCase();
    pending.current.add(_key);
    setPendingCount((n) => n + 1);
  };

  useEffect(() => {
    const t = setTimeout(() => {
      const delta = Object.fromEntries(Array.from(pending.current).map((key) => [key, false]));
      setKeys((k) => ({ ...delta, ...k }));
    }, 500);

    return () => {
      clearTimeout(t);
    };
  }, [pendingCount]);

  const isActive = useCallback(
    (k: string) => {
      k = k.toLowerCase();
      if (!(k in keys)) pending.current.add(k); // collect for later flush
      return !!keys[k];
    },
    [keys],
  );

  const toggle = useCallback((k: string) => {
    k = k.toLowerCase();
    setKeys((prev) => ({ ...prev, [k]: !prev[k] }));
  }, []);

  /* ---------- one-time initial merge ---------- */
  useEffect(() => {
    const fromURL =
      searchParams
        .get('debug')
        ?.split(',')
        .filter(Boolean)
        .reduce<Record<string, boolean>>((acc, k) => ({ ...acc, [k]: true }), {}) ?? {};
    setKeys({ ...storageKeys, ...fromURL });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // ← intentional empty array

  /* ---------- persist to localStorage ---------- */
  const updateLocalStorage = useCallback(() => {
    if (Object.keys(keys).length === 0) {
      removeValue();
    } else {
      setValue(keys);
    }
  }, [keys, removeValue, setValue]);

  useEffect(updateLocalStorage, [updateLocalStorage]);

  return <DebugContext.Provider value={{ isActive, toggle, keys, addKey }}>{children}</DebugContext.Provider>;
};
