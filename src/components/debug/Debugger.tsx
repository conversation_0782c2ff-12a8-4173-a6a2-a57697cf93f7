import { Close } from '@mui/icons-material';
import { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';
import { isLocal, isStage } from '../../framework/settings/settings';
import { useAudioMode } from '../../framework/useAudioMode';
import { useEnv } from '../../framework/useEnv';
import { useOrchestration } from '../../framework/useOrchestration';
import { useStashId } from '../../framework/useStashId';
import { PlayerStatus } from '../../models/models';
import { useWaveImage } from '../../utils/useWaveImage';
import { useLocalLogin } from '../../view/me/useLocalLogin';
import { MediaVolumeControl } from '../../view/sound/audio/ui/MediaVolumeControl';
import { useAuth } from '../login/useAuth';
import { useAudioController } from '../player/AudioPlayer/useAudioController';
import { useAudioTrack } from '../player/AudioPlayer/useAudioTrack';
import { useRecorder } from '../player/AudioRecorder/useRecorder';
import { RecordStash, useRecordStash } from '../player/AudioRecorder/useRecordStash';
import { useSettings } from '../settings/useSettings';
import { DebugCheckbox } from './DebugCheckbox';
import { PrettyPre } from './PrettyPre';
import { useDebug } from './useDebug';
import { useDebugger } from './useDebugger';

// const DebugCompMap = {
//         utm: DebugUTM,
//         track: DebugTrack,
//         mode: DebugMode,
//         auth: DebugAuth
// }

export const Debugger = () => {
  const db = useDebugger();
  const settings = useSettings();
  const { debug, toggleDebug } = useDebug('debug');
  const buffer = useRef<string[]>([]);
  const [savedWindowWidth, setSavedWindowWidth] = useLocalStorage('debugWindowWidth', 33);
  const [windowWidth, setWindowWidth] = useState(savedWindowWidth);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    setIsReady(true);
  }, []);

  // resize debug window
  const handleDragStart = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();
      const handleMouseMove = (ev: MouseEvent) => {
        const pct = (ev.clientX / window.innerWidth) * 100;
        // clamp between 10 and 90
        const clamped = Math.max(10, Math.min(90, pct));
        setWindowWidth(clamped);
      };
      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    [setWindowWidth],
  );

  useEffect(() => {
    const w = Math.max(5, Math.min(90, windowWidth));
    document.body.style.setProperty('--debug-window-width', `${w}vw`);
    setSavedWindowWidth(w);
  }, [setSavedWindowWidth, windowWidth]);

  // watch for key combo
  const handleKey = useCallback(
    (e: KeyboardEvent) => {
      buffer.current.push(e.key);
      if (buffer.current.length > 5) buffer.current.shift();

      if (buffer.current.slice(-5).join('') === 'debug') {
        db.toggle('debug');
      }
    },
    [db],
  );

  // listen for key combo
  useEffect(() => {
    if (isLocal || isStage) {
      window.addEventListener('keydown', handleKey);
      return () => {
        window.removeEventListener('keydown', handleKey);
      };
    }
  }, [handleKey]);

  // style page for debug window
  useEffect(() => {
    if (!settings.isProd && debug) {
      document.body.classList.add('debug');
    } else {
      document.body.classList.remove('debug');
    }
  }, [settings.isProd, debug]);

  const onChange: React.ChangeEventHandler<HTMLSelectElement> = (e) => {
    e.preventDefault();
    db.toggle(e.currentTarget.value);
  };

  if (settings.isProd || !db.isActive('debug')) {
    return null;
  }

  if (!isReady || settings.isProd) return null;

  return (
    <div
      style={{
        position: 'fixed', //
        top: 0,
        left: 0,
        bottom: 0,
        borderRight: '1px solid #ccc',
        zIndex: 999999,
        background: '#000',
        color: '#fff',
        width: 'var(--debug-window-width)',
        overflow: 'auto',
      }}
    >
      <div className='d-flex p-1'>
        <select value='' onChange={onChange} className='form-select form-select-sm p-1' style={{ backgroundColor: 'black', color: '#fff' }}>
          <option value={''}>Toggle...</option>
          {Object.entries(db.keys)
            ?.filter((k) => !k[1] && k[0] != 'debug')
            .sort((a, b) => (a[0] < b[0] ? -1 : a[0] > b[0] ? 1 : 0))
            .map((k) => (
              <option key={`cb${k[0]}`} value={k[0]}>
                {k[0]}
              </option>
            ))}
        </select>
        <div className='p-2 fs-5 pointer' onClick={toggleDebug}>
          <Close style={{ fontSize: 12 }} />
        </div>
      </div>

      <div className='d-grid w-100 h-100' style={{ gridTemplateColumns: 'auto min-content' }}>
        <div className='Xoverflow-hidden p-1'>
          <div className='d-flex flex-column gap-2 p-2 pe-0'>
            <div className='d-flex gap-2 flex-wrap'>
              {Object.entries(db.keys)
                .filter((k) => k[1] && k[0] != 'debug')
                .map((k) => (
                  <DebugCheckbox key={`cb${k[0]}`} name={k[0]} />
                ))}
            </div>
            <DebugUTM />
            <DebugTrack />
            <DebugMode />
            <DebugAuth />
            <DebugSettings />
            <DebugRecord />
            <DebugStage />
            <DebugDataLayer />
            <DebugOrchestration />
            <DebugEnv />
            <DebugVolume />
            <DebugAudioTrack />
            <DebugAudioController />
            <DebugStash />
            <DebugWave />
          </div>
        </div>

        <div className='d-flex flex-center'>
          <button className='btn btn-sm text-white' onMouseDown={handleDragStart}>
            <div style={{ width: 3, height: 40, background: '#fff' }}></div>
          </button>
        </div>
      </div>
    </div>
  );
};

const colors = [
  { className: 'amp-high', p: 0.15 },
  { className: 'amp-medium', p: 0.4 },
  { className: 'amp-low', p: 0.6 },
  { className: 'amp-medium', p: 0.85 },
  { className: 'amp-high', p: 1 },
];

const DebugWave = () => {
  const { debug } = useDebug('wave');
  const rec = useRecorder();

  const colorLookup = useMemo(() => {
    const blocks = rec.wave.length * 2;
    let layoutIndex = 0;

    const a = Array.from({ length: blocks }, (_, i) => {
      const d = Math.abs(i / blocks);
      if (colors[layoutIndex].p < d) {
        layoutIndex++;
      }
      return colors[layoutIndex].className;
    });

    return a;
  }, [rec.wave.length]);

  const fullwave = [...rec.wave, ...rec.wave.reverse()];

  const maxAmp = Math.max(...rec.wave);

  if (!debug) return null;

  return (
    <div>
      <div className='d-flex align-items-center' style={{ gap: 2, height: 30 }}>
        {fullwave.map((v, i) => {
          const color = colorLookup[i];
          return (
            <div
              key={i}
              className={color}
              style={{
                borderRadius: 2, //
                height: 4 + 25 * v,
                flex: '1 1 auto',
                opacity: v > 0 ? 1 : 0.5,
                transition: v > 0.1 ? 'opacity 0.1s' : 'opacity 1s',
              }}
            ></div>
          );
        })}
      </div>
      <input type='range' className='d-block w-100' readOnly disabled min={0} max={1} step={0.001} value={maxAmp} />
      {maxAmp.toFixed(2)}
    </div>
  );
  return <DebugBlock name='wave' data={rec.wave} />;
};

const DebugBlock = ({ name, data, children, childrenBefore = false }: { name: string; data: unknown; children?: ReactNode; childrenBefore?: boolean }) => {
  const { debug, toggleDebug } = useDebug(name);

  if (debug) {
    return (
      <PrettyPre name={name} data={data} childrenBefore={childrenBefore}>
        <div className='p-2 fs-5 pointer text-white' style={{ position: 'absolute', top: -15, right: -6 }} onClick={toggleDebug}>
          <Close style={{ fontSize: 12 }} />
        </div>
        {children}
      </PrettyPre>
    );
  }
};

const DebugStash = () => {
  const rec = useRecorder();
  const stashId = useStashId();
  const { restore } = useRecordStash();
  const [stashData, setStashData] = useState<RecordStash | null>(null);

  useEffect(() => {
    (async () => {
      const stash = await restore(stashId);
      setStashData(stash);
    })();
  }, [restore, stashId]);

  return (
    <DebugBlock name='stash' data={{ stashId, stashData }}>
      <div className='d-flex gap-2'>
        <button className='btn btn-sm btn-secondary' onClick={() => rec.stash(stashId)}>
          Stash
        </button>
        <button className='btn btn-sm btn-secondary' onClick={() => rec.flush(stashId)}>
          Flush
        </button>
        <button className='btn btn-sm btn-secondary' onClick={() => rec.restore(stashId)}>
          Restore
        </button>
      </div>
    </DebugBlock>
  );
};

const DebugAudioController = () => {
  const aux = useAudioController();
  return (
    <DebugBlock
      name='audiocontroller'
      data={{
        ...aux, //
        swell: null,
        // tracks: aux.tracks.map((t) => ({ id: t.id, index: t.index })),
        tracks: aux.tracks.length,
        currentTrack: aux.currentTrack?.id,
      }}
    />
  );
};

const DebugAudioTrack = () => {
  const track = useAudioTrack();
  return <DebugBlock name='audiotrack' data={track} />;
};

const DebugDataLayer = () => {
  const { debug } = useDebug('datalayer');
  const [events, setEvents] = useState<unknown[]>([]);
  const [index, setIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const data = useMemo(() => events[index], [events, index]);

  const handlePrev = () => {
    setIsPlaying(false);
    setIndex((i) => Math.max(0, i - 1));
  };

  const handleNext = () => {
    setIsPlaying(false);
    setIndex((i) => Math.min(events.length - 1, i + 1));
  };

  const handlePlay = () => {
    setIndex(events.length - 1);
    setIsPlaying(true);
  };

  const handleReset = () => {
    window.dataLayer = [];
    setEvents([]);
    setIndex(0);
  };

  useEffect(() => {
    if (debug) {
      const onTick = () => {
        setEvents(window.dataLayer?.filter((d) => (d as { event?: string })?.event?.indexOf('gtm') !== 0) ?? []);
      };

      const i = setInterval(onTick, 500);
      onTick();

      return () => {
        clearInterval(i);
      };
    }
  }, [debug]);

  useEffect(() => {
    if (debug && isPlaying) {
      setIndex(events.length - 1);
    }
  }, [debug, events, isPlaying]);

  return (
    <DebugBlock name='datalayer' data={data} childrenBefore={true}>
      <div className='d-flex gap-2 justify-content-between align-items-center'>
        <div className='d-flex gap-2'>
          <button className='btn btn-sm btn-secondary' onClick={handlePrev}>
            &lt;
          </button>
          <button className={`btn btn-sm ${isPlaying ? 'btn-primary' : 'btn-secondary'}`} onClick={handlePlay} title='Always show latest event'>
            auto
          </button>
          <button className='btn btn-sm  btn-secondary' onClick={handleNext}>
            &gt;
          </button>
          <button className='btn btn-sm  btn-secondary' onClick={handleReset}>
            reset
          </button>
        </div>
        <small className='text-white'>
          {index + 1} / {events.length}
        </small>
      </div>
      {events.length === 0 ? <div className='text-white'>No events</div> : null}
    </DebugBlock>
  );
};

const DebugVolume = () => {
  const { $audio, volume: mediaVolume } = useAudioTrack();
  const { update, volume: settingsVolume } = useSettings();

  useEffect(() => {
    update({ volume: mediaVolume });
  }, [update, mediaVolume]);

  useEffect(() => {
    $audio.current!.volume = settingsVolume!;
  }, [$audio, settingsVolume]);

  return (
    <DebugBlock name='volume' data={{ mediaVolume, settingsVolume }}>
      <MediaVolumeControl $media={$audio!} />
    </DebugBlock>
  );
};

const DebugOrchestration = () => {
  const orch = useOrchestration();
  return <DebugBlock name='orch' data={orch} />;
};

const DebugRecord = () => {
  const rec = useRecorder();
  return (
    <DebugBlock
      name='record'
      data={{
        ...rec, //
        $media: null,
        status: PlayerStatus[rec.status],
        wave: rec.wave.length ? Math.max(...rec.wave)?.toFixed(2) ?? 0 : 0,
      }}
    />
  );
};

const DebugSettings = () => {
  const settings = useSettings();
  const { dynamicWave } = useWaveImage();
  return (
    <DebugBlock name='settings' data={{ ...settings, dynamicWave }}>
      <button onClick={() => settings.update({ allowPlayInCard: !settings.allowPlayInCard })} className={`btn btn-sm btn-${settings.allowPlayInCard ? 'primary' : 'secondary'}`}>
        allowPlayInCard
      </button>
    </DebugBlock>
  );
};

const DebugAuth = () => {
  const { isLocal } = useSettings();
  const auth = useAuth();
  const me = useLocalLogin();

  useEffect(() => {
    auth.update();
  }, [auth, me.info]);

  return (
    <DebugBlock name='auth' data={auth}>
      {isLocal ? (
        <div className='d-flex gap-2'>
          <button disabled={auth.loggedIn} className='btn btn-sm btn-secondary' onClick={me.login}>
            log in
          </button>
          <button disabled={!auth.loggedIn} className='btn btn-sm btn-secondary' onClick={me.logout}>
            log out
          </button>
          <button className={`btn btn-sm btn-${me.info.isPremium ? 'primary' : 'secondary'}`} onClick={me.togglePremium}>
            premium
          </button>
        </div>
      ) : null}
    </DebugBlock>
  );
};

const DebugMode = () => {
  const { isOpen } = useOrchestration();
  const { audioMode } = useAudioMode();
  const track = useAudioTrack();
  return <DebugBlock name='mode' data={{ isOpen, audioMode, track: { status: PlayerStatus[track.status] } }} />;
};

const DebugUTM = () => {
  const { utmParams } = useSettings();
  return <DebugBlock name='utm' data={utmParams} />;
};

const DebugEnv = () => {
  const env = useEnv();
  return <DebugBlock name='env' data={env} />;
};

const DebugTrack = () => {
  const { trackingData } = useAudioMode();
  return <DebugBlock name='track' data={{ trackingData }} />;
};

const DebugStage = () => {
  const { debug, toggleDebug } = useDebug('stage');
  const settings = useSettings();

  if (debug) {
    return (
      <PrettyPre name='stage'>
        <div className='p-2 fs-5 pointer text-white' style={{ position: 'absolute', top: '-1em', right: '-0.5em' }} onClick={toggleDebug}>
          <Close style={{ fontSize: 12 }} />
        </div>

        <div className='d-flex w-100 gap-2 p-2'>
          <button className={'btn flex-grow-1 ' + (settings.stage == 'stage' ? 'btn-primary' : 'btn-secondary')} onClick={() => settings.update({ stage: 'stage' })}>
            stage
          </button>
          <button className={'btn flex-grow-1 ' + (settings.stage == 'default' ? 'btn-primary' : 'btn-secondary')} onClick={() => settings.update({ stage: 'default' })}>
            default
          </button>
          <button className={'btn flex-grow-1 ' + (settings.stage == 'prod' ? 'btn-primary' : 'btn-secondary')} onClick={() => settings.update({ stage: 'prod' })}>
            prod
          </button>
        </div>
      </PrettyPre>
    );
  }
};
