import { CSSProperties, ReactNode } from 'react';
import { useSettings } from '../settings/useSettings';

export const PrettyPre = ({ data, name, children = null, childrenBefore = false }: { name: string; data?: ReactNode | string | unknown; children?: ReactNode; childrenBefore?: boolean }) => {
  return (
    <fieldset className='border-white-50 position-relative p-2'>
      <legend className='fs-5 m-0' style={{ float: 'initial', width: 'auto', paddingInline: 4 }}>
        {name}
      </legend>
      <div className='d-flex flex-column gap-2'>
        {childrenBefore ? children : null}
        {data !== undefined ? <Pre data={data} style={{ margin: 0 }} /> : null}
        {childrenBefore ? null : children}
      </div>
    </fieldset>
  );
};

export const Pre = ({ data, style, ...props }: { data: unknown; style?: CSSProperties } & React.HTMLAttributes<HTMLPreElement>) => {
  const { isProd } = useSettings();

  if (isProd) return null;

  return (
    <pre {...props} style={{ wordBreak: 'normal', whiteSpace: 'pre', overflowX: 'auto', width: '100%', boxSizing: 'border-box', ...style }}>
      {JSON.stringify(data, null, 2)}
    </pre>
  );
};
