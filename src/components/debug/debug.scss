body {
  --debug-window-width: 33vw;
}
/*

Adjust layout when debug window is open

*/
body.debug {
  left: var(--debug-window-width);
  width: calc(100vw - var(--debug-window-width));
  position: absolute;

  header {
    left: var(--debug-window-width) !important;
    width: calc(100vw - var(--debug-window-width));
  }

  [data-component='peekaboodrawer'] {
    left: var(--debug-window-width) !important;
  }

  [data-component='draggabledash'] {
    left: var(--debug-window-width) !important;
    width: calc(100vw - var(--debug-window-width));
  }

  [data-component='popupprovider'] {
    left: var(--debug-window-width) !important;
    width: calc(100vw - var(--debug-window-width));
  }
}
