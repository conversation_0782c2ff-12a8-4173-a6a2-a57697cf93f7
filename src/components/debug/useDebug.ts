import { useCallback, useEffect, useRef, useState } from 'react';
import { useDebugger } from './useDebugger';

export const useDebug = (key: string, { color = '#ff6600' }: { color?: string } = {}) => {
  const { isActive, toggle, keys, addKey } = useDebugger();
  // Register the flag *after* the first render
  const added = useRef(false);

  useEffect(() => {
    if (addKey && key && !added.current) {
      addKey(key);
      added.current = true; // prevent spamming addKey
    }
  }, [addKey, key]);

  const [debug, setDebug] = useState(isActive(key));

  useEffect(() => {
    const active = isActive(key);
    if (debug !== active) {
      setDebug(active);
    }
    if (!added.current && Object.prototype.hasOwnProperty.call(keys, key)) {
      added.current = true;
    }
  }, [debug, isActive, key, keys]);

  const prepareArgs = useCallback(
    (...args: unknown[]) => {
      return [`%c${key}`, `color: ${color}; font-weight: bold`, ...args];
    },
    [key, color],
  );

  const toggleDebug = useCallback(() => toggle(key), [toggle, key]);

  return { debug, toggleDebug, prepareArgs };
};
