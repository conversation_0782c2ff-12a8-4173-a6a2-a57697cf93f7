import { useEffect, useRef, useState } from 'react';

export const CopyBlock = ({ text, onClick }: { text: string; onClick(): void }) => {
  const textareaRef = useRef(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [message, setMessage] = useState('');

  const messageTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (message.length) {
      if (messageTimeout.current) {
        clearTimeout(messageTimeout.current);
      }
      messageTimeout.current = setTimeout(() => setMessage(''), 3000);
      return () => {
        if (messageTimeout.current) clearTimeout(messageTimeout.current);
      };
    }
  }, [message]);

  async function copyToClipboard() {
    try {
      if (inputRef.current) {
        // Select the text in the input field
        inputRef.current.select();
        inputRef.current.setSelectionRange(0, 99999); // For mobile devices

        // Using Clipboard API to copy
        await navigator.clipboard.writeText(inputRef.current.value);

        setMessage('Copied to clipboard');
        onClick();
      }
    } catch (error) {
      console.error('Failed to copy to clipboard', error);
      setMessage('Failed to copy');
    }
  }

  return (
    <div className='d-block bg-grey-10 p-2' onClick={copyToClipboard}>
      <div className='p-2'>
        <input style={{ opacity: 0, position: 'fixed', left: -1000 }} ref={inputRef} value={text} onChange={() => null} />
        <div ref={textareaRef} className='text-break h4-normal'>
          {text}
        </div>
      </div>
      <div className='pt-2 d-flex justify-content-end align-items-center'>
        <div className='px-2'>{message}</div>
        <div className='btn btn-secondary btn-sm rounded-pill px-3 border border-secondary'>Copy</div>
      </div>
    </div>
  );
};
