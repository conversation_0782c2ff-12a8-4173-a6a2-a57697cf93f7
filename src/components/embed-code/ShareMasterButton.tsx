import { useMemo } from 'react';
import { useSwell } from '../../api/gql.loadSwellById';
import { ShareType } from '../../models/models';
import { dataLayer } from '../../tracking/Tracking';
import { getOGFromSwell, getSwellContentType } from '../../utils/swell-utils';
import { SwellButton } from '../swellbutton/SwellButton';
import { useSharePopup } from './useSharePopup';

export const ShareMasterButton = ({ canonicalId }: { canonicalId: string }) => {
  const swell = useSwell({ canonicalId });
  const og = swell.isSuccess ? getOGFromSwell(swell.data) : {};
  const contentType = useMemo(() => getSwellContentType(swell.data!), [swell.data]);
  const payload = {
    swellID: swell.data?.id, //
    // name: swell.data?.author?.alias ?? '',
    name: swell.data?.swellcast?.owner?.alias ?? '',
    type: `${ShareType.SWELL}_${contentType}`,
    ...(swell.data?.promptId ? { promptId: swell.data.promptId } : {}),
  };
  const share = useSharePopup({ og, shareType: ShareType.SWELL, payload });

  const onClick: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    dataLayer({ event: 'swellShare', ...payload });
    share.show();
  };

  return <SwellButton.Share onClick={onClick} title={'Share this Swell'} />;
};
