import { useState } from 'react';
import { SwellIcons } from '../../assets/icons/SwellIcons';
import { OGModel, ShareType } from '../../models/models';
import { dataLayer } from '../../tracking/Tracking';
import urlJoin from '../../utils/urlJoin';
import { usePopup } from '../popup/usePopup';
import { SwellButton } from '../swellbutton/SwellButton';
import { CopyBlock } from './CopyBlock';
import { WidgetType } from './WidgetType';
import { getWidgetIframe } from './getWidgetIframe';
import { useBasePath } from './useBasePath';

interface IShareTrack {
  event: 'replyEmbedCopy' | 'swellEmbedCopy' | 'swellcastEmbedCopy' | 'embedCopy';
  type: ShareType | string;
  name: string | null;
  [key: string]: string | number | null;
}

enum ShareTabs {
  LINK = 0,
  EMBED = 1,
}

const widgetTypeMap = {
  [ShareType.COMMUNITY]: WidgetType.DEFAULT,
  [ShareType.REPLY]: WidgetType.SWELL,
  [ShareType.STATION]: WidgetType.DEFAULT,
  [ShareType.SWELL]: WidgetType.SWELL,
  [ShareType.SWELLCAST]: WidgetType.DEFAULT,
  [ShareType.SWELLCAST_SWELL]: WidgetType.SWELL,
};

export const SharePopup = ({ shareType, og, payload }: { shareType: ShareType; og: OGModel; payload: Partial<IShareTrack> }) => {
  const popup = usePopup();
  const [tabIndex, setTabIndex] = useState(ShareTabs.LINK);
  const [autoplay, setAutoplay] = useState(true);
  const widgetType = widgetTypeMap[shareType];
  const basePath = useBasePath();
  const shareLink = urlJoin(basePath, og.canonicalPath);
  const widgetLink = urlJoin(basePath, widgetType, og.canonicalPath, autoplay ? `?autoplay=${autoplay ? 1 : 0}` : '');
  const iframeHTML = getWidgetIframe(widgetLink, { autoplay });

  const trackCopyLink = {
    event: `${shareType}LinkCopy`,
    ...payload,
  };

  const trackCopyEmbed = {
    event: `${shareType}EmbedCopy`,
    ...payload,
  };

  const onChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    setTabIndex(parseInt(e.currentTarget.value));
  };

  const onClickCopyLink = () => {
    dataLayer(trackCopyLink);
  };

  const onClickCopyEmbed = () => {
    dataLayer(trackCopyEmbed);
  };

  return (
    <div className='p-3' style={{ maxWidth: '100%', width: 800 }}>
      <div data-component='embed-popup' onClick={(e) => e.stopPropagation()} className='rounded-1'>
        <div style={{ borderBottom: '1px solid rgba(128,128,128,0.3)', display: 'flex', width: '100%', justifyContent: 'space-between' }}>
          <div className='tabs-ui'>
            <input type='radio' name='embed-tabs' id='tab-0' value={ShareTabs.LINK} checked={tabIndex === ShareTabs.LINK} onChange={onChange} />
            <label htmlFor='tab-0'>Share link</label>
            <input type='radio' name='embed-tabs' id='tab-1' value={ShareTabs.EMBED} checked={tabIndex === ShareTabs.EMBED} onChange={onChange} />
            <label htmlFor='tab-1'>Embed in website</label>
          </div>
          <div className='p-2 flex-center'>
            <SwellButton.Base aria-label='close' className='shadow-none' onClick={popup.close} Icon={SwellIcons.Close} />
          </div>
        </div>
        <div className='d-flex flex-column flex-md-row gap-4 p-4'>
          <div className='d-flex flex-column gap-2' style={{ flex: '0 0 min(300px, 50vw)' }}>
            <img className='landscape cover rounded-2 border-grey-30' src={og.image} alt='share image' />
            <h4 className='m-0'>{og.title}</h4>
            <p className='m-0 text-break'>{og.description}</p>
          </div>
          <div style={{ flex: '1 1 auto' }}>
            <div style={{ display: tabIndex === ShareTabs.LINK ? 'block' : 'none' }}>
              <div className='bg-grey-10 overflow-hidden rounded-2'>
                <CopyBlock text={shareLink} onClick={onClickCopyLink} />
              </div>
            </div>
          </div>

          <div style={{ display: tabIndex === ShareTabs.EMBED ? 'block' : 'none' }}>
            <div className='bg-grey-10 overflow-hidden rounded-2'>
              <CopyBlock text={iframeHTML} onClick={onClickCopyEmbed} />
              <div className={'p-2 align-items-center d-flex'}>
                <div className='p-2 d-flex align-items-center'>
                  <input id='autoplay-checkbox' type='checkbox' checked={autoplay} value='autoplay' onChange={(e) => setAutoplay(e.currentTarget.checked)}></input>
                </div>
                <label className='py-2 small text-mode' style={{ lineHeight: 1 }} htmlFor='autoplay-checkbox'>
                  autoplay
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
