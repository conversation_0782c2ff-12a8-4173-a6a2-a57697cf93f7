import { usePromptPage } from '../../api/gql.getPromptPage';
import { dataLayer } from '../../tracking/Tracking';
import { getOGFromPrompt } from '../../utils/swell-utils';
import { SwellButton } from '../swellbutton/SwellButton';

export const SharePromptButton = ({ promptId }: { promptId: string }) => {
  const query = usePromptPage({ id: promptId });
  const og = query.isSuccess ? getOGFromPrompt(query.data.pages[0]) : {};
  const url = og?.canonicalPath ?? '';

  const share: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    if (navigator.share) {
      navigator
        .share({ url })
        .then(() => {
          dataLayer({
            event: `prompt-share`,
            promptId,
            swellcastId: query.data?.pages[0].swellcast.id,
            swellcastAlias: query.data?.pages[0].swellcast.owner?.alias,
          });
        })
        .catch(() => {});
    } else {
      const globalUrl = new URL(url, window.location.origin);
      navigator.clipboard.writeText(globalUrl.href);
      alert('Link copied to clipboard!');
    }
  };

  return <SwellButton.Share onClick={share} title={'Share this prompt'} />;
};
