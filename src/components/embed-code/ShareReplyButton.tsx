import { useMemo } from 'react';
import { useSwell } from '../../api/gql.loadSwellById';
import { ShareType } from '../../models/models';
import { dataLayer } from '../../tracking/Tracking';
import { getOGFromReply, getSwellContentType } from '../../utils/swell-utils';
import { SwellButton } from '../swellbutton/SwellButton';
import { useSharePopup } from './useSharePopup';

export const ShareReplyButton = ({ canonicalId, replyId }: { canonicalId: string; replyId: string }) => {
  const swell = useSwell({ canonicalId });
  const reply = swell.isSuccess ? swell.data.replies.find((r) => r.id === replyId) : null;
  const og = swell.isSuccess && reply ? getOGFromReply(swell.data, reply) : {};
  const contentType = useMemo(() => getSwellContentType(swell.data!), [swell.data]);
  const payload = {
    swellID: replyId, //
    // name: reply?.author?.alias ?? '',
    name: swell.data?.swellcast?.owner?.alias ?? '',
    type: `${ShareType.SWELL}_${contentType}`,
    promptId: swell.data?.promptId,
  };
  const share = useSharePopup({ og, shareType: ShareType.REPLY, payload });

  const onClick: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    dataLayer({ event: 'replyShare', ...payload });
    share.show();
  };

  return <SwellButton.Share onClick={onClick} title={'Share this Reply'} />;
};
