import { useStationData } from '../../api/gql.getStationPage';
import { StationType } from '../../generated/graphql';
import { ShareType } from '../../models/models';
import { dataLayer } from '../../tracking/Tracking';
import { getOGFromCommunity } from '../../utils/swell-utils';
import { SwellButton } from '../swellbutton/SwellButton';
import { useSharePopup } from './useSharePopup';

export const ShareStationButton = ({ stationId, type }: { stationId: string; type: StationType }) => {
  const query = useStationData({ id: stationId, type }, { enabled: !!stationId });
  const station = query.isSuccess ? query.data : null;
  const og = query.isSuccess && station ? getOGFromCommunity(station) : {};
  const payload = { name: station?.id, type: ShareType.STATION };
  const share = useSharePopup({
    og,
    shareType: ShareType.STATION,
    payload,
  });

  const onClick = () => {
    dataLayer({ event: 'stationShare', ...payload });
    share.show();
  };

  return <SwellButton.Share onClick={onClick} title={'Share this Swell Community'} />;
};
