// import { useSwell } from '../../api/gql.loadSwellById';
// import { ShareType } from '../../models/models';
// import { dataLayer } from '../../tracking/Tracking';
// import { getOGFromSwell } from '../../utils/swell-utils';
// import { SwellButton } from '../swellbutton/SwellButton';
// import { useSharePopup } from './useSharePopup';

// // TODO: deprecated because the card in PLP is not clickable anymore

// export const ShareSwellButton = ({ canonicalId }: { canonicalId: string }) => {
//   const swell = useSwell({ canonicalId });
//   const og = swell.isSuccess ? getOGFromSwell(swell.data) : {};
//   const payload = { swellID: swell.data?.id, name: swell.data?.swellcast?.owner?.alias, type: ShareType.SWELL };
//   const share = useSharePopup({ og, shareType: ShareType.SWELL, payload });

//   const onClick = () => {
//     dataLayer({ event: 'swellShare', ...payload });
//     share.show();
//   };

//   return <SwellButton.Share onClick={onClick} title={'Share this Swell'} />;
// };
