import { CSSProperties } from 'react';
import { useProfile } from '../../api/gql.getProfilePage';
import { ShareType } from '../../models/models';
import { dataLayer } from '../../tracking/Tracking';
import { getOGFromProfile } from '../../utils/swell-utils';
import { useRouteParams } from '../../utils/useRouteParams';
import { SwellButton } from '../swellbutton/SwellButton';
import { useSharePopup } from './useSharePopup';

export const ShareSwellcastButton = ({ alias, style, className }: { alias?: string | null; style?: CSSProperties; className?: string }) => {
  //   const isMobile = useIsMobile();
  const params = useRouteParams();
  const profile = useProfile({ alias });
  const og = profile.isSuccess ? getOGFromProfile(profile.data, { ...params, listId: alias?.toLowerCase() ?? '' }) : {};
  const payload = { name: alias, type: ShareType.SWELLCAST };
  const share = useSharePopup({ og, shareType: ShareType.SWELLCAST, payload });

  const onClick = () => {
    dataLayer({ event: 'swellcastShare', ...payload });
    share.show();
  };

  if (!alias) {
    return null;
  }

  return <SwellButton.Share onClick={onClick} title={'Share this Swellcast'} style={style} className={className} />;

  //   return (
  //     <SwellButton.Share onClick={onClick} title={'Share this Swellcast'} style={style} className={className}>
  //       {isMobile ? null : 'Share'}
  //     </SwellButton.Share>
  //   );
};
