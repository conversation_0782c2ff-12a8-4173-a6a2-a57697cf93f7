export const getWidgetIframe = (url: string, options: { autoplay?: boolean } = { autoplay: true }) => {
  return `
        <iframe
          src="${url}"
          ${options.autoplay ? 'allow="autoplay"' : ''}
          frameBorder="0"
          style="margin: 0 auto; width: 500px; max-width: 90vw; height: 750px; max-height: 90vh; border: 1px solid #ccc; border-radius: 8px; overflow: hidden; display: block;"
        ></iframe>
      `;

  //   return (
  //     <iframe
  //       src={url}
  //       {...(options.autoplay ? { allow: 'autoplay' } : {})}
  //       frameBorder='0'
  //       style={{
  //         margin: '0 auto', //
  //         width: 500,
  //         maxWidth: '90vw',
  //         height: 750,
  //         maxHeight: '90vh',
  //         border: '1px solid #ccc',
  //         borderRadius: 8,
  //         overflow: 'hidden',
  //         display: 'block',
  //       }}
  //     ></iframe>
  //   );
};
