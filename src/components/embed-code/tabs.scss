@use 'sass:map';
@use '../../assets/css/base' as *;

.tabs-ui {
  display: flex;
  input {
    display: none;
  }
  label {
    @extend .fs-3, .fw-light, .flex-center;
    padding: 16px 24px;
    text-align: center;
    border-bottom: 2px solid transparent;
    opacity: 0.5;
    transition: all 0.3s;
  }
  input:checked + label {
    opacity: 1;
    border-bottom: 2px solid map.get($theme-colors, 'squash');
  }
}
