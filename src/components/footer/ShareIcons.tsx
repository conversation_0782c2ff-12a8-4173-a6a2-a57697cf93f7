import { useMemo } from 'react';
import FacebookIconSVG from '../../assets/images/icon-facebook-white.svg';
import InstagramIconSVG from '../../assets/images/icon-instagram-white.svg';
import LinkedinIconSVG from '../../assets/images/icon-linkedin-white.svg';
import XIconSVG from '../../assets/images/icon-x.svg';

import { DEFAULT_COUNTRY_CODE } from '../../framework/settings/settings';
import { SwellAppIcon } from '../common/SwellAppIcon';
import { useSettings } from '../settings/useSettings';

const SocialIcons = {
  swell: {
    name: 'Swell',
    icon: SwellAppIcon,
    color: '#fcb950',
    containerStyle: {},
    style: { width: 32, height: 32, backgroundColor: '#fcb950' },
    url: {
      US: 'https://www.swellcast.com/swell',
    } as Record<string, string>,
    ariaLabel: 'share on swell',
  },
  facebook: {
    name: 'Facebook',
    icon: FacebookIconSVG,
    color: '#3b5998',
    containerStyle: {},
    style: { width: 32, height: 32, backgroundColor: '#3b5998' },
    url: {
      US: 'https://www.facebook.com/swelltalk',
      IN: 'https://www.facebook.com/swelltalkindia',
    } as Record<string, string>,
    ariaLabel: 'share on Facebook',
  },
  x: {
    name: 'X',
    icon: XIconSVG,
    color: '#FFF', //'#00aced',
    containerStyle: { width: 32, height: 32, backgroundColor: '#000', display: 'flex', justifyContent: 'center', alignItems: 'center' },
    style: { width: 24, height: 24, backgroundColor: '#000' },
    url: {
      US: 'https://x.com/swell_talk',
      IN: 'https://x.com/SwellIndia',
    } as Record<string, string>,
    ariaLabel: 'share on X',
  },
  linkedin: {
    name: 'LinkedIn',
    icon: LinkedinIconSVG,
    color: '#007fb1',
    containerStyle: {},
    style: { width: 32, height: 32, backgroundColor: '#007fb1' },
    url: {
      US: 'https://www.linkedin.com/company/swellcast',
      IN: 'https://www.linkedin.com/company/swellcast',
    } as Record<string, string>,
    ariaLabel: 'share on LinkedIn',
  },
  instagram: {
    name: 'Instagram',
    icon: InstagramIconSVG,
    color: '#c13584',
    containerStyle: {},
    style: { width: 32, height: 32, backgroundColor: '#c13584' },
    url: {
      US: 'https://www.instagram.com/swell_talk/',
      IN: 'https://www.instagram.com/swell_talk_india/',
    } as Record<string, string>,
    ariaLabel: 'share on Instagram',
  },
};

const SocialLink = ({ socialKey, target = '_blank' }: { socialKey: keyof typeof SocialIcons; target?: string }) => {
  const { countryCode } = useSettings();
  const s = SocialIcons[socialKey];
  const Icon = s.icon;
  const href = useMemo(() => s.url?.[countryCode as keyof typeof s.url] ?? s.url?.[DEFAULT_COUNTRY_CODE], [countryCode]);

  return (
    <a
      href={href}
      target={target}
      className='share-icon'
      aria-label={s.ariaLabel}
      style={{ padding: 3, verticalAlign: 'top' }}
      onClick={() => {
        window.dataLayer.push({ event: 'social-link', social: socialKey });
      }}
      rel='noreferrer'
    >
      <div style={{ width: 32, height: 32, ...s.containerStyle }}>
        <Icon style={s.style} id='SocialLink' />
      </div>
    </a>
  );
};

export const ShareIcons = (): JSX.Element => {
  return (
    <div>
      <SocialLink socialKey='facebook' />
      <SocialLink socialKey='x' />
      <SocialLink socialKey='linkedin' />
      <SocialLink socialKey='instagram' />
      <SocialLink socialKey='swell' target='_self' />
    </div>
  );
};
