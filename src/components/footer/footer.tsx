import { SmartLink } from '../common/SmartLink';
import { useSettings } from '../settings/useSettings';
import { ShareIcons } from './ShareIcons';
import { useBranchInfo } from './useBranchInfo';

export const Footer = () => {
  return (
    <footer className='p-3 position-relative text-mode bg-mode fs-4' style={{ borderTop: '1px solid rgba(128,128,128,0.3)' }}>
      <FooterInner />
      <BranchInfo />
    </footer>
  );
};

const FooterInner = () => {
  const info = useBranchInfo();
  return (
    <div className='d-flex flex-column align-items-center flex-lg-row-reverse'>
      <div className='p-1'>
        <ShareIcons />
      </div>
      <div className='flex-grow-1' />
      <div className='p-1 d-flex flex-column align-items-center flex-lg-row fw-light'>
        <SmartLink to='https://swell.life/contact' utm={true} className='p-2' area-label='Contact Us'>
          Contact Us
        </SmartLink>
        <a className='p-2 fix' href={`/copyrightpolicy`} aria-label='Copyright Policy'>
          Copyright Policy
        </a>
        <a className='p-2 fix' href={`/termsofservice`} aria-label='Terms of Services'>
          Terms of Service
        </a>
        <a className='p-2 fix' href={`/privacypolicy`} aria-label='Privacy Policy'>
          Privacy Policy
        </a>
      </div>
      <div className='flex-grow-1' />
      <div title={info} className='p-2 small fw-light fs-6'>
        &copy; {new Date().getFullYear()} Anecure, Inc. All Rights Reserved.
      </div>
    </div>
  );
};

const BranchInfo = () => {
  const info = useBranchInfo();
  const settings = useSettings();
  return !settings.isProd ? <div className='p-2 small fw-light text-center text-blue'>{info}</div> : null;
};
