import { SwellIcons } from '../../assets/icons/SwellIcons';
import { SmartLink } from '../common/SmartLink';
import { useHasNotifications } from './useNotifications';

export const Notification = () => {
  const hasNotifications = useHasNotifications();
  const isVisible = hasNotifications.isSuccess && hasNotifications.data === true;

  if (isVisible) {
    return (
      <SmartLink to='/me/?op=notifications' reloadDocument={true} className='flex-center fade-in fix icon-notification bg-grey-10 position-relative square rounded-1' style={{ aspectRatio: '1/1', width: 40 }}>
        <SwellIcons.Notification className='absolute-center icon-notification' style={{ width: 20, height: 20 }} />
      </SmartLink>
    );
  }

  return null;
};
