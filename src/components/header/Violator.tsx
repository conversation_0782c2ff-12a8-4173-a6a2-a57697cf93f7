// import React, { useEffect, useState } from 'react';
// import { StoreAction } from '../common/downloadapp/DownloadAppButton';
// import { useAuth } from '../login/useAuth';
// import { SwellButton } from '../swellbutton/SwellButton';
// import { AppButton } from './header';

// export const Violator = () => {
//   const auth = useAuth();
//   const [isOpen, setIsOpen] = useState(false);

//   useEffect(() => {
//     setIsOpen(!auth.loggedIn);
//   }, [auth.loggedIn]);

//   if (!isOpen) {
//     return null;
//   }

//   return (
//     <div
//       style={{
//         zIndex: 99,
//         // position: 'fixed',
//         position: isOpen ? 'sticky' : 'relative',
//         bottom: -1,
//         left: 0,
//         right: 0,
//         // transform: `translateY(${isOpen ? 0 : '110%'})`,
//         // transition: 'transform 0.3s',
//       }}
//       className='bg-mode text-mode p-3 p-lg-4 border-top'
//     >
//       <ViolatorContent onClickClose={() => setIsOpen(false)} isOpen={isOpen} />
//     </div>
//   );
// };

// const ViolatorContent = ({ onClickClose, isOpen }: { onClickClose: React.MouseEventHandler<HTMLElement>; isOpen: boolean }) => {
//   return (
//     <div className={`d-flex gap-3 align-items-center ${isOpen ? 'justify-content-between' : 'justify-content-center'}`}>
//       {isOpen ? <SwellButton.Secondary onClick={onClickClose}>No Thanks</SwellButton.Secondary> : null}
//       <StoreAction context='website-header-cta' className={'gap-2 fix d-flex align-items-center justify-content-end'}>
//         <h4 className='text-nowrap'>Get&nbsp;the&nbsp;App&nbsp;&#9656;</h4>
//         <AppButton />
//       </StoreAction>
//     </div>
//   );
// };
