@use '../../assets/css/base' as *;

[data-component='mobileheader'] {
  & > * {
    overflow: hidden;
    max-width: 100%;
    transition: all 0.3s;
  }
  .search-cell {
    @extend .flex-center;
  }
  &.opened > *:not(.search-cell) {
    max-width: 0;
  }
}

$anim-duration: 5; // in seconds
$anim-speed: 0.65; // in seconds

:root {
  --shine-degree: 120deg;
  --shine-color: rgba(255, 255, 255, 0.5);
  --shine-effect: linear-gradient(var(--shine-degree), transparent, var(--shine-color), transparent);
  --shine-transition: all #{$anim-speed}s ease-in-out;
}

@keyframes shine {
  0% {
    left: -100%;
    transition-property: left;
  }
  12%,
  100% {
    left: 100%;
    transition-property: left;
  }
}

.btn-shine {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--shine-effect);
    animation: shine #{$anim-duration}s ease-in-out infinite;
  }
}
.nav-link:hover {
  --icon-fill: rgba(128, 128, 128, 0.5);
}
.nav-link:hover svg * {
  transition: 'fill 0.3s';
}
.nav-link {
  @extend .d-flex, .gap-3, .fix, .align-items-center, .m-0, .py-3, .px-4;
}
.nav-link,
.nav-link > div {
  @extend .h2, .fw-light;
}
[data-bs-theme='light'] .nav-link svg path {
  stroke: #000;
}
[data-bs-theme='light'] .mic-icon rect {
  stroke: #000;
}
/*

nav logo

*/
.swell-logo-nav {
  width: 120px;
  max-width: 23vw;
  height: 40px;
  background-repeat: no-repeat;
  background-position: center left;
  background-size: contain;
}

.btn-appicon {
  @extend .flex-center, .bg-grey-dark, .btn-shine;
  border-radius: 0.6rem;
  overflow: hidden;
  width: 45px;
  height: 45px;

  & svg {
    width: 100%;
    height: 100%;
    fill: #fff;
  }

  &[data-os='ios'] svg,
  &[data-os='android'] svg {
    width: 28px;
    height: 28px;
  }
}

.max-content {
  width: max-content;
}

[data-bs-theme='dark'] .icon-notification {
  --icon-color: #fff;
}

.icon-notification {
  --icon-color: #000;
}
