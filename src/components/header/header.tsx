import SwellAppIcon from '@images/swell-app-icon-square.svg';
import { Download, ExpandLess, ExpandMore } from '@mui/icons-material';
import AppBar from '@mui/material/AppBar';
import Slide from '@mui/material/Slide';
import useScrollTrigger from '@mui/material/useScrollTrigger';
import { useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useRef } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';

import { useResizeObserver } from 'usehooks-ts';
import { ThemeColors } from '../../assets/css/ThemeColors';
import { SwellIcons } from '../../assets/icons/SwellIcons';
import { zIndex } from '../../framework/settings/zIndex';
import { useEnv } from '../../framework/useEnv';
import { useOrchestration } from '../../framework/useOrchestration';
import { SwellListType } from '../../models/models';
import { dataLayer } from '../../tracking/Tracking';
import { getSwellcastLink } from '../../utils/swell-utils';
import { useIsIframed } from '../../utils/useIsIframed';
import { useIsWidget } from '../../utils/useIsWidget';
import { SearchForm } from '../../view/website/search/SearchView';
import { Mugshot } from '../common/Mugshot';
import { SmartLink } from '../common/SmartLink';
import { SwellLogo } from '../common/SwellLogo';
import { ColorModeToggle } from '../common/colormode/ColorModeToggle';
import { StoreAction } from '../common/downloadapp/DownloadAppButton';
import { useDebug } from '../debug/useDebug';
import { useAuth } from '../login/useAuth';
import { SwellAnchor, SwellButton } from '../swellbutton/SwellButton';
import { Notification } from './Notification';
import { useHeaderHeight } from './useHeaderHeight';
import { useNavSettings } from './useNavSettings';
import { useHasNotifications } from './useNotifications';

export interface INavSettings {
  userNavIsOpen: boolean;
  navIsOpen: boolean;
  violatorIsVisible: boolean;
  useIframe: boolean;
}

const PeekabooDrawer = ({ open = false, children, onClickBackdrop, align = 'right', fillHeight = true, className = '', zIndex = 9999 }: { zIndex?: number; open?: boolean; children: React.ReactNode; onClickBackdrop(): void; align?: 'left' | 'right'; fillHeight?: boolean; className?: string }) => {
  const containerStyle: React.CSSProperties = {
    transition: open ? 'background 0.3s, visibility 0s ease-in 0s' : 'background 0.3s, visibility 0s ease-in 0.3s',
    visibility: open ? 'visible' : 'hidden',
  };

  const innerStyle: React.CSSProperties = {
    transform: align === 'left' ? (open ? 'translateX(0)' : 'translateX(-100%)') : open ? 'translateX(0)' : 'translateX(100%)',
    transition: open ? 'transform 0.3s ease-out' : 'transform 0.3s ease-in',
    ...(align === 'left' ? { left: 0 } : { right: 0 }),
  };

  return (
    <div
      onClick={onClickBackdrop}
      data-component='peekaboodrawer'
      style={{
        zIndex, //
        position: 'fixed',
        inset: 0,
        willChange: 'background, visibility',
        background: open ? 'rgba(40,40,40,0.5)' : 'rgba(40,40,40,0)',
        overflow: 'auto',
        ...containerStyle,
      }}
    >
      <div
        className={className}
        style={{
          position: 'absolute',
          top: 0,
          minHeight: fillHeight ? '100%' : 'auto',
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          ...innerStyle,
        }}
      >
        <div style={{ flex: '1 1 auto', position: 'relative' }}>{children}</div>
      </div>
    </div>
  );
};

const HideOnScroll = ({ children, disabled = false }: { children: JSX.Element; disabled?: boolean }) => {
  const trigger = useScrollTrigger();
  return (
    <Slide appear={false} direction='down' in={disabled ? true : !trigger}>
      {children}
    </Slide>
  );
};

const AppButton = () => {
  return (
    <div className='btn-appicon'>
      <SwellAppIcon id='AppButton' />
    </div>
  );
};

export const WebsiteHeaderSpacer = (props: React.HTMLAttributes<HTMLDivElement> & { style?: React.CSSProperties }) => {
  const height = useHeaderHeight();
  return <div data-component='WebsiteHeaderSpacer' {...props} style={{ ...(props?.style ?? {}), height }} />;
};

const NavBarInner = () => {
  const nav = useNavSettings();
  const auth = useAuth();
  const message = <span className='h4 text-nowrap'>Get&nbsp;the&nbsp;App&nbsp;&#9656;</span>;
  const showUser = auth.loggedIn && auth.isReady;
  const isLoading = !auth.isReady;
  const userImage = auth?.user?.image;
  const userColor = showUser || isLoading ? ThemeColors.squash : 'rgba(128,128,128,0.3)';
  const userName = auth?.user?.alias ?? '';
  const framed = useIsIframed();
  const isWidget = useIsWidget();
  const targetForMePage = framed || isWidget ? '_blank' : '_self';
  const debugNav = useDebug('nav');
  const useMenuNav = debugNav.debug;

  const onClickMugshot: React.MouseEventHandler<HTMLAnchorElement> = (e) => {
    if (useMenuNav) {
      e.preventDefault();
      nav.update({ userNavIsOpen: !nav.userNavIsOpen });
    }
  };

  const onClickMenu = () => {
    nav.update({ navIsOpen: !nav.navIsOpen });
  };

  const onClickSignIn = () => {
    dataLayer({ event: 'signin', context: 'header' });
  };

  return (
    <div
      data-component='mobileheader' //
      className={`d-grid w-100 align-items-center gap-2 bg-mode text-mode`}
      style={{
        gridTemplateColumns: '1fr max-content 1fr', //'min-content auto auto auto min-content', //
        borderBottom: '1px solid rgba(128,128,128,0.3)',
        zIndex: 10,
      }}
    >
      <div className='d-flex align-items-center'>
        <div onClick={onClickMenu} className='flex-center pointer' style={{ width: 60, height: 60 }} tabIndex={0} role='button'>
          {nav.navIsOpen ? <SwellIcons.Close /> : <SwellIcons.Menu />}
        </div>

        <div className='d-flex align-items-center justify-content-start'>
          <SmartLink to='/' className='pointer' aria-label='home'>
            <SwellLogo style={{ height: 30 }} className='swell-logo-nav' id='NavBarInner' />
          </SmartLink>
        </div>
      </div>

      <div className='search-cell'>
        <div className='d-none d-lg-block' style={{ maxWidth: 500, minWidth: '33vw', width: '100%' }}>
          <SearchForm className='rounded-pill' />
        </div>
      </div>

      <div className='d-flex gap-3 align-items-center justify-content-end overflow-visible' style={{ overflow: 'visible' }}>
        <StoreAction context='website-header-cta' className={auth.loggedIn ? 'd-none' : 'd-none gap-2 fix align-items-center justify-content-end'}>
          <h4 className='h2'>{message}</h4>
          <AppButton />
        </StoreAction>

        {auth.loggedIn ? (
          <div className='d-flex gap-2 align-items-center pe-3'>
            <Notification />
            <SmartLink onClick={onClickMugshot} to={`/${auth.user?.alias.toLowerCase()}`} className='fix position-relative'>
              <Mugshot image={userImage} size={45} isLoading={isLoading} progress={1} color={userColor} alt={userName} />
              {useMenuNav ? (
                <div className='rounded-circle bg-white flex-center' style={{ boxShadow: 'rgba(0, 0, 0, 0.3) 0px 1px 4px 0px', width: 16, height: 16, position: 'absolute', bottom: 0, right: 0 }}>
                  {nav.userNavIsOpen ? <ExpandLess style={{ color: '#000' }} /> : <ExpandMore style={{ color: '#000' }} />}
                </div>
              ) : null}
            </SmartLink>
          </div>
        ) : (
          <a href='/me/' onClick={onClickSignIn} target={targetForMePage} className='fix text-mode d-flex align-items-center justify-content-end pointer pe-3' tabIndex={1} role='button'>
            <div className='Xbtn d-flex align-items-center gap-2' style={{ height: 'inherit' }}>
              {showUser ? null : <div className='fs-3 fw-bold text-mode text-nowrap'>Sign In ▸</div>}
              <Mugshot image={userImage} size={45} isLoading={isLoading} progress={1} color={userColor} alt={userName} />
            </div>
          </a>
        )}
      </div>
    </div>
  );
};

const UserDrawerInner = () => {
  const { loggedIn, user } = useAuth();
  const hasNotifications = useHasNotifications();
  const history = useNavigate();
  const nav = useNavSettings();
  const message = <span className='h4 text-nowrap'>Get&nbsp;the&nbsp;App&nbsp;&#9656;</span>;
  const orch = useOrchestration();
  const newSwellDisabled = orch.isRecording;

  const onClickNew = () => {
    history(`/${user?.alias?.toLowerCase() ?? ''}?action=new&debug=nav`);
  };

  const onClickMySwellcast = () => {
    history(`/${user?.alias?.toLowerCase() ?? ''}?debug=nav`);
  };

  const onClickAccount = () => {
    window.open('/me/', '_self');
  };

  const onClickFaq = () => {
    window.open('https://www.swell.life/faqs', '_blank');
  };

  if (loggedIn) {
    if (nav.useIframe) {
      return (
        <nav className='position-relative d-flex flex-column gap-3 p-4 rounded-bottom-left text-mode bg-mode' style={{ width: 320, maxWidth: '80vw', height: '100%' }}>
          <iframe className='absolute-fill' sandbox='allow-forms allow-scripts' style={{ border: 'none', margin: 0, padding: 0 }} src='/me/' />
        </nav>
      );
    }

    return (
      <nav className='d-flex flex-column gap-3 p-4 rounded-bottom-left text-mode bg-mode' style={{ width: 320, maxWidth: '80vw' }}>
        <WebsiteHeaderSpacer />
        {user?.alias ? (
          <div className='d-flex gap-2'>
            <Mugshot image={user?.image} size={65} />
            <div className='d-flex flex-column justify-content-center'>
              <h2>{user?.name}</h2>
              <h4 className='fw-light'>
                <SmartLink className='fix' to={getSwellcastLink({ listType: SwellListType.SWELLCAST, listId: user?.alias?.toLowerCase() })}>
                  @{user?.alias}
                </SmartLink>
              </h4>
            </div>
          </div>
        ) : null}

        <div className='d-flex flex-column gap-2'>
          <SwellButton.NewSwell className='w-100 btn-lg' disabled={newSwellDisabled} onClick={onClickNew} />
          <div className='position-relative w-100'>
            <SwellAnchor.Secondary className='w-100 btn-lg' href='/me/?op=notifications'>
              Notifications
            </SwellAnchor.Secondary>
            {hasNotifications.isSuccess && hasNotifications.data === true ? <div className='bg-notification-red rounded-circle position-absolute' style={{ width: 10, height: 10, top: 0, right: 0, transform: 'translate(25%, -20%)' }} /> : null}
          </div>
          <SwellButton.Secondary className='w-100 btn-lg' onClick={onClickMySwellcast}>
            My Swellcast
          </SwellButton.Secondary>
          <SwellButton.Secondary className='w-100 btn-lg' onClick={onClickAccount}>
            Account
          </SwellButton.Secondary>
          <SwellButton.Secondary className='w-100 btn-lg' onClick={onClickFaq}>
            FAQs
          </SwellButton.Secondary>
        </div>
      </nav>
    );
  }

  return (
    <nav className='d-flex flex-column gap-3 p-4 rounded-bottom-left text-mode bg-mode' style={{ width: 320, maxWidth: '70vw' }}>
      <StoreAction context='website-header-cta' className={loggedIn ? 'd-none' : 'gap-2 fix d-flex align-items-center justify-content-end'}>
        <h4 className='h2'>{message}</h4>
        <AppButton />
      </StoreAction>
    </nav>
  );
};

const SidebarInner = () => {
  const env = useEnv();
  const nav = useNavSettings();

  return (
    <nav data-component='drawerinner' className='d-flex flex-column text-mode bg-mode' style={{ maxWidth: '76vw', minHeight: '100%' }}>
      <WebsiteHeaderSpacer />
      <div className='d-lg-none' onClick={(e) => e.stopPropagation()}>
        <SearchForm onSubmit={() => nav.update({ navIsOpen: !nav.navIsOpen })} />
      </div>
      <div className='d-flex flex-column gap-2 pt-3'>
        <NavLink to='/' className='nav-link'>
          <SwellIcons.Home width={22} />
          Home
        </NavLink>

        <SmartLink to='/me/' className='nav-link' reloadDocument={true}>
          <SwellIcons.Person width={20} />
          Account
        </SmartLink>

        <SmartLink utm={true} to='https://swell.life' className='nav-link'>
          <SwellIcons.Find width={22} />
          About Swell
        </SmartLink>

        <SmartLink utm={true} to={env.SWELL_DEVELOPERS_URL} className='nav-link'>
          <SwellIcons.Speak width={22} />
          Swell Widget
        </SmartLink>

        <StoreAction context='website-nav' className={'nav-link'}>
          <Download style={{ width: 22 }} />
          Get the App
        </StoreAction>

        <hr />
        <div className='d-flex flex-column ps-4 pe-1'>
          <ColorModeToggle />
        </div>
        <hr />

        <SmartLink to='/copyrightpolicy' className='nav-link' reloadDocument={true}>
          Copyright Policy
        </SmartLink>

        <SmartLink to='/termsofservice' className='nav-link' reloadDocument={true}>
          Terms of Service
        </SmartLink>

        <SmartLink to='/privacypolicy' className='nav-link' reloadDocument={true}>
          Privacy Policy
        </SmartLink>

        <SmartLink utm={true} to='https://swell.life/communityguidelines' className='nav-link'>
          Community Guidelines
        </SmartLink>

        <SmartLink utm={true} to='https://swell.life/troubleshooting' className='nav-link'>
          Troubleshooting
        </SmartLink>

        <SmartLink utm={true} to='https://swell.life/faqs' className='nav-link'>
          FAQs
        </SmartLink>

        <SmartLink utm={true} to='https://swell.life/contact' className='nav-link'>
          Contact
        </SmartLink>
      </div>
      <WebsiteHeaderSpacer />
    </nav>
  );
};

export const WebsiteHeader = () => {
  const nav = useNavSettings();

  useEffect(() => {
    document.body.style.overflow = nav.navIsOpen || nav.userNavIsOpen ? 'hidden' : '';
  }, [nav.navIsOpen, nav.userNavIsOpen]);

  const scrollDisabled = nav.navIsOpen || nav.userNavIsOpen;

  const onClickBackdrop = () => {
    nav.update({ navIsOpen: false, userNavIsOpen: false });
  };

  const queryClient = useQueryClient();
  const $ref = useRef<HTMLElement>(null!);
  const size = useResizeObserver({ ref: $ref });

  useEffect(() => {
    queryClient.setQueryData(['header-height'], size.height);
  }, [queryClient, size]);

  return (
    <div style={{ display: 'flex' }}>
      <HideOnScroll disabled={scrollDisabled}>
        <AppBar ref={$ref} position='fixed' style={{ zIndex: zIndex.topNav }} className='bg-mode' sx={{ backgroundColor: '#000' }}>
          <NavBarInner />
        </AppBar>
      </HideOnScroll>

      <PeekabooDrawer open={nav.navIsOpen} onClickBackdrop={onClickBackdrop} align='left' className='text-mode bg-mode' zIndex={zIndex.leftNav}>
        <SidebarInner />
      </PeekabooDrawer>

      <PeekabooDrawer open={nav.userNavIsOpen} onClickBackdrop={onClickBackdrop} align='right' fillHeight={false} zIndex={zIndex.rightNav}>
        <UserDrawerInner />
      </PeekabooDrawer>
    </div>
  );
};
