import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { INavSettings } from './header';

const DEFAULT_NAV_DATA = {
  userNavIsOpen: false,
  navIsOpen: false,
  violatorIsVisible: false,
  useIframe: false,
};

export function useNavSettings() {
  const queryKey = ['nav-info'];
  const queryClient = useQueryClient();

  const query = useQuery<INavSettings>({
    queryKey,
    enabled: false,
    gcTime: Infinity,
    queryFn: () => DEFAULT_NAV_DATA,
    initialData: DEFAULT_NAV_DATA,
  });

  const update = (data: Partial<INavSettings>) => {
    queryClient.setQueryData<INavSettings>(queryKey, (d) => ({ ...d, ...data } as INavSettings));
  };

  useEffect(() => {
    update({ useIframe: window.localStorage.getItem('useIframe') === '1' });
  }, []);

  useEffect(() => {
    if (query.data.navIsOpen) {
      update({ userNavIsOpen: false });
    }
  }, [query.data.navIsOpen]);

  useEffect(() => {
    if (query.data.userNavIsOpen) {
      update({ navIsOpen: false });
    }
  }, [query.data.userNavIsOpen]);

  return { ...query.data, update };
}
