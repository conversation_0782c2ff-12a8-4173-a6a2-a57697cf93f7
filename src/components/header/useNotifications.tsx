import { keepPreviousData, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { inBrowser } from '../../utils/Utils';
import { useAuth } from '../login/useAuth';

export const useHasNotifications = () => {
  const location = useLocation();
  const queryClient = useQueryClient();
  const auth = useAuth();

  useEffect(() => {
    queryClient.refetchQueries({ queryKey: ['user-notifications'] });
  }, [location]);

  return useQuery({
    queryKey: ['user-notifications'], //
    queryFn: async () => window?.sw_notfn() ?? false,
    enabled: inBrowser && auth.loggedIn,
    placeholderData: keepPreviousData,
  });
};
