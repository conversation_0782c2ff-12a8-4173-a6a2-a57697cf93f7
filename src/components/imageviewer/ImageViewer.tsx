import classNames from 'classnames';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { OpenApiReplyResponseEnhanced, OpenApiSwellResponseEnhanced } from '../../api/gql.loadSwellById';
import { SwellIcons } from '../../assets/icons/SwellIcons';
import { BrunoWaveCard } from '../../framework/settings/settings';
import { useAudioMode } from '../../framework/useAudioMode';
import { ArticleType, OpenApiReplyResponse, OpenApiSwellResponse } from '../../generated/graphql';
import { hasArticleImage } from '../../utils/swell-utils';
import { useImage } from '../../utils/useImage';
import { isVideoFile } from '../../view/sound/media/hooks/isVideoFile';
import { FullscreenButton } from '../../view/sound/ui/FullscreenButton';
import { VideoOutlet } from '../../view/sound/video/VideoOutlet';
import { CircleLoader } from '../common/circleloader/CircleLoader';
import { AudioEvents } from '../player/AudioPlayer/IAudioPlayer';

export const ImageViewer = ({ swell, className }: { swell: OpenApiSwellResponse | OpenApiSwellResponseEnhanced | OpenApiReplyResponseEnhanced | OpenApiReplyResponse; className?: string }) => {
  return (
    <div>
      <ImageViewerInner swell={swell} className={className} />
    </div>
  );
};

const ImageViewerInner = ({ swell, className = '' }: { swell: OpenApiSwellResponse | OpenApiSwellResponseEnhanced | OpenApiReplyResponseEnhanced | OpenApiReplyResponse; className?: string }) => {
  const { audioQuery } = useAudioMode();
  const isTrackActive = audioQuery.trackId == swell.id;
  const isVideo = isVideoFile(swell.audio?.url ?? '');
  const hasImages = hasArticleImage(swell) || isVideo;
  const [index, setIndex] = useState(0);
  const $slides = useRef<Map<number, HTMLAnchorElement>>(new Map());
  const maxIndex = (swell.articles?.length ?? 1) - 1;
  const showVideo = isTrackActive && isVideo;

  function handleScroll(e: React.UIEvent<HTMLDivElement, UIEvent>): void {
    const w = e.currentTarget.getBoundingClientRect().width - 2;
    const x = e.currentTarget.scrollLeft + w / 2;
    setIndex(~~(x / w));
  }

  function scrollTo(i: number) {
    $slides?.current?.get(i)?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
  }

  const setSlideRef = useCallback((el: HTMLAnchorElement | null, index: number) => {
    if (el) {
      $slides.current.set(index, el);
    } else {
      $slides.current.delete(index);
    }
  }, []);

  if (!hasImages) return null;

  const bgBlur = 5;

  return (
    <div data-component='image-viewer' data-index={index} className={className}>
      <div className='image-viewer-scroller' onScroll={handleScroll}>
        <div className={isVideo ? 'image-viewer-video image-viewer-image bg-grey-70' : 'd-none'}>
          <VideoPreviewFrame
            src={swell.audio!.url!}
            style={{
              zIndex: 1, //
              filter: `blur(${bgBlur}px) brightness(0.7)`,
              position: 'absolute',
              inset: -bgBlur * 2,
              objectFit: 'cover',
              objectPosition: 'center',
              width: `calc(100% + ${bgBlur * 4}px)`,
              height: `calc(100% + ${bgBlur * 4}px)`,
            }}
          />
          <VideoPreviewFrame
            src={swell.audio!.url!}
            style={{
              zIndex: 2, //
              position: 'absolute',
              inset: 0,
              display: isTrackActive ? 'none' : 'block',
              objectFit: 'contain',
              objectPosition: 'center',
              width: '100%',
              height: '100%',
            }}
          />
          <VideoOutlet
            active={showVideo}
            style={{
              zIndex: 3, //
              position: 'absolute',
              inset: 0,
            }}
          />
          <FullscreenButton
            data-test={isTrackActive ? 'isTrackActive' : 'not'}
            className={isTrackActive ? 'd-block' : 'd-none'}
            style={{
              zIndex: 4, //
              position: 'absolute',
              right: 5,
              bottom: 5,
            }}
          />
        </div>

        {swell?.articles?.map((article, i) => (
          <ArticleImage key={`image-viewer-slide-${i}-${swell.id}`} article={article} ref={(el) => setSlideRef(el, i)} />
        ))}
      </div>

      <div
        className='p-3 d-none d-lg-flex pointer'
        style={{ justifyContent: 'center', alignItems: 'center', transition: 'opacity 0.3s', zIndex: 2, position: 'absolute', left: 0, top: 0, bottom: 0, opacity: index === 0 ? 0 : 1 }}
        onClick={(e) => {
          e.stopPropagation();
          scrollTo(Math.max(0, index - 1));
        }}
      >
        <SwellIcons.ChevronLeft style={{ width: 24, '--icon-color': '#fff' }} />
      </div>

      <div
        className='p-3 d-none d-lg-flex pointer'
        style={{ justifyContent: 'center', alignItems: 'center', transition: 'opacity 0.3s', zIndex: 2, position: 'absolute', right: 0, top: 0, bottom: 0, opacity: index === maxIndex ? 0 : 1 }}
        onClick={(e) => {
          e.stopPropagation();
          scrollTo(Math.min(maxIndex, index + 1));
        }}
      >
        <SwellIcons.ChevronRight style={{ width: 24, '--icon-color': '#fff' }} />
      </div>

      <NavDots slides={swell?.articles ?? []} onClick={scrollTo} index={index} />
    </div>
  );
};

const ArticleImage = ({ article, ref }: { article: ArticleType; ref: React.Ref<HTMLAnchorElement> }) => {
  const { $img, isError, isSuccess } = useImage({ src: article?.image ?? '', alt: BrunoWaveCard });
  const hideLoader = isSuccess || isError;
  const mediumImage = article.image ?? '';
  const highImage = article.image?.replace('SWELL_MEDIUM', 'SWELL_HIGH');
  const href = article.link === article.image ? highImage : article.link;

  return (
    <a
      className='image-viewer-image bg-grey-70' //
      onClick={(e) => e.stopPropagation()}
      ref={ref}
      title={article?.title ?? ''}
      href={href}
      target='_blank'
      rel='noreferrer'
    >
      <img
        className='image-viewer-bg' //
        src={mediumImage}
        loading='lazy'
        alt='article image placeholder'
      />
      <img
        className='image-viewer-main' //
        ref={$img}
        src={mediumImage}
        draggable={false}
        loading='lazy'
        alt={article.title ?? ''}
      />
      {hideLoader ? null : (
        <div className='image-viewer-loader'>
          <CircleLoader />
        </div>
      )}
    </a>
  );
};

export const NavDots = ({ slides, index, onClick, className }: { slides: Array<unknown>; index: number; onClick(i: number): void; className?: string }) => {
  return slides.length > 1 ? (
    <div className={classNames('nav-dots', className)} data-index={index}>
      {slides.map((_slide, i) => (
        <div
          key={`nav-dot-${i}`}
          onClick={(e) => {
            e.stopPropagation();
            onClick(i);
          }}
          className='nav-dot'
          data-index={i}
        />
      ))}
    </div>
  ) : null;
};

type VideoPreviewFrameProps = {
  src: string;
} & React.CanvasHTMLAttributes<HTMLCanvasElement>;

const VideoPreviewFrame: React.FC<VideoPreviewFrameProps> = ({ src, ...props }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const captureFrame = () => {
      const canvas = canvasRef.current;
      if (canvas && video.videoWidth && video.videoHeight) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(video, 0, 0, canvas.width, canvas.height);
        setLoaded(true);
        video.pause();
      }
    };

    const handleLoadedData = () => {
      // Seek to the beginning to get the first frame
      video.currentTime = 0;
    };

    const handleSeeked = () => {
      captureFrame();
    };

    video.addEventListener(AudioEvents.LOADEDMETADATA, handleLoadedData);
    video.addEventListener(AudioEvents.LOADEDDATA, handleLoadedData);
    video.addEventListener(AudioEvents.SEEKED, handleSeeked);

    return () => {
      video.removeEventListener(AudioEvents.LOADEDMETADATA, handleLoadedData);
      video.removeEventListener(AudioEvents.LOADEDDATA, handleLoadedData);
      video.removeEventListener(AudioEvents.SEEKED, handleSeeked);
    };
  }, [src]);

  return (
    <>
      <canvas ref={canvasRef} {...props} data-loaded={loaded ? '1' : '0'} />
      {/* Hidden video used for loading the frame */}
      <video className='video-preview-frame' ref={videoRef} src={src} style={{ display: 'none' }} muted playsInline preload='metadata' />
    </>
  );
};
