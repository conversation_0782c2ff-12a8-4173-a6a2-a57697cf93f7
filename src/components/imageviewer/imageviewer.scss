@use 'sass:map';
@use '../../assets/css/base' as *;

[data-component='image-viewer'] {
  @extend .d-flex, .rounded-4, .overflow-hidden;
  --nav-dot-color: #fff;

  position: relative;
  touch-action: manipulation;

  .image-viewer-scroller {
    @extend .landscape;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    scroll-snap-align: center;
  }

  .image-viewer-scroller::-webkit-scrollbar {
    display: none;
  }

  .image-viewer-image {
    @extend .no-select;
    position: relative;
    aspect-ratio: inherit;
    scroll-snap-stop: normal;
    scroll-snap-align: center;
    width: 100%;
    flex: 0 0 100%;
    min-width: 1px;
    box-sizing: content-box;
    overflow: hidden;

    & > .image-viewer-bg {
      z-index: 1;
      position: absolute;
      inset: 0;
      background-size: cover;
      transform: scale(1.1);
      filter: blur(12px) saturate(90%) brightness(50%);
      overflow: hidden;
      width: 100%;
      height: 100%;
    }

    & > .image-viewer-main {
      z-index: 2;
      position: relative;
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
    }

    & > .image-viewer-loader {
      @extend .flex-center, .bg-grey-70;
      z-index: 3;
      position: absolute;
      inset: 0;
      background-size: cover;
    }
  }
  .nav-dot:after {
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.6);
  }
  .nav-dots {
    position: absolute;
    bottom: 3px;
    width: 100%;
    padding: 5px;
    z-index: 5;
  }
}
/*

nav dots

*/
:root {
  --nav-dot-color-active: #{map.get($theme-colors, 'squash')};
}
[data-bs-theme='dark'] {
  --nav-dot-color: #fff;
}
[data-bs-theme='light'] {
  --nav-dot-color: #000;
}
.nav-dots {
  display: flex;
  gap: 1px;
  justify-content: center;
  align-items: center;

  > .nav-dot {
    @extend .pointer;
    padding: 2px;
    flex: 0 0 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    &:after {
      transition: all 0.2s;
      content: '';
      display: block;
      width: auto;
      width: 8px;
      height: 8px;
      background-color: var(--nav-dot-color);
      border-radius: 100vw;
    }
  }
  @for $i from 0 through 50 {
    &[data-index='#{$i}'] {
      .nav-dot[data-index='#{$i}']::after {
        background-color: var(--nav-dot-color-active);
        width: 100%;
      }
    }
  }
}
