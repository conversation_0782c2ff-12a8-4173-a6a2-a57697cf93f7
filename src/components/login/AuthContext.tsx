import { isServer } from '@tanstack/react-query';
import React from 'react';

export interface userInfo {
  alias: string;
  image: string;
  name: string;
  id: string;
}

interface IAuth {
  loggedIn: boolean;
  isReady: boolean;
  isPremium: boolean;
  user: userInfo | null;
  update(): void;
  getAlias(): string;
  getToken(): Promise<string | null>;
}

export const AuthContext = React.createContext<IAuth>({
  loggedIn: false,
  isReady: isServer,
  isPremium: false,
  user: {
    alias: 'swell_guest',
    image: '',
    name: '',
    id: '',
  },
  update: () => {},
  getToken: async () => '',
  getAlias: () => '',
});
