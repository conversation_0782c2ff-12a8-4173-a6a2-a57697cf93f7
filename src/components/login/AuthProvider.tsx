import { focusManager, isServer, useQueryClient } from '@tanstack/react-query';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { isValidToken } from '../../api/gql.common';
import { inBrowser } from '../../utils/Utils';
import { useDOMContentLoaded } from '../../utils/useDomContentLoaded';
import { AuthContext, userInfo } from './AuthContext';

export interface IAuthTrack {
  swellId: string;
  replyId: string;
  audioUrl: string;
  totalDuration: number;
  currentDuration: number;
  playrate: number;
  eventtype: 'start' | 'stop' | 'play' | 'complete'; // play is for progress, stop = pause/complete
}

interface ICache<T> {
  promise: Promise<T | null> | null;
  timestamp: number;
  maxAge: number;
  data: T | null;
  isCached: boolean;
}

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const queryClient = useQueryClient();
  const location = useLocation();
  const [isReady, setIsReady] = useState(isServer);
  //   const [isReady, setIsReady] = useState(isServer ? true : false);
  const [isPremium, setIsPremium] = useState(false);
  const [loggedIn, setLoggedIn] = useState(false);
  const [user, setUser] = useState<userInfo | null>(null);
  const tokenPromise = useRef<ICache<string>>({
    promise: null, //
    timestamp: 0,
    maxAge: 200,
    data: null,
    isCached: false,
  });
  const userPromise = useRef<ICache<userInfo>>({
    promise: null, //
    timestamp: 0,
    maxAge: 200,
    data: null,
    isCached: false,
  });

  // try to invalidate data when safari uses back button
  useDOMContentLoaded(() => {
    queryClient.invalidateQueries();
  });

  // for tecnical reasons the login needs to be open in a new tab sometimes
  // this fixes the refresh on login status if it changes
  useEffect(() => {
    const handleFocus = () => update();
    window.addEventListener('visibilitychange', handleFocus, false);
    window.addEventListener('focus', handleFocus, false);
    return () => {
      window.removeEventListener('visibilitychange', handleFocus);
      window.removeEventListener('focus', handleFocus);
    };
  }, [focusManager.isFocused]);

  const isPremiumUser = async () => {
    if (isServer) return false;
    return window?.sw_phub?.() ?? false;
  };

  const getUser = async () => {
    if (!inBrowser) return null;

    const age = Date.now() - userPromise.current.timestamp;
    if (userPromise.current.isCached && age < userPromise.current.maxAge) {
      return userPromise.current.data;
    }
    userPromise.current.isCached = false;
    if (!userPromise.current.promise) {
      userPromise.current.promise = (async () => {
        const _user = (await window?.sw_gu?.()) ?? null;
        userPromise.current.data = _user;
        userPromise.current.timestamp = Date.now();
        userPromise.current.isCached = true;
        userPromise.current.promise = null;
        return userPromise.current.data;
      })();
    }
    return userPromise.current.promise;
  };

  const getToken = async () => {
    if (!inBrowser) return '';

    const age = Date.now() - tokenPromise.current.timestamp;
    if (age < tokenPromise.current.maxAge) {
      return tokenPromise.current.data;
    }
    tokenPromise.current.isCached = false;
    if (!tokenPromise.current.promise) {
      tokenPromise.current.promise = (async () => {
        let _user = await getUser();
        let _token = '';
        let _loggedIn = !!_user?.alias;
        if (_loggedIn) {
          _token = await window.sw_ah();
          if (!isValidToken(_token)) {
            _loggedIn = false;
            _token = '';
            _user = null;
          }
        }
        tokenPromise.current.data = _token;
        tokenPromise.current.timestamp = Date.now();
        tokenPromise.current.isCached = true;
        tokenPromise.current.promise = null;
        return tokenPromise.current.data;
      })();
    }
    return Promise.resolve(tokenPromise.current.promise);
  };

  const update = async () => {
    let _user = await getUser();
    let _token = null;
    let _loggedIn = !!_user?.alias;
    let _premium = false;
    if (_loggedIn) {
      _token = await getToken();
      if (!isValidToken(_token)) {
        _loggedIn = false;
        _token = '';
        _user = null;
      }
      _premium = await isPremiumUser();
    }
    setUser(_user);
    setLoggedIn(_loggedIn);
    setIsPremium(_premium);
    setIsReady(true);
  };

  useEffect(() => {
    // check login status on navigate
    update();
  }, [location]);

  useEffect(() => {
    if (!loggedIn && tokenPromise.current.isCached) {
      // clear data on logout
      tokenPromise.current.data = null;
      userPromise.current.data = null;
      queryClient.invalidateQueries();
    }
  }, [loggedIn]);

  useEffect(() => {
    // hide the page until auth resolves
    // the js that hides the div#root is in /public/html/index.ejs
    if (isReady) {
      const $root = document.getElementById('root');
      if ($root) {
        $root.style.display = '';
      }
    }
  }, [isReady]);

  const getAlias = () => {
    if (loggedIn) {
      return user?.alias ?? 'swell_guest';
    }
    return 'swell_guest';
  };

  return <AuthContext.Provider value={{ isReady, user, loggedIn, update, getToken, isPremium, getAlias }}>{children}</AuthContext.Provider>;
};
