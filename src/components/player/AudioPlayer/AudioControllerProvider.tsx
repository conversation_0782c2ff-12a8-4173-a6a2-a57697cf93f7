import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { useCurrentSwell } from '../../../api/gql.loadSwellById';
import { useAudioMode } from '../../../framework/useAudioMode';
import { AudioMode } from '../../../models/models';
import { isTextFile } from '../../../utils/swell-utils';
import { useDebug } from '../../debug/useDebug';
import { useSettings } from '../../settings/useSettings';
import { AudioControllerContext } from './AudioControllerContext';
import { AudioPlaylistItem, AudioReadyState } from './IAudioPlayer';
import { useAudioTrack } from './useAudioTrack';

export const AudioControllerProvider = ({ children }: { children: ReactNode }) => {
  const { debug } = useDebug('AudioControllerProvider');
  const track = useAudioTrack();
  const audioMode = useAudioMode();
  const settings = useSettings();
  const swell = useCurrentSwell();
  const [tracks, setTracks] = useState<AudioPlaylistItem[]>([]);
  const currentTrack = useMemo(() => tracks.find((t) => t.id === audioMode.audioQuery.trackId) || null, [audioMode.audioQuery.trackId, tracks]);
  const currentTrackIndex = useMemo(() => currentTrack?.index ?? -1, [currentTrack]);
  const hasNext = useMemo(() => (currentTrackIndex ?? 99999999) + 1 < tracks.length, [currentTrackIndex, tracks.length]);
  const hasPrev = useMemo(() => (currentTrackIndex ?? -1) - 1 >= 0, [currentTrackIndex]);
  const isLoadingData = useMemo(() => {
    if (swell.isLoading) return true;
    if (track.readyState === AudioReadyState.HAVE_METADATA) return true; // seeking
    if (track.isLoading && !track.canPlay) return true;
    if (swell.isSuccess) return false;
    return (swell.isLoading || swell.isFetching || track.isLoading) && !swell.isRefetching && swell.fetchStatus !== 'idle';
  }, [swell.isLoading, swell.isSuccess, swell.isFetching, swell.isRefetching, swell.fetchStatus, track.readyState, track.isLoading, track.canPlay]);
  const swellId = swell.isSuccess ? swell.data.id : '';

  const skipNext = useCallback(() => {
    if (hasNext && currentTrackIndex > -1 && tracks?.[currentTrackIndex + 1]?.id) {
      audioMode.setAudioQuery({ trackId: tracks[currentTrackIndex + 1].id });
    }
  }, [audioMode, currentTrackIndex, hasNext, tracks]);

  const skipPrev = useCallback(() => {
    if (hasPrev && currentTrackIndex > -1 && tracks?.[currentTrackIndex - 1]?.id) {
      audioMode.setAudioQuery({ trackId: tracks[currentTrackIndex - 1].id });
    }
  }, [audioMode, currentTrackIndex, hasPrev, tracks]);

  // when current swell changes, we need to change the tracks
  useEffect(() => {
    if (swellId && swell.data) {
      if (debug) console.log('ACP.useEffect()', '[swell.isSuccess]');
      setTracks(
        [swell.data, ...(swell.data?.replies ?? [])].map(
          (s, i) =>
            ({
              id: s.id,
              src: s?.audio?.url ?? '',
              index: i,
              data: s,
              duration: s?.audio?.duration ?? 0,
              placeholder: s.placeholder,
            } as AudioPlaylistItem),
        ),
      );
    }
  }, [swell.data, debug, swellId]);

  // play track when audioMode updates or tracks are updated (post swell load)
  useEffect(() => {
    if (audioMode.audioMode === AudioMode.PLAY) {
      if (tracks?.length && swell.isSuccess) {
        if (!audioMode.audioQuery.trackId || !currentTrack) {
          audioMode.setAudioQuery({ trackId: swellId });
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tracks]);

  // once currentTrack is defined, we can play
  useEffect(() => {
    if (audioMode.audioMode === AudioMode.PLAY) {
      if (debug) console.log('ACP.useEffect()', '[currentTrack]', currentTrack?.id);
      if (currentTrack) {
        if (isTextFile(currentTrack.src)) {
          // TODO: this should check for valid media file
          track.eject();
        } else {
          track.setAutoPlay(settings.autoPlay);
          //   track.setSrc(currentTrack.src);
          track.play(currentTrack.src);
        }
      } else {
        track.pause();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    // audioMode.audioMode, //
    currentTrack,
    // currentTrack?.id,
    // debug,
    // settings.autoPlay,
  ]); // adding "track" to deps breaks play/pause

  // leave play mode
  useEffect(() => {
    if (audioMode.audioMode !== AudioMode.PLAY) {
      if (debug) console.log('ACP.useEffect()', '[audioMode.audioMode]');
      track.pause();
    } else {
      track.play();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [audioMode.audioMode, debug]); // adding "track" to deps breaks play/pause

  // auto play next track
  useEffect(() => {
    if (track.isComplete && hasNext) {
      if (debug) console.log('ACP.useEffect()', '[hasNext, skipNext, track.isComplete]');
      skipNext();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [track.isComplete]);

  const { debug: debugAutoplay } = useDebug('autoplay');

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      const tagName = (e.target as HTMLElement).tagName.toLowerCase();
      const isContentEditable = (e.target as HTMLElement).isContentEditable;

      if (tagName === 'input' || tagName === 'textarea' || isContentEditable) {
        // Allow space bar to function normally
        return;
      }

      switch (e.key) {
        case ' ':
          if (debugAutoplay) console.log('AudioControllerProvider::space key play');
          track.togglePlay();
          e.preventDefault();
          break;

        case 'ArrowRight':
          track.skip(5);
          e.preventDefault();
          break;

        case 'ArrowLeft':
          track.skip(-5);
          e.preventDefault();
          break;

        case 'ArrowUp':
          skipPrev();
          e.preventDefault();
          break;

        case 'ArrowDown':
          skipNext();
          e.preventDefault();
          break;
      }
    },
    [debugAutoplay, skipNext, skipPrev, track],
  );
  // add some handy keyboard controls
  useEffect(() => {
    if (audioMode.audioMode === AudioMode.PLAY) {
      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [track.isPlaying, audioMode.audioMode, handleKeyDown]);

  return (
    <AudioControllerContext.Provider
      value={{
        isLoadingData,
        swell,
        trackId: audioMode.audioQuery.trackId,
        playlistId: audioMode.audioQuery.playlistId,
        tracks,
        currentTrack,
        skipNext,
        hasNext,
        skipPrev,
        hasPrev,
      }}
    >
      {children}
    </AudioControllerContext.Provider>
  );
};
