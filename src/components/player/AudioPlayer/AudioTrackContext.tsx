import { createContext, RefObject } from 'react';
import { PlayerStatus } from '../../../models/models';
import { AudioReadyState } from './IAudioPlayer';

interface IAudioTrackContext {
  $audio: RefObject<HTMLVideoElement>; //
  progress: number;
  duration: number;
  position: number;
  isLoading: boolean;
  isPlaying: boolean;
  isPaused: boolean;
  isError: boolean;
  isBlocked: boolean;
  isComplete: boolean;
  hasMetaData: boolean;
  canPlay: boolean;
  buffer: number;
  bufferProgress: number;
  playbackRate: number;
  volume: number;
  autoPlay: boolean;
  shouldPlay: boolean;
  play(src?: string): void;
  pause(): void;
  eject(): void;
  seekStart(progress: number): void;
  seek(progress: number): void;
  seekEnd(progress: number): void;
  setVolume(volume: number): void;
  setAutoPlay(ap: boolean): void;
  setSrc(src: string): void;
  togglePlay(): void;
  skip(seconds: number): void;
  readyState: AudioReadyState;
  status: PlayerStatus;
}

export const AudioTrackContext = createContext<IAudioTrackContext>({
  $audio: { current: null }, //
  progress: 0,
  duration: 0,
  position: 0,
  volume: 1,
  isLoading: false,
  isPlaying: false,
  isPaused: false,
  isError: false,
  isBlocked: true,
  isComplete: false,
  hasMetaData: false,
  autoPlay: true,
  shouldPlay: true,
  canPlay: false,
  play: () => {},
  pause: () => {},
  eject: () => {},
  buffer: 0,
  bufferProgress: 0,
  playbackRate: 1,
  seekStart: () => {},
  seek: () => {},
  seekEnd: () => {},
  setVolume: () => {},
  setAutoPlay: () => {},
  setSrc: () => {},
  togglePlay: () => {},
  skip: () => {},
  readyState: AudioReadyState.HAVE_NOTHING,
  status: PlayerStatus.NONE,
});
