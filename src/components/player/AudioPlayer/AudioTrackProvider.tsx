import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import { PlayerStatus } from '../../../models/models';
import { useVideoElement } from '../../../view/sound/video/hooks/useVideoElement';
import { useDebug } from '../../debug/useDebug';
import { useSettings } from '../../settings/useSettings';
import { AudioTrackContext } from './AudioTrackContext';
import { AudioEvents, AudioReadyState } from './IAudioPlayer';

export const AudioTrackProvider = ({ children }: { children: React.ReactNode }) => {
  const { debug } = useDebug('audiotrack');
  const { $video: $audio } = useVideoElement();
  //   const $audio = useRef<HTMLAudioElement>(null!);
  //   const $audio = useAudio();
  const settings = useSettings();
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(settings?.volume ?? 1);
  const [buffer, setBuffer] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [isError, setIsError] = useState(false);
  const [canPlay, setCanPlay] = useState(false);
  const [autoPlay, setAutoPlay] = useState(true);
  const [hasMetaData, setHasMetaData] = useState(false);
  const [readyState, setReadyState] = useState(AudioReadyState.HAVE_NOTHING);
  const [src, setSrc] = useState('');
  const [shouldPlay, setShouldPlay] = useState(true);
  const progress = useMemo(() => (duration > 0 ? position / duration : 0), [position, duration]);
  const bufferProgress = useMemo(() => (duration > 0 ? buffer / duration : 0), [buffer, duration]);

  //   const isPaused = useMemo(() => !isLoading && canPlay && !isPlaying && !isComplete, [isPlaying, isComplete, canPlay, isLoading]);

  const status = useMemo(() => {
    if (isBlocked) {
      return PlayerStatus.BLOCKED;
    } else if (isPlaying) {
      return PlayerStatus.PLAYING;
    } else if (isComplete) {
      return PlayerStatus.COMPLETED;
    } else if (canPlay && !shouldPlay) {
      return PlayerStatus.PAUSED;
    } else if (isLoading) {
      return PlayerStatus.LOADING;
    } else if (shouldPlay) {
      return PlayerStatus.LOADING;
    } else {
      return PlayerStatus.NONE;
    }
  }, [isLoading, isPlaying, isBlocked, isComplete, canPlay, shouldPlay]);

  const handleEvent = useCallback(
    (e: Event) => {
      if (debug && e.type !== AudioEvents.TIMEUPDATE) console.log('AudioTrackProvider.handleEvent', e.type);
      // if (e.type !== AudioEvents.TIMEUPDATE) console.log('AudioTrackProvider.handleEvent', e.type);

      if (!$audio.current) return;

      if (e.type !== AudioEvents.TIMEUPDATE) {
        setReadyState($audio.current?.readyState ?? 0);
      }

      switch (e.type) {
        case AudioEvents.EMPTIED:
        case AudioEvents.LOADSTART:
          setBuffer(0);
          setPosition(0);
          setDuration(0);
          setIsPlaying(false);
          setIsPaused(false);
          setIsComplete(false);
          setCanPlay(false);
          setIsLoading(true);
          setIsError(false);
          setHasMetaData(false);
          break;

        case AudioEvents.PROGRESS:
          if ($audio.current.buffered.length > 0) {
            setBuffer($audio.current.buffered.end($audio.current.buffered.length - 1));
            setDuration($audio.current.duration);
          }
          break;

        case AudioEvents.TIMEUPDATE:
          setPosition($audio.current.currentTime);
          // this is handle outside of this code because the number of renders gets crazy if this is triggering a change
          break;

        case AudioEvents.DURATIONCHANGE:
          setDuration($audio.current.duration);

          break;

        case AudioEvents.CANPLAY:
          setCanPlay(true);
          break;

        case AudioEvents.CANPLAYTHROUGH:
          setIsLoading(false);
          break;

        case AudioEvents.PLAY:
          setIsLoading(true);
          setIsError(false);
          setIsComplete(false);
          setIsPaused(false);
          break;

        case AudioEvents.PLAYING:
          setIsLoading(false);
          setIsPlaying(true);
          setIsPaused(false);
          setIsError(false);
          setIsBlocked(false);
          break;

        case AudioEvents.PAUSE:
          setPosition($audio.current.currentTime);

          if ($audio.current.currentTime == $audio.current.duration) {
            // this is ended not paused
            return;
          }

          setIsPlaying(false);
          setIsPaused(true);
          setIsLoading(false);
          if ($audio.current.currentTime === 0) {
            // this is the safari browser blocking audio play - no way a human can pause at 0
            setIsBlocked(true);
          }
          break;

        case AudioEvents.ENDED:
          setIsPaused(false);
          setIsPlaying(false);
          setIsComplete(true);
          break;

        case AudioEvents.ERROR:
          if (debug) console.log('AudioProvider |', e);
          if ($audio.current.currentSrc !== '') {
            setIsError(true);
          }
          setIsPlaying(false);
          setIsPaused(false);
          setIsLoading(false);
          setCanPlay(false);
          break;

        case AudioEvents.STALLED:
        case AudioEvents.ABORT:
          setIsPlaying(false);
          setIsPaused(false);
          setIsLoading(false);
          setCanPlay(false);
          break;

        case AudioEvents.RATECHANGE:
          break;

        case AudioEvents.SEEKED:
        case AudioEvents.SEEKING:
          setPosition($audio.current.currentTime);
          break;

        case AudioEvents.SUSPEND:
        case AudioEvents.WAITING:
          break;

        case AudioEvents.VOLUMECHANGE:
          if ($audio.current) {
            setVolume($audio.current.volume);
          }
          break;

        case AudioEvents.LOADEDMETADATA:
          setHasMetaData(true);
          break;

        case AudioEvents.LOADEDDATA:
          break;
      }
    },
    [$audio, debug],
  );

  const fixPitch = ($v: HTMLVideoElement) => {
    $v.preservesPitch = true;
    ($v as unknown as { mozPreservesPitch: boolean }).mozPreservesPitch = true;
    ($v as unknown as { webkitPreservesPitch: boolean }).webkitPreservesPitch = true;
  };

  function play(_src?: string) {
    if (debug) console.log('AudioTrackProvider::play()', { _src, current: $audio.current?.src });
    if (_src && _src !== src) {
      setAutoPlay(true);
      setSrc(_src);
    }
    if ($audio.current?.readyState === AudioReadyState.HAVE_NOTHING) return;

    $audio.current
      ?.play()
      .then(() => setIsBlocked(false))
      .catch(() => {
        if (debug) console.log('play() catch', { _src, readyState: AudioReadyState[readyState], r: AudioReadyState[$audio.current?.readyState ?? 0] });
        setIsBlocked(true);
      });
  }

  const pause = useCallback(() => {
    if (debug) console.log('AudioTrackProvider::pause()');
    $audio.current?.pause();
    setIsPlaying(false);
  }, [debug, $audio]);

  function togglePlay() {
    if (debug) console.log('AudioTrackProvider::togglePlay()');
    setAutoPlay(!isPlaying);
    setShouldPlay(!isPlaying);
    if (isPlaying) {
      pause();
    } else {
      play();
    }
  }

  // seek helpers
  const shouldPlayRef = useRef(false);
  const isSeekingRef = useRef(false);

  const seekStart = useCallback(
    (progress: number) => {
      // Store the current playing state
      const wasPlaying = isPlaying;

      // Use a ref to track if we should resume playback
      shouldPlayRef.current = wasPlaying;

      // If currently playing, pause first
      if (wasPlaying) {
        pause();
      }

      // Update position without triggering re-renders
      const newPosition = progress * duration;
      setPosition(newPosition);
    },
    [isPlaying, pause, duration],
  );

  const seek = useCallback(
    (progress: number) => {
      // Skip if we're already in a seeking operation
      if (isSeekingRef.current) return;

      isSeekingRef.current = true;
      try {
        // Calculate new position
        const newPosition = progress * duration;

        // Update position state
        setPosition(newPosition);

        // Optionally update the audio element directly for smoother feedback
        if ($audio.current) {
          // Don't trigger timeupdate events by setting currentTime
          $audio.current.removeEventListener(AudioEvents.TIMEUPDATE, handleEvent);
          $audio.current.currentTime = newPosition;
          setTimeout(() => {
            $audio.current?.addEventListener(AudioEvents.TIMEUPDATE, handleEvent);
          }, 0);
        }
      } finally {
        // Reset seeking flag after a short delay
        setTimeout(() => {
          isSeekingRef.current = false;
        }, 0);
      }
    },
    [duration, $audio, handleEvent],
  );

  const seekEnd = useCallback(
    (progress: number) => {
      if ($audio.current) {
        // Use flushSync to ensure the DOM is updated immediately
        flushSync(() => {
          if ($audio.current) {
            $audio.current.currentTime = progress * duration;
          }
          setPosition(progress * duration);
        });

        // Resume playback if it was playing before
        if (shouldPlayRef.current) {
          $audio.current
            .play()
            .then(() => setIsBlocked(false))
            .catch(() => {
              if (debug) console.log('play() catch during seekEnd');
              setIsBlocked(true);
            });
        }
      }
    },
    [$audio, duration, debug],
  );

  const skip = (seconds = 5) => {
    if ($audio.current) {
      $audio.current.currentTime += seconds;
      setPosition($audio.current.currentTime);
    }
  };

  const eject = () => {
    pause();
    setSrc('');
    setDuration(0);
    setPosition(0);
    setIsPlaying(false);
    setIsPaused(false);
    setIsLoading(false);
    setCanPlay(false);
    setIsError(false);
  };

  function _setVolume(volume: number) {
    if ($audio.current) {
      $audio.current.volume = volume;
    }
  }

  //   useEffect(() => {
  //     console.log({ readyState: AudioReadyState[readyState] });
  //     if (readyState == AudioReadyState.HAVE_NOTHING) {
  //       setDuration(0);
  //     }
  //   }, [readyState]);

  useEffect(() => {
    const audioEl = $audio.current;
    if (!audioEl) return;

    const evts = Object.values(AudioEvents);
    // add listeners
    evts.forEach((e) => audioEl.addEventListener(e, handleEvent));

    return () => {
      // cleanup using the same audioEl reference
      evts.forEach((e) => audioEl.removeEventListener(e, handleEvent));
    };
  }, [$audio, handleEvent]);

  useEffect(() => {
    // if (debug) console.log('AudioTrackProvider::useEffect()', '[isPaused, isPlaying, canPlay, isLoading]', { isPaused, isPlaying, canPlay, isLoading });
    if (canPlay && !isPaused && !isPlaying && !isLoading) {
      // blocked could be a false alarm so wait a bit before declaring it blocked
      const t = setTimeout(() => {
        setIsBlocked(true);
      }, 500);

      return () => {
        clearTimeout(t);
      };
    }
  }, [isPaused, isPlaying, canPlay, debug, isLoading]);

  useEffect(() => {
    setShouldPlay(autoPlay);
  }, [autoPlay]);

  useEffect(() => {
    if (isError) {
      console.error({ isError });
    }
  }, [isError]);

  useEffect(() => {
    if ($audio.current) {
      $audio.current.volume = settings.volume || 1;
    }
  }, [$audio, settings.volume]);

  useEffect(() => {
    if ($audio.current) {
      $audio.current.src = src;
    }
  }, [$audio, src]);

  useEffect(() => {
    if ($audio.current) {
      $audio.current.autoplay = autoPlay;
    }
  }, [$audio, autoPlay]);

  useEffect(() => {
    if ($audio.current) {
      fixPitch($audio.current);
    }
  }, [$audio, settings.playbackRate]);

  return (
    <AudioTrackContext.Provider
      value={{
        $audio, //
        progress,
        duration,
        position,
        playbackRate: settings.playbackRate,
        volume,
        isLoading,
        canPlay,
        isPlaying,
        isPaused,
        isError,
        isBlocked,
        isComplete,
        hasMetaData,
        autoPlay,
        shouldPlay,
        buffer,
        bufferProgress,
        setVolume: _setVolume,
        seekStart,
        seek,
        seekEnd,
        play,
        pause,
        setAutoPlay,
        setSrc,
        togglePlay,
        skip,
        readyState,
        status,
        eject,
      }}
    >
      {/* <audio style={{ display: 'none' }} ref={$audio} controls={true} autoPlay={autoPlay} src={src} /> */}
      {children}
    </AudioTrackContext.Provider>
  );
};
