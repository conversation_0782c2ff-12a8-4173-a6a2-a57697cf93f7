import { UseQueryResult } from '@tanstack/react-query';
import { OpenApiSwellcastSwellResponseEnhanced } from '../../../api/gql.getSwellcast';
import { OpenApiReplyResponseEnhanced, OpenApiSwellResponseEnhanced } from '../../../api/gql.loadSwellById';
import { OpenAPIError } from '../../../models/models';

export interface AudioQuery {
  playlistId: string;
  trackId: string;
}

export interface AudioPlaylistItem {
  id: string;
  src: string;
  index: number;
  data: OpenApiSwellcastSwellResponseEnhanced | OpenApiReplyResponseEnhanced;
  duration: number;
  placeholder?: boolean;
}

export interface IAudioController {
  swell: UseQueryResult<OpenApiSwellResponseEnhanced, OpenAPIError> | null;
  trackId: string;
  playlistId: string;
  isLoadingData: boolean;
  tracks: AudioPlaylistItem[];
  currentTrack: AudioPlaylistItem | null;
  skipNext(): void;
  hasNext: boolean;
  skipPrev(): void;
  hasPrev: boolean;
}

export enum AudioEvents {
  ABORT = 'abort',
  CANPLAY = 'canplay',
  CANPLAYTHROUGH = 'canplaythrough',
  DURATIONCHANGE = 'durationchange',
  EMPTIED = 'emptied',
  ENDED = 'ended',
  ERROR = 'error',
  LOADEDDATA = 'loadeddata',
  LOADEDMETADATA = 'loadedmetadata',
  LOADSTART = 'loadstart',
  PAUSE = 'pause',
  PLAY = 'play',
  PLAYING = 'playing',
  PROGRESS = 'progress',
  RATECHANGE = 'ratechange',
  SEEKED = 'seeked',
  SEEKING = 'seeking',
  STALLED = 'stalled',
  SUSPEND = 'suspend',
  TIMEUPDATE = 'timeupdate',
  VOLUMECHANGE = 'volumechange',
  WAITING = 'waiting',
}

export enum AudioReadyState {
  HAVE_NOTHING = 0, // no information whether or not the audio/video is ready
  HAVE_METADATA = 1, // metadata for the audio/video is ready
  HAVE_CURRENT_DATA = 2, // data for the current playback position is available, but not enough data to play next frame/millisecond
  HAVE_FUTURE_DATA = 3, // data for the current and at least the next frame is available
  HAVE_ENOUGH_DATA = 4, // enough data available to start playing
}
