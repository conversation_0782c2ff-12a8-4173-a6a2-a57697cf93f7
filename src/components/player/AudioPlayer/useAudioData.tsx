import { useMemo } from 'react';
import { useCurrentSwell } from '../../../api/gql.loadSwellById';
import { useAudioMode } from '../../../framework/useAudioMode';

export const useAudioData = () => {
  const swell = useCurrentSwell();
  const aux = useAudioMode();
  const track = useMemo(() => {
    if (swell.isSuccess) {
      if (aux.audioQuery.trackId) {
        return [swell.data, ...(swell.data?.replies ?? [])].find((r) => r.id === aux.audioQuery.trackId);
      } else {
        return swell.data;
      }
    }
    return null;
  }, [aux.audioQuery.trackId, swell.isSuccess]);

  return track;
};

// TODO: this could be migrated into AudioMode data
export const useAudioReply = () => {
  const swell = useCurrentSwell();
  const aux = useAudioMode();
  const reply = useMemo(() => {
    const trackId = aux.audioQuery.trackId;
    if (!swell.isSuccess || !trackId || !swell.data?.replies) {
      return null;
    }
    return swell.data.replies.find((r) => r.id === trackId) ?? null;
  }, [swell.isSuccess, aux.audioQuery.trackId, swell.data?.replies]);
  return reply;
};
