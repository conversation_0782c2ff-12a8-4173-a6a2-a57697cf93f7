import { useMemo } from 'react';
import { useOrchestration } from '../../../framework/useOrchestration';
import { PlayerStatus } from '../../../models/models';
import { useAudioController } from './useAudioController';
import { useAudioTrack } from './useAudioTrack';

function getAudioStatus({ isPaused, isPlaying, isLoadingData, isError, shouldPlay, isBlocked, isOpen }: { isPaused: boolean; isPlaying: boolean; isLoadingData: boolean; isError: boolean; shouldPlay: boolean; isBlocked: boolean; isOpen: boolean }) {
  if (isOpen) {
    if (isPlaying) {
      return PlayerStatus.PLAYING;
    } else if (isPaused) {
      return PlayerStatus.PAUSED;
    } else if (isLoadingData) {
      return PlayerStatus.LOADING;
    } else if (isError) {
      return PlayerStatus.ERROR;
    } else if (isBlocked) {
      return PlayerStatus.BLOCKED;
    } else if (shouldPlay) {
      return PlayerStatus.PLAYING;
    } else {
      return PlayerStatus.PAUSED;
    }
  }
  return PlayerStatus.NONE;
}

export const useAudioStatus = () => {
  const track = useAudioTrack();
  const audio = useAudioController();
  const orch = useOrchestration();
  const status = useMemo(
    () =>
      getAudioStatus({
        isPlaying: track?.isPlaying,
        isPaused: track?.isPaused,
        isLoadingData: audio.isLoadingData,
        isError: track?.isError,
        shouldPlay: track?.shouldPlay,
        isBlocked: track?.isBlocked,
        isOpen: orch.isOpen,
      }),
    [track.isPlaying, track.isPaused, track.isError, track.shouldPlay, track.isBlocked, audio.isLoadingData, orch.isOpen],
  );
  return status;
};
