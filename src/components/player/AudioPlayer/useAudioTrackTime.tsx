import { useEffect, useState } from 'react';
import { useAudioTrack } from './useAudioTrack';

export const useAudioTrackTime = (enabled = true, _debug = false) => {
  const track = useAudioTrack();
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const progress = duration > 0 ? position / duration : 0;

  useEffect(() => {
    setPosition(enabled ? track.position : 0);
  }, [enabled, track.position]);

  useEffect(() => {
    setDuration(enabled ? track.duration : 0);
  }, [enabled, track.duration]);

  // smooth position change between player events
  useEffect(() => {
    if (enabled && track.isPlaying) {
      let t: number;
      const update = () => {
        if (track?.$audio?.current) {
          setPosition(track.$audio.current.currentTime);
        }
        t = requestAnimationFrame(update);
      };
      update();

      return () => {
        cancelAnimationFrame(t);
      };
    } else {
      if (track?.$audio?.current) {
        setPosition(track.$audio.current.currentTime);
      }
    }
  }, [enabled, track.$audio, track.isPlaying]);

  // reset position when new track loads
//   useEffect(() => {
//     // if (debug) console.log({ readyState: AudioReadyState[track.readyState] });
//     if (track.readyState == AudioReadyState.HAVE_METADATA || track.readyState == AudioReadyState.HAVE_NOTHING) {
//       if (debug) console.log('reset', { readyState: AudioReadyState[track.readyState] });
//       setPosition(0);
//     }
//   }, [track.readyState]);

  return { position, duration, progress };
};
