import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Scrubber } from 'react-scrubber';
import { useCurrentSwell } from '../../../api/gql.loadSwellById';
import { inBrowser } from '../../../utils/Utils';
import { getElementY, scrollWindowTo } from '../../../utils/scrollWindowTo';
import { getAuthorLink, getSwellLink2, isTextSwell } from '../../../utils/swell-utils';
import { timeago } from '../../../utils/timeago';
import { useRouteParams } from '../../../utils/useRouteParams';
import { ActiveMugshot } from '../../common/Mugshot';
import { SmartLink } from '../../common/SmartLink';
import { CircleLoader } from '../../common/circleloader/CircleLoader';
import { AudioReadyState } from '../AudioPlayer/IAudioPlayer';
import { useAudioController } from '../AudioPlayer/useAudioController';
import { useAudioData } from '../AudioPlayer/useAudioData';
import { useAudioTrack } from '../AudioPlayer/useAudioTrack';
import { useAudioTrackTime } from '../AudioPlayer/useAudioTrackTime';
import { ReplyButton } from '../AudioRecorder/buttons/ReplyButton';
import { ScrubberDisabled } from './AudioTrackScrubber';
import { ActiveDashTrackTimes, ReplayButton, SkipNextButton, SkipPrevButton } from './DashControls';
import { MediaPlaybackRateControl } from './MediaPlaybackRateControl';
import { ActivePlayButton, SwellTextButton } from './PlayButton';

export const AudioUI = ({ className = '' }: { className?: string }) => {
  const audio = useAudioController();
  const { canPlay, skip, readyState, $audio } = useAudioTrack();
  const swell = useCurrentSwell();
  const audioData = useAudioData();
  const nextDisabled = !audio.hasNext;
  const authorLink = useMemo(() => (audioData?.author ? getAuthorLink(audioData.author) : ''), [audioData]);
  const swellLink = useMemo(() => (swell.isSuccess ? getSwellLink2(swell.data) : ''), [swell.data, swell.isSuccess]);
  const createdDate = new Date(Date.parse(audioData?.createdOn ?? '0'));
  const createdDateStr = createdDate.toLocaleDateString('en-us', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
  const isText = swell.isSuccess && audioData ? isTextSwell(audioData) : false;
  // mitigate hydration issue
  const [render, setRender] = useState(false);
  const nav = useNavigate();
  const scrubberDisabled = readyState === AudioReadyState.HAVE_NOTHING || isText;

  const params = useRouteParams();

  const scrollToSwell = (id: string) => {
    const $el = document.querySelector(`[data-swellid="${id}"]`);
    if ($el) {
      scrollWindowTo(getElementY($el));
    }
  };

  // navigate/scroll to text swell when text icon is clicked
  const onClickText = () => {
    if (params.canonicalId !== audio.playlistId) {
      nav(swellLink);
    } else {
      scrollToSwell(audioData?.id ?? '');
    }
  };

  useEffect(() => {
    setRender(inBrowser);
  }, []);

  if (!render) {
    return null;
  }

  if (swell.isSuccess) {
    return (
      <div className={className}>
        <div className='player-container'>
          <div style={{ display: 'flex', overflow: 'hidden' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <SmartLink to={authorLink} style={{ margin: 3 }}>
                <ActiveMugshot size={50} />
              </SmartLink>
            </div>

            <div style={{ display: 'flex', alignItems: 'center', maxWidth: '100%', overflow: 'hidden' }}>
              <div data-component='playertrackinfo' className='trackinfo-copy p-2 w-100 text-white' style={{ minWidth: 0, display: 'flex', alignItems: 'start', flexDirection: 'column', justifyContent: 'center' }}>
                <SmartLink className='fix text-break text-truncate fs-3 fw-bold' style={{ maxWidth: '100%' }} to={swellLink} title={audioData?.masterRef?.title}>
                  {audioData?.masterRef?.title}
                </SmartLink>
                <SmartLink className='fix d-flex text-break text-truncate fs-4 align-items-center' style={{ maxWidth: '100%' }} to={authorLink} title={`@${audioData?.author?.alias}`}>
                  <p className='text-hover-underline text-truncate d-inline-block pointer mb-0'>@{audioData?.author?.alias}</p>
                  <span className='text-nowrap text-truncate' title={createdDateStr}>
                    &nbsp;&middot;&nbsp;{timeago(createdDate)}
                  </span>
                </SmartLink>
              </div>
            </div>
          </div>

          <div className='player-container-controls d-flex align-items-center justify-content-center'>
            <MediaPlaybackRateControl $media={$audio} />
            <ActiveSkipPrevButton />
            {isText ? <SwellTextButton onClick={onClickText} /> : <ActivePlayButton className='play-button-clean' loaderSize={60} />}
            <SkipNextButton disabled={nextDisabled} onClick={audio.skipNext} />
            <ReplayButton disabled={!canPlay} onClick={() => skip(-10)} />
          </div>

          <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
            <ReplyButton context='player' canonicalId={audio.playlistId} replyId={audio.trackId} />
          </div>
        </div>
        <div>
          {/* <TestScrubber disabled={scrubberDisabled} /> */}

          <div className='scrubber-container'>{scrubberDisabled ? <ScrubberDisabled /> : <ActiveScrubber />}</div>
          <div style={{ opacity: scrubberDisabled ? 0 : 1, transition: 'opacity 0.3s' }}>
            <ActiveDashTrackTimes />
          </div>
        </div>
      </div>
    );
  }

  //   if (swell.isLoading) {
  return (
    <div className='flex-center p-5'>
      <CircleLoader />
    </div>
  );
  //   }

  //   return null;
};

const ActiveSkipPrevButton = () => {
  const time = useAudioTrackTime();
  const track = useAudioTrack();
  const audio = useAudioController();
  const swell = useCurrentSwell();
  const audioData = useAudioData();
  const nav = useNavigate();
  const isText = swell.isSuccess && audioData ? isTextSwell(swell.data) : false;
  const swellLink = useMemo(() => (swell.isSuccess ? getSwellLink2(swell.data) : ''), [swell.data, swell.isSuccess]);
  const prevDisabled = !audio.hasPrev && time.position < 10;

  const onClickPrev = () => {
    if (time.position > 10) {
      track.seekEnd(0);
      return;
    }
    if (audio.currentTrack?.index == 1 && isText) {
      nav(swellLink);
    }
    audio.skipPrev();
  };

  return <SkipPrevButton disabled={prevDisabled} onClick={onClickPrev} />;
};

const ActiveScrubber = () => {
  const time = useAudioTrackTime();
  const track = useAudioTrack();
  const [localProgress, setLocalProgress] = useState(0);
  const [isActive, setIsActive] = useState(false);

  // Use the local progress value during scrubbing to prevent update loops
  const displayValue = isActive ? localProgress : time.progress;

  const handleScrubStart = useCallback(
    (progress: number) => {
      setIsActive(true);
      setLocalProgress(progress);
      track.seekStart(progress);
    },
    [track],
  );

  const handleScrubChange = useCallback(
    (progress: number) => {
      // Only update local state during active scrubbing
      if (isActive) {
        setLocalProgress(progress);
        // Use requestAnimationFrame to throttle updates to the audio element
        requestAnimationFrame(() => {
          track.seek(progress);
        });
      }
    },
    [isActive, track],
  );

  const handleScrubEnd = useCallback(
    (progress: number) => {
      // Final update
      setLocalProgress(progress);
      track.seekEnd(progress);
      // Delay resetting active state to prevent flickering
      setTimeout(() => setIsActive(false), 50);
    },
    [track],
  );

  return <Scrubber min={0} max={1} value={displayValue} onScrubStart={handleScrubStart} onScrubChange={handleScrubChange} onScrubEnd={handleScrubEnd} />;
};
