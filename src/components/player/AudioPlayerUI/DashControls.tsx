import { SwellIcons } from '../../../assets/icons/SwellIcons';
import { AudioTimesUI } from '../../../view/sound/audio/ui/AudioTimes';
import { useAudioTrackTime } from '../AudioPlayer/useAudioTrackTime';

export const SkipPrevButton = ({ disabled, onClick }: { disabled: boolean; onClick(): void }) => (
  <button title='skip to previous reply' className='btn icon-white' disabled={disabled} onClick={onClick}>
    <SwellIcons.SkipPrevious />
  </button>
);

export const SkipNextButton = ({ disabled, onClick }: { disabled: boolean; onClick(): void }) => (
  <button title='skip to next reply' aria-label='skip to next reply' className='btn icon-white' disabled={disabled} onClick={onClick}>
    <SwellIcons.SkipNext />
  </button>
);

export const ReplayButton = ({ disabled, onClick, className = undefined }: { className?: string; disabled: boolean; onClick(): void }) => (
  <button title='skip 10s back' aria-label='skip 10s back' className={`btn icon-white ${className}`} disabled={disabled} onClick={onClick}>
    <SwellIcons.Replay10 style={{ width: 22, height: 22 }} />
  </button>
);

export const ActiveDashTrackTimes = () => {
  const time = useAudioTrackTime();
  return (
    <div className='no-select' style={{ marginTop: -15 }}>
      <AudioTimesUI className='text-white' duration={time.duration} position={time.position} />
    </div>
  );
};
