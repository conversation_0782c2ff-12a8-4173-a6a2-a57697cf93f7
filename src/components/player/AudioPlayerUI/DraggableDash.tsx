import { useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useRef } from 'react';
import { useResizeObserver } from 'usehooks-ts';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { zIndex } from '../../../framework/settings/zIndex';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { AudioMode } from '../../../models/models';
import { PointerAction, useDragger } from '../../../utils/useDragger';
import { useDashHeight } from './useDashHeight';

export const DashSpacer = (props: React.HTMLAttributes<HTMLDivElement> & { style?: React.CSSProperties }) => {
  const height = useDashHeight();
  return <div data-component='DashSpacer' {...props} style={{ ...(props?.style ?? {}), height }} />;
};

export const DraggableDash = ({ children, sticky = false }: { children: React.ReactNode; isOpen?: boolean; sticky?: boolean }) => {
  const queryClient = useQueryClient();
  const dragger = useDragger();
  const { isOpen, close } = useOrchestration();
  const { audioMode } = useAudioMode();

  useEffect(() => {
    if (dragger.action === PointerAction.SWIPE_DOWN || dragger.action === PointerAction.CLICK) {
      close();
    }
  }, [close, dragger.action, dragger.state]);

  const $div = useRef<HTMLDivElement>(null);
  const size = useResizeObserver({ ref: $div });

  useEffect(() => {
    queryClient.setQueryData(['dash-height'], isOpen ? size.height : 0);
  }, [isOpen, queryClient, size]);

  return (
    <div
      ref={$div}
      data-component='draggabledash'
      style={{
        position: sticky ? 'sticky' : 'fixed',
        bottom: -1,
        left: 0,
        right: 0,
        zIndex: zIndex.bottomNav,
        overflow: 'hidden',
        height: 'min-content',
        transition: dragger.isActive ? '' : isOpen ? 'all 0.3s ease-out' : 'all 0.3s ease-in',
        transform: dragger.isActive ? `translateY(${Math.max(0, dragger.offsetY)}px)` : isOpen ? 'translateY(0)' : 'translateY(100%)',
        opacity: isOpen ? 1 : 0,
        maxHeight: 'fit-content',
      }}
    >
      <div className={audioMode == AudioMode.PLAY ? 'backdrop-blur-player' : 'backdrop-blur-recorder'}>
        <div className='mx-auto' style={{ maxWidth: BreakPoints.L }}>
          <DragHandle ref={dragger.$handle} />
          {children}
        </div>
      </div>
    </div>
  );
};

const DragHandle = ({ onClick, ref }: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>) => (
  <div
    ref={ref} //
    className='d-flex align-items-center justify-content-center pointer'
    onClick={onClick}
    aria-label='close player'
    role='button'
    tabIndex={0}
    style={{ height: 30, transform: 'translateY(-5px)', zIndex: 999 }}
  >
    <div style={{ width: 80, height: 4, borderRadius: 2, backgroundColor: 'rgba(255,255,255,0.5)' }}></div>
  </div>
);
