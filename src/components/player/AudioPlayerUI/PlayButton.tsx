import { Article, ErrorOutline } from '@mui/icons-material';
import classNames from 'classnames';
import React, { CSSProperties, useEffect, useMemo } from 'react';
import { useSwell } from '../../../api/gql.loadSwellById';
import { SwellIcons } from '../../../assets/icons/SwellIcons';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { PlayerStatus } from '../../../models/models';
import { getElementY, scrollWindowTo } from '../../../utils/scrollWindowTo';
import { CircleLoader } from '../../common/circleloader/CircleLoader';
import { Pie } from '../../common/Mugshot';
import { useAudioStatus } from '../AudioPlayer/useAudioStatus';
import { useAudioTrack } from '../AudioPlayer/useAudioTrack';
import { useAudioTrackTime } from '../AudioPlayer/useAudioTrackTime';
import { useTrack } from '../WaveUI/useTrack';

export const ActivePlayButton = ({ className = '', disabled, style = {} }: { className?: string; loaderSize?: number; disabled?: boolean; style?: CSSProperties }) => {
  const status = useAudioStatus();
  const track = useAudioTrack();

  return (
    <PlayButton
      className={className}
      status={status}
      onClick={(e) => {
        e.stopPropagation();
        track.togglePlay();
      }}
      disabled={disabled}
      style={style}
    />
  );
};

export const PlayButton = ({ status, onClick, className = '', disabled = false, style = {} }: { status: PlayerStatus; onClick?: React.MouseEventHandler<HTMLButtonElement>; className?: string; disabled?: boolean; style?: CSSProperties }) => {
  return (
    <button
      data-component='play-button' //
      data-status={PlayerStatus[status]}
      disabled={disabled}
      className={classNames(className, 'pointer')}
      onClick={disabled ? undefined : onClick}
      aria-label='play pause'
      style={style}
    >
      <div className='icon icon-load'>
        <CircleLoader />
      </div>
      <SwellIcons.Play className='icon icon-play' style={{ width: '50%', marginLeft: 1 }} />
      <SwellIcons.Pause className='icon icon-pause' style={{ width: '50%' }} />
      <ErrorOutline className='icon icon-error' />
    </button>
  );
};

export const SwellTextButton = (props: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const { audioQuery } = useAudioMode();

  // scroll to text when clicked
  const onClick = () => {
    const $el = document.querySelector(`[data-swellid="${audioQuery.trackId}"]`);
    if ($el) {
      scrollWindowTo(getElementY($el));
    }
  };

  return (
    <button
      data-component='play-button' //
      style={{ width: 60, height: 60 }}
      onClick={onClick}
      {...props}
    >
      <div className='bg-white-50 text-black rounded-circle d-flex flex-center' style={{ width: 40, height: 40 }}>
        <Article />
      </div>
    </button>
  );
};

export const LivePlayButton = ({ canonicalId }: { canonicalId: string }) => {
  const audioMode = useAudioMode();
  const track = useAudioTrack();
  const playToggle = useTrack({ playlistId: canonicalId, trackId: '' });
  const swell = useSwell({ canonicalId: playToggle.active ? canonicalId : '' });
  const { togglePlay, close } = useOrchestration();
  const time = useAudioTrackTime(playToggle.active);

  const playerStatus = useMemo(() => {
    if (playToggle.active) {
      return track.isLoading || audioMode.audioQuery.trackId == '' ? PlayerStatus.LOADING : track.isPlaying ? PlayerStatus.PLAYING : PlayerStatus.PAUSED;
    }
    return PlayerStatus.PAUSED;
  }, [playToggle.active, track.isLoading, track.isPlaying, audioMode.audioQuery.trackId]);

  useEffect(() => {
    if (playToggle.active) {
      if (audioMode.audioQuery.trackId && audioMode.audioQuery.trackId !== swell.data?.id) {
        close();
      }
    }
  }, [playToggle.active, audioMode.audioQuery.trackId, swell.data?.id, close]);

  const onClick = () => {
    if (playToggle.active) {
      playToggle.onClick();
    } else {
      togglePlay({ playlistId: canonicalId, trackId: '' }, true);
    }
  };

  return (
    <div style={{ position: 'relative', overflow: 'visible', zIndex: 999 }}>
      <Pie
        progress={playToggle.active && playerStatus != PlayerStatus.LOADING && !playToggle.isBlocked ? time.progress : 0} //
        color={'rgba(128,128,128,0.5)'}
        size={40}
        style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', pointerEvents: 'none' }}
      />
      <PlayButton
        className='play-button-squash' //
        status={playerStatus}
        onClick={onClick}
      />
    </div>
  );
};
