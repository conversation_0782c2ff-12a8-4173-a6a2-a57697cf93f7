import React, { Suspense, startTransition, useEffect, useState } from 'react';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { AudioMode, UploadStatus } from '../../../models/models';
import { CircleLoader } from '../../common/circleloader/CircleLoader';
import AudioRecorderUI from '../AudioRecorder/AudioRecorderUI';
import { UploadingUI } from '../AudioRecorder/UploadingUI';
import { AudioUI } from './AudioUI';
import { DraggableDash } from './DraggableDash';

const PlayerLoader = () => (
  <div className='flex-center' style={{ height: 130 }}>
    <CircleLoader />
  </div>
);

export const SwellAudioPlayer = () => {
  const orch = useOrchestration();
  const { audioMode } = useAudioMode();
  const UIclass = 'pt-0 px-3 pb-3 fade-in-fast';
  let dashContents: React.ReactNode = null;

  const [showPage, setShowPage] = useState(false);

  // Ensure updates are deferred until after hydration
  useEffect(() => {
    startTransition(() => {
      setShowPage(true);
    });
  }, []);

  if (!showPage) {
    return null; // Prevents rendering during hydration
  }

  if (orch.uploadStatus == UploadStatus.NONE) {
    switch (audioMode) {
      case AudioMode.PLAY:
        if (orch.hasOpened) {
          // This fixes an issue where the player disappears while closing animation is in progress.
          dashContents = (
            <Suspense fallback={<PlayerLoader />}>
              <AudioUI className={UIclass} />
            </Suspense>
          );
        }
        break;

      case AudioMode.RECORD_NEW:
      case AudioMode.RECORD_PROFILE:
      case AudioMode.RECORD_PROMPT_RESPONSE:
      case AudioMode.RECORD_REPLY:
        dashContents = (
          <Suspense fallback={<PlayerLoader />}>
            <AudioRecorderUI className={UIclass} />
          </Suspense>
        );
        break;
      case AudioMode.NONE:
      //
    }
  } else {
    dashContents = <UploadingUI className={UIclass} />;
  }

  return <DraggableDash>{dashContents}</DraggableDash>;
};
