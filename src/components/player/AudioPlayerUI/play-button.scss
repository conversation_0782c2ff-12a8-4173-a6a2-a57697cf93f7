@use '../../../assets/css/base' as *;
/*

<PlayButton />

*/
[data-component='play-button'] {
  @extend .btn, .p-0;
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;

  .icon {
    display: none;
    font-size: 2rem;
    color: #000000;
  }

  .icon-load {
    // display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: visibility 0.2s 0s, opacity 1s 0.3s;
  }

  &[data-status='PLAYING'] {
    .icon-pause {
      display: block;
    }
  }

  &[data-status='PAUSED'],
  &[data-status='COMPLETED'] {
    .icon-play {
      display: block;
    }
  }

  &[data-status='LOADING'] {
    .icon-load,
    .icon-pause {
      display: block;
    }
  }

  &[data-status='BLOCKED'] {
    .icon-play {
      display: block;
    }
  }

  &[data-status='ERROR'] {
    .icon-error {
      display: block;
    }
  }

  &[data-status='NONE'] {
    .icon-play {
      display: block;
    }
  }
}

[data-component='play-button'].play-button-squash {
  @extend .rounded-pill;
  .icon-load .spinner-border {
    --bs-spinner-width: 36px !important;
    --bs-spinner-height: 36px !important;
    --bs-spinner-vertical-align: -7px;
    color: rgba(0, 0, 0, 0.2);
  }
  &[data-status='PLAYING'],
  &[data-status='PAUSED'],
  &[data-status='COMPLETED'],
  &[data-status='LOADING'] {
    @extend .bg-squash;
    --icon-color: #000;
  }
  &[data-status='BLOCKED'],
  &[data-status='NONE'] {
    @extend .btn-gradient, .backdrop-blur;
    --icon-color: #fff;
  }
}

[data-component='play-button'].play-button-clean {
  width: 60px;
  height: 60px;
  .icon {
    font-size: 3.7rem;
    color: #ffffff;
    --icon-color: #fff;
  }
  .icon-load .spinner-border {
    --bs-spinner-width: 50px !important;
    --bs-spinner-height: 50px !important;
    --bs-spinner-vertical-align: -5px;
    color: #fff;
    // --loader-size: 50px;
    // --loader-size-half: 25px;
    // --loader-color: #ffffff;
  }
}
