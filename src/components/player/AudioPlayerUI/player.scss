.player-container {
  display: grid;
  flex-wrap: wrap;
  grid-template-columns: 1fr auto 1fr;
}
.player-container-controls {
  --icon-color: #fff;
}
// .player-container > div:nth-child(1) {
//   overflow: hidden;
// }
.player-container > div:nth-child(2) {
  display: flex;
  justify-content: center;
}
.player-container > div:nth-child(3) {
  display: flex;
  justify-content: end;
  align-items: center;
}
@media (max-width: 640px) {
  .player-container {
    grid-template-columns: 1fr min-content auto;
  }
  .player-container > div:nth-child(1) {
    order: 1;
  }
  .player-container > div:nth-child(2) {
    order: 3;
    grid-column: 1/3;
  }
  .player-container > div:nth-child(3) {
    order: 2;
  }
}

tr.selected,
tr.selected > td {
  background-color: #ccc;
}
