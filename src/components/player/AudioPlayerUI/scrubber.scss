/*! purgecss start ignore */

@use '~react-scrubber/lib/scrubber';

.scrubber-container {
  height: 50px;
}
.scrubber {
  width: 100%;
  height: 100%;
  position: relative;
  user-select: none;
  touch-action: none;
}
.scrubber * {
  user-select: none;
}
.scrubber .bar {
  background: rgba(255, 255, 255, 0.25);
  position: relative;
  transition: height 0.2s linear, width 0.2s linear;
}
.scrubber.horizontal .bar {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  height: 4px;
  width: 100%;
  border-radius: 1000px;
}
.scrubber.vertical .bar {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 100%;
}
.scrubber .bar__progress {
  position: absolute;
  background: #fcb950;
  border-radius: 1000px;
}
.scrubber .bar__buffer {
  position: absolute;
  background: rgba(170, 170, 170, 0.6);
  border-radius: 1000px;
}
.scrubber.horizontal .bar__progress,
.scrubber.horizontal .bar__marker,
.scrubber.horizontal .bar__buffer {
  height: 100%;
}
.scrubber.vertical .bar__progress,
.scrubber.vertical .bar__marker,
.scrubber.vertical .bar__buffer {
  width: 100%;
  bottom: 0;
}
.scrubber .bar__thumb {
  position: absolute;
  width: 0px;
  height: 0px;
  border-radius: 1000px;
  background: #fff;
  transition: height 0.2s linear, width 0.2s linear;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.3);
}
.scrubber.horizontal .bar__thumb {
  transform: translate(-50%, -50%);
  top: 50%;
}
.scrubber.vertical .bar__thumb {
  transform: translate(-50%, 50%);
  left: 50%;
}
.scrubber.hover.horizontal .bar {
  height: 6px;
}
.scrubber.hover.vertical .bar {
  width: 6px;
}
.scrubber.hover .bar__thumb {
  width: 16px;
  height: 16px;
}
.scrubber .bar__marker {
  position: absolute;
  background: rgb(240, 205, 5);
}
.scrubber.horizontal .bar__marker {
  width: 12px;
}
.scrubber.vertical .bar__marker {
  height: 12px;
}
