import React, { memo, useEffect, useMemo, useRef, useState } from 'react';

interface AmpMeterProps {
  wave: number[]; // 0-1 normalized analyser values
  blockWidth?: number; // px
  blockHeight?: number; // px
  blockGap?: number; // px
}

function debounce<F extends (...args: unknown[]) => void>(fn: F, ms = 120) {
  let timeout: NodeJS.Timeout | null = null;
  return (...args: Parameters<F>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      fn(...args);
      timeout = null;
    }, ms);
  };
}

const arePropsEqual = (prev: AmpMeterProps, next: AmpMeterProps) => {
  return prev.blockWidth === next.blockWidth && prev.blockHeight === next.blockHeight && prev.blockGap === next.blockGap && Math.max(...prev.wave, 0) === Math.max(...next.wave, 0);
};

export const AmpMeter = memo<AmpMeterProps>(({ wave, blockWidth = 4, blockHeight = 7, blockGap = 3 }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [blocks, setBlocks] = useState(21);

  // Resize-driven block count
  const handleResize = useMemo(
    () =>
      debounce(() => {
        const width = ref.current?.offsetWidth ?? 0;
        let n = Math.floor(width / (blockWidth + blockGap));
        n = Math.min(128, Math.max(3, n));
        setBlocks(n % 2 ? n : n - 1);
      }, 120),
    [blockWidth, blockGap],
  );

  useEffect(() => {
    handleResize(); // Initial
    const ro = new ResizeObserver(handleResize);
    if (ref.current) ro.observe(ref.current);
    return () => {
      ro.disconnect();
      // No need to clear debounce timeout (handled internally)
    };
  }, [handleResize]);

  // Active mask
  const activeMask = useMemo(() => {
    const level = wave.length ? Math.max(...wave) : 0;
    const r = Math.round((level * (blocks - 1)) / 2);
    const mid = Math.floor(blocks / 2);
    return Array.from({ length: blocks }, (_, i) => Math.abs(i - mid) <= r);
  }, [blocks, wave]);

  // Colour lookup
  const colours = useMemo(() => {
    const layout = [
      { className: 'amp-high', p: 0.15 },
      { className: 'amp-medium', p: 0.4 },
      { className: 'amp-low', p: 0.6 },
      { className: 'amp-medium', p: 0.85 },
      { className: 'amp-high', p: 1 },
    ];

    let layoutIndex = 0;

    const a = Array.from({ length: blocks }, (_, i) => {
      const d = Math.abs(i / blocks);
      if (layout[layoutIndex].p < d) {
        layoutIndex++;
      }
      return layout[layoutIndex].className;
    });

    return a;
  }, [blocks]);

  // Precompute block styles
  const blockStyle = useMemo(
    () => ({
      width: blockWidth,
      height: blockHeight,
    }),
    [blockWidth, blockHeight],
  );

  return (
    <div ref={ref} className='d-flex justify-content-center w-100 rounded-pill bg-grey-10' style={{ gap: blockGap, height: blockHeight, '--block-width': `${blockWidth}px`, '--block-height': `${blockHeight}px` } as React.CSSProperties} aria-hidden='true'>
      <span className='visually-hidden'>Audio amplitude level: {Math.max(...wave, 0).toFixed(2)}</span>
      {Array.from({ length: blocks }, (_, i) => (
        <div key={i} className={`amp-block ${colours[i]} ${activeMask[i] ? 'active' : 'inactive'}`} style={blockStyle} />
      ))}
    </div>
  );
}, arePropsEqual);
