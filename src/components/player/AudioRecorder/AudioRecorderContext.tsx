import React from 'react';
import { PlayerStatus } from '../../../models/models';
import { PermissionStateExt, RecordErrorType } from './AudioRecorderProvider';

// TODO: UI notify user when quiet
// TODO: trim click from start/end
// NOTE: messagechannels can only be used once, then they're "neutered"

export interface IRecordingOutput {
  file: File;
  duration: number;
  wave: number[];
  payload?: Record<string, string> | null;
}

export interface IAudioRecorder {
  $media: React.RefObject<HTMLMediaElement>;
  unblock(): void;
  startRecording(): void;
  stopRecording(): void;
  pauseRecording(): Promise<void>;
  resumeRecording(): void;
  resetRecording(force?: boolean): Promise<boolean>;
  deleteRecording(): Promise<void>;
  toggleRecording(): void;
  completeRecording(): Promise<IRecordingOutput>;
  togglePlay(): void;
  disconnect(): Promise<void>;
  wave: number[];
  duration: number;
  maxDuration: number;
  setMaxDuration: React.Dispatch<React.SetStateAction<number>>;
  isLoading: boolean;
  isProcessing: boolean;
  isBlocked: boolean;
  isRecording: boolean;
  hasData: boolean;
  isMaxDuration: boolean;
  displayMode: 'play' | 'record';
  micStatus: string; // TODO: tie into isBlocked logic so <RecorderInfo> doesn't need to reference both to show blocked message
  status: PlayerStatus;
  micPermissionState: PermissionStateExt | null;
  mediaRecorderState: RecordingState | null;
  runTest: (k: string) => unknown;
  errorType: RecordErrorType;
  trackReadyState?: MediaStreamTrackState | 'none';
  trackMuted?: boolean;
  trackLive?: boolean;
  soundCheck?(): void;
  isError: boolean;
  stash: (id: string) => Promise<void>;
  restore: (id: string) => Promise<void>;
  flush: (id: string) => Promise<void>;
  hasStash: (id: string) => Promise<boolean>;
  chunksLength: number;
  setAutoSave: React.Dispatch<React.SetStateAction<boolean>>;
  isStashing: boolean;
}

export const AudioRecorderContext = React.createContext<IAudioRecorder>(undefined!);
