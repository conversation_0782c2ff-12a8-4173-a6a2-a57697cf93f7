/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDebug } from '../../../components/debug/useDebug';
import { isRecordMode } from '../../../framework/isRecordMode';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { useStashId } from '../../../framework/useStashId';
import { AudioMode, MediaRecorderEvent, PlayerStatus } from '../../../models/models';
import { Duration } from '../../../utils/Duration';
import { sleep } from '../../../utils/sleep';
import { useAudioContext } from '../../../view/sound/audio/hooks/useAudioContext';
import { useAudioElement } from '../../../view/sound/audio/hooks/useAudioElement';
import { usePopup } from '../../popup/usePopup';
import { AudioEvents } from '../AudioPlayer/IAudioPlayer';
import { AudioRecorderContext, IRecordingOutput } from './AudioRecorderContext';
import { GenericRecordErrorMessage } from './popups/GenericRecordErrorMessage';
import { MicAccessMessage } from './popups/MicAccessMessage';
import { MicMutedAccessMessage } from './popups/MicMutedAccessMessage';
import { ProblemSavingMessage } from './popups/ProblemSavingMessage';
import { RecordingPausedMessage } from './popups/RecordingPausedMessage';
import { TimeLimitMessage } from './popups/TimeLimitMessage';
import { useRecordStash } from './useRecordStash';
import { allowMimeTypes, mimeToExtension, mimeToMime } from './utils/mimeToExtension';
import { patchBlobWithDuration } from './utils/patchBlobWithDuration';
import { waveConfig } from './waveConfig';

export type PermissionStateExt = PermissionState | 'unknown';

// eslint-disable-next-line react-refresh/only-export-components
export enum RecordErrorType {
  AUDIO_CONTEXT_FAILED = 'AUDIO_CONTEXT_FAILED',
  TRACKS_MUTED = 'TRACKS_MUTED',
  TRACKS_INACTIVE = 'TRACKS_INACTIVE',
  TRACKS_DISABLED = 'TRACKS_DISABLED',
  MICROPHONE_DENIED = 'MICROPHONE_DENIED',
  STREAM_FAILED = 'STREAM_FAILED',
  STREAM_INACTIVE = 'STREAM_INACTIVE',
  STREAM_NOT_ALLOWED = 'STREAM_NOT_ALLOWED',
  STREAM_MISMATCH = 'STREAM_MISMATCH',
  SOURCE_FAILED = 'SOURCE_FAILED',
  UNKNOWN = 'UNKNOWN',
  PAUSED_ON_BLUR = 'PAUSED_ON_BLUR',
  RECORDER_FAILED = 'RECORDER_FAILED',
  RECORDER_INACTIVE = 'RECORDER_INACTIVE',
  NONE = 'NONE',
  ANALYSER_FAILED = 'ANALYSER_FAILED',
}

const RecordErrorMessage = {
  [RecordErrorType.AUDIO_CONTEXT_FAILED]: 'pretty message',
  [RecordErrorType.TRACKS_DISABLED]: 'pretty message',
  [RecordErrorType.SOURCE_FAILED]: 'pretty message',
  [RecordErrorType.MICROPHONE_DENIED]: 'pretty message',
  [RecordErrorType.NONE]: 'pretty message',
  [RecordErrorType.PAUSED_ON_BLUR]: 'pretty message',
  [RecordErrorType.STREAM_FAILED]: 'pretty message',
  [RecordErrorType.STREAM_NOT_ALLOWED]: 'pretty message',
  [RecordErrorType.STREAM_INACTIVE]: 'pretty message',
  [RecordErrorType.TRACKS_INACTIVE]: 'pretty message',
  [RecordErrorType.TRACKS_MUTED]: 'pretty message',
  [RecordErrorType.UNKNOWN]: 'pretty message',
  [RecordErrorType.RECORDER_FAILED]: 'pretty message',
  [RecordErrorType.ANALYSER_FAILED]: 'pretty message',
  [RecordErrorType.RECORDER_INACTIVE]: 'pretty message',
  [RecordErrorType.STREAM_MISMATCH]: 'pretty message',
};

class RecordError extends Error {
  public type: RecordErrorType;
  constructor(type: RecordErrorType, message = 'unknown') {
    super(RecordErrorMessage[type]);
    this.name = type;
    this.type = type;
    this.message = message;
  }
}

export const AudioRecorderProvider = ({ children }: { children: React.ReactNode }) => {
  // hooks
  const { debug, prepareArgs } = useDebug('record');
  const { debug: debugStash } = useDebug('stash');
  const { audioMode, audioQuery } = useAudioMode();
  const { isOpen } = useOrchestration();
  const $audio = useAudioElement();
  const popup = usePopup();
  const audioContext = useAudioContext();
  const stashId = useStashId();
  const { stash: _stash, restore: _restore, flush, hasStash, cleanup } = useRecordStash();

  // core audio properties
  const analyserNode = useRef<AnalyserNode | null>(null);
  const dataArray = useRef<Uint8Array>(null!);
  const mediaStream = useRef<MediaStream | null>(null);
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const micPermission = useRef<PermissionStatus | null>(null);
  const sourceNode = useRef<MediaStreamAudioSourceNode | null>(null);
  const [mediaRecorderState, setMediaRecorderState] = useState<RecordingState>('inactive');
  const [micPermissionState, setMicPermissionState] = useState<PermissionStateExt>('unknown');
  const startRecordTime = useRef(0);
  const elapsedRecordTime = useRef<Duration>(new Duration({ milliseconds: 0 }));
  const totalRecordTime = useRef<Duration>(new Duration({ milliseconds: 0 }));
  const chunksArray = useRef<Blob[]>([]);
  const blobData = useRef<Blob>(undefined!);
  const mimeType = useRef('');
  const isWaiting = useRef(false);
  const trackInfo = useRef({ muted: false, live: true });
  const savedPosition = useRef(0);
  const hasAudioData = useRef(false);
  const requestDataAttempts = useRef(0);
  const requestDataEmpties = useRef(0);
  const killMode = useRef(false);

  // state flags
  const [wave, setWave] = useState<number[]>(waveConfig.DEFAULT_WAVE);
  const [isVisible, setIsVisible] = useState(true);
  const [duration, setDuration] = useState(0);
  const [maxDuration, setMaxDuration] = useState(5 * 60);
  const [status, setStatus] = useState(PlayerStatus.NONE);
  const [errorType, setErrorType] = useState<RecordErrorType>(RecordErrorType.NONE);
  const [trackMuted, setTrackMuted] = useState(false);
  const [trackLive, setTrackLive] = useState(true);
  const [hasTrackEvents, setHasTrackEvents] = useState(false);
  const [trackReadyState, setTrackReadyState] = useState<MediaStreamTrackState | 'none'>('none');
  const [hasPermissionEvents, setHasPermissionEvents] = useState(false);
  const [streamActive, setStreamActive] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [autoSave, setAutoSave] = useState(true);
  const [chunksLength, setChunksLength] = useState(0);

  // derived flags
  const isMaxDuration = useMemo(() => duration >= maxDuration - 1 && duration < 1613450346, [duration, maxDuration]);
  const hasData = duration > 0 || mediaRecorderState == 'paused';
  const displayMode: 'record' | 'play' = useMemo(() => (status == PlayerStatus.RECORDING || status == PlayerStatus.NONE ? 'record' : 'play'), [status]);
  const isLoading = isProcessing || status == PlayerStatus.LOADING;
  const isRecording = status == PlayerStatus.RECORDING;
  const isBlocked = errorType == RecordErrorType.MICROPHONE_DENIED || status == PlayerStatus.BLOCKED;
  const isError = errorType !== RecordErrorType.NONE;
  const [isStashing, setIsStashing] = useState(false);

  const stash = async (id: string) => {
    if (chunksArray.current.length && isStashing === false) {
      try {
        setIsStashing(true);
        await _stash(id, chunksArray.current, audioQuery);
        setIsStashing(false);
      } catch (err) {
        if (debugStash) console.error(...prepareArgs('stash() ERROR', err));
      }
      if (debugStash) console.log(...prepareArgs('stash() COMPLETE', { id, duration, chunks: chunksArray.current.length }));
    }
  };

  // auto save
  useEffect(() => {
    if (autoSave && isRecording) {
      const t = setInterval(() => {
        if (debugStash) console.log(...prepareArgs('autoSave()'));
        requestData();
      }, 10000);

      return () => {
        clearInterval(t);
      };
    }
  }, [autoSave, isRecording]);

  // stash when data arrives
  useEffect(() => {
    if (chunksLength && autoSave) {
      console.log({ chunksLength });
      stash(stashId);
    }
  }, [chunksLength]);

  const restore = async (id: string) => {
    if (await hasStash(id)) {
      setIsProcessing(true);
      const stash = await _restore(id);
      chunksArray.current = stash?.chunks ?? [];
      await buildAudio();
      setIsProcessing(false);
      setStatus(PlayerStatus.PAUSED);
      if (debugStash) console.log(...prepareArgs('restore() SUCCESS', { stash }));
    } else {
      if (debugStash) console.log(...prepareArgs('restore() EMPTY', { stash }));
    }
  };

  const unblock = async () => {
    if (debug) console.log(...prepareArgs('unblock()'));
    soundCheck();
  };

  const startRecording = async () => {
    if (debug) console.log(...prepareArgs('startRecording()'));

    if (!isTrackValid()) {
      killStream();
      killRecorder();
    }

    const success = await soundCheck();

    if (!success) {
      if (debug) console.log(...prepareArgs('startRecording()', 'failed initMic', { soundCheck: success }));
      return;
    }

    if (mediaRecorder.current?.state == 'recording') {
      if (debug) console.log(...prepareArgs('startRecording()', 'already recording', { state: mediaRecorder.current?.state }));
      return;
    }

    if (!mediaStream.current?.active) {
      if (debug) console.log(...prepareArgs('startRecording()', 'stream inactive', { stream: mediaStream.current?.active }));
      return;
    }

    try {
      if (mediaRecorder.current?.state == 'inactive') {
        mediaRecorder.current?.start();
      }
      //   if (debug) console.log(...prepareArgs('startRecording()'));
    } catch (err) {
      if (debug)
        console.error(
          ...prepareArgs('startRecording()', 'FAILED', err, {
            rec: mediaRecorder.current?.state,
            stream: mediaStream.current?.active, //
            hasLiveTracks: isTrackLive(),
            hasMutedTracks: isTrackMuted(),
            hasEndedTracks: isTrackEnded(),
            hasEnabledTracks: isTrackEnabled(),
          }),
        );
    }
  };

  const stopRecording = () => {
    // TODO: need try around this?
    if (mediaRecorder.current && mediaRecorder.current?.state !== 'inactive') {
      mediaRecorder.current.stop();
    }
  };

  const pauseRecording = async () => {
    if (mediaRecorder.current?.state == 'recording') {
      mediaRecorder.current?.pause();
      await confirmDataReceived();
    }
  };

  const confirmDataReceived = () => {
    return new Promise((resolve) => {
      const onData = () => resolve(true);
      mediaRecorder.current?.addEventListener(MediaRecorderEvent.DATA_AVAILABLE, onData, { once: true });
      setTimeout(() => {
        mediaRecorder.current?.removeEventListener(MediaRecorderEvent.DATA_AVAILABLE, onData);
        resolve(false);
      }, 1000);
    });
  };

  const resumeRecording = async () => {
    if (debug) console.log(...prepareArgs('resumeRecording()'));
    const success = await soundCheck();
    if (success) {
      if (isTrackMuted()) {
        setTrackMuted(true);
      } else {
        if (mediaRecorder.current?.state == 'paused') {
          mediaRecorder.current?.resume();
        }
      }
    }
  };

  const deleteRecording = async () => {
    if (stashId) await flush(stashId);
    await resetRecording();
  };

  const resetRecording = async (force = true) => {
    if (!force) {
      alert('This flow needs to be updated!!!');
      return false;
    }
    if (debug) console.log(...prepareArgs('resetRecording()', { force, stashId }));
    // if (stashId) await flush(stashId);
    mediaRecorder.current?.stop();
    chunksArray.current = [];
    setChunksLength(0);
    totalRecordTime.current = new Duration({ seconds: 0 });
    elapsedRecordTime.current = new Duration({ seconds: 0 });
    if ($audio.current) {
      $audio.current.pause();
      URL.revokeObjectURL($audio.current.src);
      $audio.current.src = '';
      $audio.current.currentTime = 0;
      $audio.current.load();
    }
    savedPosition.current = 0;
    setDuration(0);
    setMediaRecorderState('inactive');

    // if (!force) {
    //   dataLayer({
    //     ...trackingData,
    //     event: 'swellrecorder',
    //     context: `delete_${trackingData.context}`,
    //   });
    // }

    return true;
  };

  const toggleRecording = () => {
    if (debug) console.log(...prepareArgs('toggleRecording()', { mediaRecorder: mediaRecorder.current?.state }));
    killMode.current = false;
    switch (mediaRecorder.current?.state) {
      case 'paused':
        resumeRecording();
        break;
      case 'recording':
        pauseRecording();
        break;
      case 'inactive':
      default:
        startRecording();
        break;
    }
  };

  const togglePlay = () => {
    if (debug) console.log(...prepareArgs('togglePlay()', { paused: $audio.current?.paused }));
    if ($audio.current?.paused) {
      $audio.current.play().catch((err) => {
        console.error(err);
        buildAudio();
      });
    } else {
      $audio.current?.pause();
    }
  };

  const disconnect = async () => {
    if (debug) console.log(...prepareArgs('disconnect()'));
    killMode.current = true;
    if ($audio.current) {
      URL.revokeObjectURL($audio.current.src);
    }
    killAnalyser();
    killStream();
    killRecorder();
    await audioContext.kill();
  };

  const requestData = () => {
    if (mediaRecorder.current && mediaRecorder.current.state !== 'inactive') {
      requestDataAttempts.current = 0;
      requestDataEmpties.current = 0;
      mediaRecorder.current.requestData();
    }
  };

  const handleMediaRecorderEvent = async (event: Event) => {
    if (!mediaRecorder.current) return;
    if (debug) console.log(...prepareArgs('handleMediaRecorderEvent()', { EVENT: event.type, state: mediaRecorder.current.state }));
    setMediaRecorderState(mediaRecorder.current.state);

    switch (event.type) {
      case MediaRecorderEvent.START:
        setStatus(PlayerStatus.RECORDING);
        $audio.current?.pause();
        hasAudioData.current = true;
        startRecordTime.current = Date.now();
        savedPosition.current = 0;
        break;

      case MediaRecorderEvent.PAUSE:
        setStatus(PlayerStatus.RECORDING_PAUSED);
        if (mediaRecorder.current.state !== 'inactive') {
          elapsedRecordTime.current = new Duration({ milliseconds: Date.now() - startRecordTime.current });
          totalRecordTime.current = totalRecordTime.current.add(elapsedRecordTime.current);
          setDuration(totalRecordTime.current.inSeconds);
          setIsProcessing(true);
          requestData();
        }
        break;

      case MediaRecorderEvent.RESUME:
        setStatus(PlayerStatus.RECORDING);
        $audio.current?.pause();
        hasAudioData.current = true;
        startRecordTime.current = Date.now();
        break;

      case MediaRecorderEvent.STOP:
        // a stop automatically triggers requestData which can not be manually triggered after stop
        setStatus(PlayerStatus.STOPPED);
        break;

      case MediaRecorderEvent.ERROR:
        setStatus(PlayerStatus.ERROR);
        if (debug) console.log(...prepareArgs('Recording error', event));
        break;
    }
  };

  const handleDataAvailable = async (event: BlobEvent) => {
    if (debug) console.log(...prepareArgs('handleDataAvailable()', { size: event.data.size, empties: requestDataEmpties.current, attempts: requestDataAttempts.current }));
    if (event.data.size > 0) {
      chunksArray.current.push(event.data);
      setChunksLength(chunksArray.current.length);
    } else {
      requestDataEmpties.current++;
    }
    if (requestDataAttempts.current < 8 && requestDataEmpties.current < 2 && mediaRecorder.current && mediaRecorder.current.state !== 'inactive') {
      requestDataAttempts.current++;
      mediaRecorder.current.requestData();
    } else {
      await buildAudio();
      setIsProcessing(false);
    }
  };

  const handleTrackEvent = (event: Event) => {
    // if (debugEvents) console.log(...prepareArgs('handleTrackEvent()', { type: event.type }));
    if (event?.currentTarget) {
      setHasTrackEvents((b) => (b === true ? b : true));
    }
    setTrackReadyState(isTrackLive() ? 'live' : 'ended');

    switch (event.type) {
      case 'mute':
        setTrackMuted(true);
        pauseRecording();
        break;
      case 'unmute':
        setTrackMuted(false);
        pauseRecording();
        break;
      case 'ended':
        setTrackLive(false);
        pauseRecording();
        break;
      case 'live':
        setTrackLive(true);
        break;
    }
  };

  const isTrackLive = () => {
    return mediaStream.current?.getTracks()?.some((t) => t.readyState === 'live') ?? false;
  };

  const isTrackEnded = () => {
    return mediaStream.current?.getTracks()?.some((t) => t.readyState === 'ended') ?? false;
  };

  const isTrackEnabled = () => {
    return mediaStream.current?.getTracks()?.some((t) => t.enabled) ?? false;
  };

  const isTrackMuted = () => {
    return mediaStream.current?.getTracks()?.some((t) => t.muted) ?? false;
  };

  const isTrackValid = () => {
    return mediaStream.current?.getTracks()?.some((t) => !t.muted && t.enabled && t.readyState === 'live');
  };

  const handleChangePermissions = async () => {
    if (debug) console.log(...prepareArgs('handleChangePermissions()', { state: micPermission.current?.state }));
    setMicPermissionState(micPermission.current?.state ?? 'unknown');
    setHasPermissionEvents((b) => (b === true ? b : true));
    if (micPermission.current?.state == 'denied') {
      pauseRecording();
      await sleep(100);
      killRecorder();
      killStream();
      killAnalyser();
      blobData.current = new Blob();
      setDuration(0);
      setStatus(PlayerStatus.NONE);
    }
    if (micPermission.current?.state == 'granted' && !isTrackLive()) {
      await soundCheck();
      await buildAudio();
    }
  };

  const initStream = async () => {
    if (debug) console.log(...prepareArgs('initStream()', { active: mediaStream.current?.active }));
    if (mediaStream.current?.active !== true || !isTrackValid()) {
      try {
        mediaStream.current = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
        mediaStream.current.getTracks().forEach((track) => {
          track.addEventListener('mute', handleTrackEvent);
          track.addEventListener('unmute', handleTrackEvent);
          track.addEventListener('ended', handleTrackEvent);
          setTrackReadyState(track.readyState);
        });
        setStreamActive(mediaStream.current.active);
      } catch (err) {
        const message = err instanceof Error ? err.message : String(err);
        if (err instanceof Error) {
          if (err.name == 'NotAllowedError') {
            throw new RecordError(RecordErrorType.STREAM_NOT_ALLOWED, message);
          }
        }
        throw new RecordError(RecordErrorType.STREAM_FAILED, message);
      }
    }
    if (!mediaStream.current?.active) {
      throw new RecordError(RecordErrorType.STREAM_INACTIVE, 'unknown');
    }
    if (!isTrackLive()) {
      throw new RecordError(RecordErrorType.TRACKS_INACTIVE, 'unknown');
    }
    if (isTrackMuted()) {
      throw new RecordError(RecordErrorType.TRACKS_MUTED, 'unknown');
    }
    if (!isTrackEnabled()) {
      throw new RecordError(RecordErrorType.TRACKS_DISABLED, 'unknown');
    }
  };

  const killStream = () => {
    if (debug) console.log(...prepareArgs('killStream()'));
    if (mediaStream.current) {
      mediaStream.current.getTracks().forEach((track) => {
        track.removeEventListener('mute', handleTrackEvent);
        track.removeEventListener('unmute', handleTrackEvent);
        track.removeEventListener('ended', handleTrackEvent);
        track.stop();
      });
      mediaStream.current = null;
    }
  };

  const initPermissions = async () => {
    if (!micPermission.current) {
      //   if (debug) console.log(...prepareArgs('initPermissions()'));
      micPermission.current = await navigator.permissions.query({ name: 'microphone' as PermissionName });
      micPermission.current.addEventListener('change', handleChangePermissions);
      micPermission.current.dispatchEvent(new Event('change'));
      setMicPermissionState(micPermission.current.state);
    }
    if (micPermission.current.state == 'denied') {
      throw new RecordError(RecordErrorType.MICROPHONE_DENIED, 'unknown');
    }
  };

  const initSource = async (ctx: AudioContext) => {
    if (debug) console.log(...prepareArgs('initSource()'));

    if (!mediaStream.current) {
      throw new RecordError(RecordErrorType.STREAM_INACTIVE, 'unknown');
    }

    if (ctx?.state != 'running') {
      throw new RecordError(RecordErrorType.AUDIO_CONTEXT_FAILED, 'unknown');
    }

    try {
      sourceNode.current = ctx.createMediaStreamSource(mediaStream.current);
    } catch (err) {
      const message = err instanceof Error ? err.message : String(err);
      throw new RecordError(RecordErrorType.SOURCE_FAILED, message);
    }
  };

  const initRecorder = async () => {
    if (debug) console.log(...prepareArgs('initRecorder()'));

    if (!mediaStream.current) {
      throw new RecordError(RecordErrorType.STREAM_INACTIVE, 'unknown');
    }

    if (!mediaRecorder.current || mediaRecorder.current.state == 'inactive') {
      try {
        const _mimeType = allowMimeTypes.find(MediaRecorder.isTypeSupported);
        mediaRecorder.current = new MediaRecorder(mediaStream.current, { mimeType: _mimeType });
        mediaRecorder.current.addEventListener(MediaRecorderEvent.ERROR, handleMediaRecorderEvent);
        mediaRecorder.current.addEventListener(MediaRecorderEvent.PAUSE, handleMediaRecorderEvent);
        mediaRecorder.current.addEventListener(MediaRecorderEvent.RESUME, handleMediaRecorderEvent);
        mediaRecorder.current.addEventListener(MediaRecorderEvent.START, handleMediaRecorderEvent);
        mediaRecorder.current.addEventListener(MediaRecorderEvent.STOP, handleMediaRecorderEvent);
        mediaRecorder.current.addEventListener(MediaRecorderEvent.DATA_AVAILABLE, handleDataAvailable);
        mimeType.current = mediaRecorder.current.mimeType;
        setMediaRecorderState(mediaRecorder.current.state);
      } catch (err) {
        setMediaRecorderState('inactive');
        const message = err instanceof Error ? err.message : String(err);
        throw new RecordError(RecordErrorType.RECORDER_FAILED, message);
      }
    }

    // streams can mismatch and it still works - TBD
    // if (mediaRecorder.current?.stream.id !== mediaStream.current?.id) {
    //   // TODO: need repro in Safari
    //   throw new RecordError(RecordErrorType.STREAM_MISMATCH);
    // }

    if (!mediaRecorder.current) {
      throw new RecordError(RecordErrorType.RECORDER_FAILED, 'unknown');
    }
  };

  const killRecorder = () => {
    if (debug) console.log(...prepareArgs('killRecorder()'));
    if (mediaRecorder.current?.stream) {
      mediaRecorder.current.stream.getTracks().forEach((track) => {
        if (debug) console.log(...prepareArgs('stopping recorder stream track', track.kind));
        track.removeEventListener('mute', handleTrackEvent);
        track.removeEventListener('unmute', handleTrackEvent);
        track.removeEventListener('ended', handleTrackEvent);
        track.stop();
      });
    }

    if (mediaRecorder.current) {
      if (mediaRecorder.current.state !== 'inactive') {
        try {
          mediaRecorder.current.stop();
        } catch (err) {
          if (debug) console.warn(...prepareArgs('error stopping recorder', err));
        }
      }
      mediaRecorder.current.removeEventListener(MediaRecorderEvent.ERROR, handleMediaRecorderEvent);
      mediaRecorder.current.removeEventListener(MediaRecorderEvent.PAUSE, handleMediaRecorderEvent);
      mediaRecorder.current.removeEventListener(MediaRecorderEvent.RESUME, handleMediaRecorderEvent);
      mediaRecorder.current.removeEventListener(MediaRecorderEvent.START, handleMediaRecorderEvent);
      mediaRecorder.current.removeEventListener(MediaRecorderEvent.STOP, handleMediaRecorderEvent);
      mediaRecorder.current.removeEventListener(MediaRecorderEvent.DATA_AVAILABLE, handleDataAvailable);
      mediaRecorder.current = null;
    }
  };

  const initAnalyser = async (ctx: AudioContext) => {
    if (debug) console.log(...prepareArgs('initAnalyser()'));

    if (ctx.state != 'running') {
      throw new RecordError(RecordErrorType.AUDIO_CONTEXT_FAILED, 'unknown');
    }

    if (!analyserNode.current || analyserNode.current.context.state !== 'running') {
      analyserNode.current = ctx.createAnalyser();
      analyserNode.current.minDecibels = waveConfig.MIN_DECIBELS;
      analyserNode.current.maxDecibels = waveConfig.MAX_DECIBELS;
      analyserNode.current.smoothingTimeConstant = waveConfig.SMOOTHING_TIME_CONSTANT;
      analyserNode.current.fftSize = waveConfig.FFT_SIZE;
      const bufferLength = analyserNode.current.frequencyBinCount;
      dataArray.current = new Uint8Array(bufferLength);
    }
    if (analyserNode.current.context.state !== 'running') {
      throw new RecordError(RecordErrorType.ANALYSER_FAILED, 'unknown');
    }
  };

  const connectAnalyser = async (attempts = 0) => {
    if (debug) console.log(...prepareArgs('connectAnalyser()'));
    if (sourceNode.current && analyserNode.current) {
      if (sourceNode.current.mediaStream.id !== mediaStream.current?.id) {
        if (debug) console.warn(...prepareArgs('connectAnalyser()', 'streams mismatch')); // TODO: no repro yet
      }
      sourceNode.current.connect(analyserNode.current);
    } else {
      if (debug) console.warn(...prepareArgs('connectAnalyser() FAILED', { sourceNode: sourceNode.current, analyserNode: analyserNode.current }));
      if (attempts == 0) {
        await sleep(1000);
        connectAnalyser(attempts + 1);
      }
    }
  };

  const disconnectAnalyser = () => {
    if (debug) console.log(...prepareArgs('disconnectAnalyser()'));
    if (sourceNode.current && analyserNode.current) {
      try {
        sourceNode.current.disconnect(analyserNode.current);
        analyserNode.current.disconnect();
      } catch {
        //
      }
    }
  };

  const killAnalyser = () => {
    if (debug) console.log(...prepareArgs('killAnalyser()'));
    disconnectAnalyser();
    analyserNode.current = null;
  };

  const initMic = async () => {
    if (killMode.current) {
      if (debug) console.log(...prepareArgs('initMic() killMode blocked'));
      return;
    }
    if (debug) console.log(...prepareArgs('initMic()'));

    const ctx = await audioContext.getContextAsync();
    if (ctx.state !== 'running') {
      setErrorType(RecordErrorType.AUDIO_CONTEXT_FAILED);
      return false;
    }

    try {
      await initPermissions();
    } catch (err) {
      if (debug) console.log(...prepareArgs(err));
      if (err instanceof RecordError) {
        if (debug) console.log(...prepareArgs('initMic()', err, { type: err.type }));
        setErrorType(RecordErrorType[err.type]);
      }
      return false;
    }

    try {
      await initStream();
    } catch (err) {
      if (debug) console.log(...prepareArgs(err));
      if (err instanceof RecordError) {
        setErrorType(RecordErrorType[err.type]);
      }
      return false;
    }

    try {
      await initSource(ctx);
    } catch (err) {
      if (debug) console.log(...prepareArgs(err));
      if (err instanceof RecordError) {
        setErrorType(RecordErrorType[err.type]);
      }
      return false;
    }

    try {
      await initRecorder();
    } catch (err) {
      if (debug) console.log(...prepareArgs(err));
      if (err instanceof RecordError) {
        setErrorType(RecordErrorType[err.type]);
      }
      return false;
    }

    try {
      await initAnalyser(ctx);
    } catch (err) {
      if (debug) console.warn(...prepareArgs('initAnalyser() FAILED', err));
    }

    // TODO: iOS Safari records empty air sometimes - no repro yet but this *should* catch it
    if (!isTrackValid()) {
      const muted = isTrackMuted();
      const live = isTrackLive();
      const enabled = isTrackEnabled();

      if (debug) console.error('!!!! track trouble', { muted, live, enabled });

      if (muted) {
        setErrorType(RecordErrorType.TRACKS_MUTED);
      } else if (!live) {
        setErrorType(RecordErrorType.TRACKS_INACTIVE);
      } else if (!enabled) {
        setErrorType(RecordErrorType.TRACKS_DISABLED);
      } else {
        setErrorType(RecordErrorType.UNKNOWN);
      }
    }

    // if (mediaRecorder.current?.stream.id !== mediaStream.current?.id) {
    //   // TODO: need repro in Safari
    //   setErrorType(RecordErrorType.STREAM_MISMATCH);
    //   return false;
    // //   throw new RecordError(RecordErrorType.STREAM_MISMATCH);
    // }

    setErrorType(RecordErrorType.NONE);
    return true;
  };

  const buildAudio = async () => {
    if (debug) console.log(...prepareArgs('buildAudio()', { chunks: chunksArray.current.length }));
    if (chunksArray.current.length == 0) return false;
    blobData.current = new Blob(chunksArray.current, { type: mimeType.current });
    const ctx = audioContext.instance; //getContextAsync();
    let patchedBlob;
    try {
      patchedBlob = await patchBlobWithDuration(blobData.current, ctx);
    } catch (err) {
      // fail-safe if patch fails
      if (debug) console.error(...prepareArgs('buildAudio()', 'patchBlobWithDuration failed', err));
      patchedBlob = blobData.current;
    }
    if ($audio.current) {
      savedPosition.current = $audio.current.currentTime;
      URL.revokeObjectURL($audio.current.src);
      $audio.current.src = URL.createObjectURL(patchedBlob);
      $audio.current.autoplay = true; // Chrome fails to play audio if this is not true
    }
    isWaiting.current = false;
    return true;
  };

  const handleAudioEvent = (event: Event) => {
    if (debug) console.log(...prepareArgs('handleAudioEvent()', { type: event.type }));
    switch (event.type) {
      case AudioEvents.LOADEDDATA:
        $audio.current!.currentTime = savedPosition.current;
        $audio.current!.pause();
        setIsProcessing(false);
        break;

      case AudioEvents.DURATIONCHANGE:
        setDuration($audio.current!.duration);
        totalRecordTime.current = new Duration({ seconds: $audio.current!.duration }); //  this is actual recorded audio duration - not estimated from the ticker
        break;

      case AudioEvents.ERROR:
        if (debug) console.log(...prepareArgs('handleAudioEvent()', { type: event.type }, event));
        break;
    }
  };

  const getWaveData = () => {
    if (analyserNode.current && dataArray.current) {
      analyserNode.current.getByteFrequencyData(dataArray.current);
      return Array.from(dataArray.current).map((n: number) => n / 255);
    }
    return waveConfig.DEFAULT_WAVE;
  };

  const handleVisibilityChange = () => {
    if (debug) console.log(...prepareArgs('handleVisibilityChange()'));
    setIsVisible(document.visibilityState !== 'hidden');
  };

  const handleFocus = useCallback(async () => {
    if (debug) console.log(...prepareArgs('handleFocus()'));
    if (isError && !isProcessing) soundCheck();
  }, [isError, isProcessing]);

  const soundCheck = async () => {
    if (debug) console.log(...prepareArgs('soundCheck()'));
    setIsProcessing(true);
    const success = await initMic();
    setIsProcessing(false);
    return success;
  };

  const completeRecording = async () => {
    if (debug) console.log(...prepareArgs('completeRecording()'));
    try {
      $audio.current?.pause();
      if (hasData && mediaRecorder.current && !isRecording) {
        const extension = mimeToExtension(mimeType.current);
        const type = mimeToMime(mimeType.current);
        if (debug) console.log(...prepareArgs({ mimeType: mimeType.current, extension, type }));
        let file;
        try {
          file = new File([blobData.current], `audio${extension}`, { type });
        } catch (err) {
          if (debug) console.error(...prepareArgs('file failed', err));
        }
        const wave = await getFullWave();
        if (debug) console.log(...prepareArgs('completeRecording()', { file: file?.size, duration, wave }));
        return { file, duration, wave } as IRecordingOutput;
      } else {
        if (debug) console.log(...prepareArgs('completeRecording()', { file: 0, duration, wave }));
        return { file: new File([], ''), duration, wave: [] };
      }
    } catch (err) {
      if (debug) console.error(...prepareArgs(err));
      popup.showPopup(<ProblemSavingMessage />);
      return { file: new File([], ''), duration, wave: [] } as IRecordingOutput;
    }
  };

  const getFullWave = async () => {
    if (debug) console.log(...prepareArgs('getFullWave()', { audioContext: audioContext.state }));
    const ctx = await audioContext.getContextAsync();
    if (ctx.state == 'running') {
      connectAnalyser();

      if (analyserNode.current) {
        if (debug) console.log(...prepareArgs('ac check', { audioContext: audioContext.state }));
        const arrayBuffer = await new Response(blobData.current).arrayBuffer();
        const audioBuffer = await ctx.decodeAudioData(arrayBuffer);
        const source = ctx.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(analyserNode.current);
        analyserNode.current.getByteFrequencyData(dataArray.current);
        if (debug) console.log(...prepareArgs('getFullWave() end'));
        return Array.from(dataArray.current).map((n: number) => n / 255);
      } else {
        if (debug) console.error(...prepareArgs('getFullWave()', 'FAILED'));
      }
    }
    return [];
  };

  const handleDeviceChange = useCallback(() => {
    if (debug) console.log(...prepareArgs('handleDeviceChange()'));
    if (isVisible && mediaRecorder.current?.state !== undefined && mediaRecorder.current?.state !== 'inactive') {
      initMic();
    }
  }, [isVisible]);

  // listen for input device change
  useEffect(() => {
    if (debug) console.log(...prepareArgs('useEffect([handleDeviceChange])'));
    if (navigator.mediaDevices && navigator.mediaDevices.addEventListener) {
      navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
      return () => navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
    }
  }, [handleDeviceChange]);

  // add visibility listeners
  useEffect(() => {
    cleanup();
    window.addEventListener('visibilitychange', handleVisibilityChange, false);
    return () => {
      window.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // prewarm mic
  useEffect(() => {
    // if (debugUseEffect) console.log(...prepareArgs('useEffect([audioMode, isOpen])'), { audioMode, isOpen });
    if (isOpen && audioMode !== AudioMode.PLAY) {
      initMic();
    }
  }, [audioMode, isOpen]);

  // handle errors
  useEffect(() => {
    if (debug) console.log(...prepareArgs('useEffect([errorType, isProcessing])'), { errorType, isProcessing });
    if (!isProcessing) {
      switch (errorType) {
        case RecordErrorType.MICROPHONE_DENIED:
          popup.showPopup(<MicAccessMessage />);
          break;

        case RecordErrorType.STREAM_NOT_ALLOWED:
        case RecordErrorType.STREAM_MISMATCH: // TODO: this should be recoverable
        case RecordErrorType.RECORDER_INACTIVE: // TODO: this should be recoverable
        case RecordErrorType.TRACKS_DISABLED: // TODO: this should be recoverable
          popup.showPopup(<GenericRecordErrorMessage msg={errorType} />);
          break;

        case RecordErrorType.TRACKS_MUTED:
          popup.showPopup(<MicMutedAccessMessage />);
          break;

        case RecordErrorType.PAUSED_ON_BLUR:
          pauseRecording();
          popup.showPopup(<RecordingPausedMessage />);
          break;
      }
    }
  }, [errorType, isProcessing]); //  // TODO: this pops too much

  // handle tracks muted
  useEffect(() => {
    if (trackMuted) {
      setErrorType(RecordErrorType.TRACKS_MUTED);
      setStatus(PlayerStatus.BLOCKED);
      pauseRecording();
    }
  }, [trackMuted]);

  // handle tracks not live
  useEffect(() => {
    if (!trackLive) {
      setErrorType(RecordErrorType.TRACKS_INACTIVE);
      setStatus(PlayerStatus.BLOCKED);
      pauseRecording();
    }
  }, [trackLive]);

  // handle visibility change
  useEffect(() => {
    if (!isVisible && isRecording) {
      setErrorType(RecordErrorType.PAUSED_ON_BLUR);
      pauseRecording();
    }
  }, [isVisible, isRecording]);

  // add focus listener
  useEffect(() => {
    if (!isRecordMode(audioMode)) return;
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [handleFocus, audioMode]);

  // add event listeners to $audio
  useEffect(() => {
    if ($audio.current) {
      $audio.current.addEventListener(AudioEvents.LOADEDDATA, handleAudioEvent);
      $audio.current.addEventListener(AudioEvents.DURATIONCHANGE, handleAudioEvent);
      $audio.current.addEventListener(AudioEvents.ERROR, handleAudioEvent);
      return () => {
        if ($audio.current) {
          $audio.current.removeEventListener(AudioEvents.LOADEDDATA, handleAudioEvent);
          $audio.current.removeEventListener(AudioEvents.DURATIONCHANGE, handleAudioEvent);
          $audio.current.removeEventListener(AudioEvents.ERROR, handleAudioEvent);
        }
      };
    }
  }, [$audio]);

  // poll permissions
  useEffect(() => {
    if (audioMode != AudioMode.PLAY && !hasPermissionEvents) {
      //   if (debugUseEffect) console.log(...prepareArgs('useEffect([audioMode, hasPermissionEvents])', 'INIT permission poll', { audioMode, hasPermissionEvents }));
      const timer = setInterval(async () => {
        if (mediaStream.current) {
          setStreamActive(mediaStream.current?.active ?? false);
        }
        if (micPermission.current?.state) {
          setMicPermissionState(micPermission.current!.state);
        }
      }, 1000);

      return () => {
        // if (debugUseEffect) console.log(...prepareArgs('useEffect([audioMode, hasPermissionEvents])', 'KILL permission poll', { audioMode, hasPermissionEvents }));
        clearInterval(timer);
      };
    }
  }, [audioMode, hasPermissionEvents]);

  // poll tracks
  useEffect(() => {
    if (!hasTrackEvents) {
      //   if (debugUseEffect) console.log(...prepareArgs('useEffect([hasTrackEvents, isRecording])', 'INIT track poll'));
      const timer = setInterval(() => {
        // safari does not fire track events
        if (mediaRecorder.current?.state) {
          const _trackMuted = isTrackMuted();
          const _trackLive = isTrackLive();

          if (trackInfo.current.muted !== _trackMuted) {
            trackInfo.current.muted = _trackMuted;
            handleTrackEvent({ type: _trackMuted ? 'mute' : 'unmute' } as Event);
          }

          if (trackInfo.current.live !== _trackLive) {
            trackInfo.current.live = _trackLive;
            handleTrackEvent({ type: _trackLive ? 'live' : 'ended' } as Event);
          }
        }
      }, 1000);

      return () => {
        // if (debugUseEffect) console.log(...prepareArgs('useEffect([hasTrackEvents, isRecording])', 'KILL track poll'));
        clearInterval(timer);
      };
    }
  }, [hasTrackEvents]);

  // maxDuration, duration, wave tick
  useEffect(() => {
    if (mediaRecorderState !== 'recording') {
      setWave([]);
      return;
    }
    // if (debugUseEffect) console.log(...prepareArgs('useEffect([mediaRecorderState, maxDuration])', 'INIT main ticker'));
    if (audioContext.instance?.state == 'suspended') {
      //   if (debugUseEffect) console.warn(...prepareArgs('useEffect([mediaRecorderState, maxDuration])', 'RESUMED CONTEXT ON TICK'));
      audioContext.instance?.resume();
    }
    connectAnalyser();
    let rafId: number;
    let last = 0;
    const FPS = 20; // same ~50 ms cadence
    const STEP = 1000 / FPS;

    const tick = (now: DOMHighResTimeStamp) => {
      if (now - last >= STEP) {
        last = now;

        // update duration
        const d = totalRecordTime.current.add(Duration.since(startRecordTime.current)).inSeconds;
        setDuration(d);

        // check max duration
        if (d >= maxDuration - 1 / FPS) {
          if (debug) console.log({ maxDuration, d, t: maxDuration - 1 / FPS });
          pauseRecording();
        }

        // export wave
        setWave(getWaveData());
      }
      rafId = requestAnimationFrame(tick);
    };

    rafId = requestAnimationFrame(tick);
    return () => {
      //   if (debugUseEffect) console.log(...prepareArgs('useEffect([mediaRecorderState, maxDuration])', 'KILL main ticker'));
      cancelAnimationFrame(rafId);
    };
  }, [mediaRecorderState, maxDuration]);

  // prompt at maxDuration
  useEffect(() => {
    if (debug) console.log(...prepareArgs('useEffect([isMaxDuration])'));
    if (isMaxDuration) {
      popup.showPopup(<TimeLimitMessage />);
    }
  }, [isMaxDuration]);

  const runTest = async (key: string) => {
    console.log('runtest');
    switch (key) {
      case 'snapshot':
        return {
          status: PlayerStatus[status],
          isSaving: isProcessing,
          errorType: RecordErrorType[errorType], //
          mediaRecorderId: mediaRecorder.current?.stream.id,
          mediaStreamId: mediaStream.current?.id,
          //   trackMuted,
          //   trackLive,
          //   trackReadyState,
          streamActive,
          isError,
          //   micPermissionState,
          hasPermissionEvents,
          hasTrackEvents,
          hasLiveTracks: isTrackLive(),
          hasEnabledTracks: isTrackEnabled(),
          hasMutedTracks: isTrackMuted(),
          sourceNode: { context: sourceNode.current?.context.state ?? '', mediaStream: sourceNode.current?.mediaStream.active ?? '' },
        };
      case 'rebuild_analyser':
        {
          if (debug) {
            console.log(
              JSON.stringify(
                {
                  analyserNode: {
                    channelCount: analyserNode.current?.channelCount, //
                    context: analyserNode.current?.context.state,
                    fftSize: analyserNode.current?.fftSize,
                    numberOfInputs: analyserNode.current?.numberOfInputs,
                    numberOfOutputs: analyserNode.current?.numberOfOutputs,
                  },
                },
                null,
                2,
              ),
            );
          }
          killAnalyser();
          const ctx = await audioContext.getContextAsync();
          initAnalyser(ctx);
        }
        break;
    }
  };

  return (
    <AudioRecorderContext.Provider
      value={{
        $media: $audio,
        status,
        // requestMicAccess,
        unblock,
        startRecording,
        stopRecording,
        pauseRecording,
        resumeRecording,
        resetRecording,
        deleteRecording,
        completeRecording,
        toggleRecording,
        togglePlay,
        disconnect,
        wave,
        setMaxDuration,
        duration,
        maxDuration,
        isMaxDuration,
        hasData,
        displayMode,
        micStatus: micPermissionState ?? '',
        micPermissionState,
        mediaRecorderState,
        isProcessing,
        isLoading,
        isBlocked,
        isRecording,
        isError,
        runTest,
        errorType,
        trackReadyState,
        trackLive,
        trackMuted,
        soundCheck,
        stash,
        restore,
        flush,
        hasStash,
        chunksLength,
        setAutoSave,
        isStashing,
      }}
    >
      {children}
    </AudioRecorderContext.Provider>
  );
};
