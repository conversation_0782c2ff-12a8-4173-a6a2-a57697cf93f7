import { useMemo } from 'react';
import { <PERSON>rubber } from 'react-scrubber';
import { IRecordTrackingData } from '../../../framework/AudioModeProvider';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { AudioMode, PlayerStatus } from '../../../models/models';
import { dataLayer } from '../../../tracking/Tracking';
import { useConfirm } from '../../../utils/useConfirm';
import { useRouteParams } from '../../../utils/useRouteParams';
import { AudioTimesUI } from '../../../view/sound/audio/ui/AudioTimes';
import { MediaScrubberWithTimes } from '../../../view/sound/audio/ui/MediaScrubberWithTimes';
import { useMediaProgress } from '../../../view/sound/media/hooks/useMediaProgress';
import { CircleLoader } from '../../common/circleloader/CircleLoader';
import { AmpMeter } from './AmpMeter';
import { RecorderInfo } from './RecorderInfo';
import { MediaRecorderPlayPause } from './buttons/MediaRecorderPlayPause';
import { PostButton } from './buttons/PostButton';
import { RecordButton } from './buttons/RecordButton';
import { RecorderResetButton } from './buttons/RecorderResetButton';
import { ResetRecordMessage } from './popups/ExitRecordMessage';
import { useRecorder } from './useRecorder';

export const AudioRecorderUI = ({ className = '' }: { className?: string }) => {
  const rec = useRecorder();
  const orch = useOrchestration();
  const { showConfirm } = useConfirm();
  const mediaProgress = useMediaProgress(rec.$media);
  const { trackingData, audioMode } = useAudioMode();
  const scrubberPos = rec.duration / rec.maxDuration;
  const duration = rec.status == PlayerStatus.NONE ? rec.maxDuration : rec.status == PlayerStatus.PLAYING ? mediaProgress.duration : rec.displayMode === 'record' ? rec.maxDuration : rec.mediaRecorderState == 'inactive' ? rec.maxDuration : rec.duration;
  const position = rec.displayMode === 'record' ? rec.duration : mediaProgress.position;
  const recordDisabled = rec.isProcessing || rec.isLoading || rec.isMaxDuration;
  const showRecordScrubber = rec.displayMode === 'record';
  const postDisabled = rec.isProcessing || rec.isRecording || rec.isBlocked || !rec.hasData || orch.isUploading;
  const playControlsDisabled = !rec.hasData || rec.isProcessing || rec.isLoading;

  const playControlsVisible = useMemo(() => {
    if (rec.hasData) {
      if (rec.displayMode === 'play') {
        return true;
      }
      if (rec.displayMode === 'record' && !rec.isRecording) {
        return true;
      }
    }
    return false;
  }, [rec.displayMode, rec.hasData, rec.isRecording]);

  const params = useRouteParams();

  const trackingDelta = useMemo(() => {
    const delta: Partial<IRecordTrackingData> = {};
    if (audioMode === AudioMode.RECORD_NEW) {
      delta.swellcastAlias = params.listId;
    }
    return delta;
  }, [audioMode, params.listId]);

  const onClickRecord = () => {
    if (!rec.hasData && !rec.isRecording) {
      dataLayer({
        ...trackingData,
        ...trackingDelta,
        event: 'swellrecorder',
        context: `start_${trackingData.context}`,
      });
    }
    rec.toggleRecording();
  };

  const onClickReset = async () => {
    if (rec.hasData) {
      const confirmed = await showConfirm(<ResetRecordMessage />);
      if (confirmed) {
        dataLayer({
          ...trackingData,
          ...trackingDelta,
          event: 'swellrecorder',
          context: `delete_${trackingData.context}`,
        });
        await rec.deleteRecording(); // tracking is handled in the function
      }
    }
  };

  const onClickPost = () => {
    dataLayer({
      ...trackingData,
      ...trackingDelta,
      event: 'swellrecorder',
      context: `post_${trackingData.context}`,
    });
    orch.completeRecording();
  };

  return (
    <div className={className}>
      <div className='d-md-none recorder-info-mobile'>
        <RecorderInfo />
      </div>
      <div className='recorder-container'>
        <div>
          <div className='d-none d-lg-block'>
            <RecorderInfo />
          </div>
        </div>

        <div className='d-flex gap-3 align-items-center'>
          <RecorderResetButton disabled={playControlsDisabled} visible={playControlsVisible} onClick={onClickReset} />
          <div style={{ position: 'relative' }}>
            <RecordButton isLoading={rec.isLoading} isRecording={rec.isRecording} disabled={recordDisabled} onClick={onClickRecord} />
            <div className='absolute-center d-none' style={{ pointerEvents: 'none', transition: 'opacity 0.3s', opacity: rec.isLoading ? 0.7 : 0 }}>
              <CircleLoader size={56} color='#fff' />
            </div>
          </div>
          <MediaRecorderPlayPause disabled={playControlsDisabled} visible={playControlsVisible} />
        </div>

        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
          <PostButton disabled={postDisabled} visible={!postDisabled} onClick={onClickPost} />
        </div>
      </div>

      <div className={showRecordScrubber ? 'd-none' : ''}>
        <MediaScrubberWithTimes $media={rec.$media} />
      </div>

      <div className={showRecordScrubber ? '' : 'd-none'}>
        <div>
          <div className='scrubber-container'>
            <Scrubber min={0} max={1} value={scrubberPos} />
          </div>
          <div className='no-select text-white' style={{ marginTop: -15 }}>
            <AudioTimesUI duration={duration} position={position}>
              <div
                className='w-100 mx-auto d-flex flex-center'
                style={{
                  maxWidth: 420,
                  opacity: rec.isRecording ? 1 : 0,
                  transition: 'opacity 1s',
                }}
              >
                <AmpMeter wave={rec.wave} />
              </div>
            </AudioTimesUI>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioRecorderUI;
