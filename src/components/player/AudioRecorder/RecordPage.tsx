import React, { useEffect, useMemo, useState } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import { useProfile } from '../../../api/gql.getProfilePage';
import { IRecordTrackingData } from '../../../framework/AudioModeProvider';
import { HelmetBuilder } from '../../../framework/HelmetBuilder';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { DEFAULT_OG } from '../../../framework/settings/DEFAULT_OG';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { AudioMode, OGModel } from '../../../models/models';
import { useSearchParam } from '../../../utils/useSearchParams';
import { BounceLoaderPage } from '../../common/bounceloader/BounceLoaderPage';
import { useHeaderHeight } from '../../header/useHeaderHeight';
import { SwellButton } from '../../swellbutton/SwellButton';
import { DashSpacer } from '../AudioPlayerUI/DraggableDash';
import { canRecord15 } from './canRecord15';
import { useRecorder } from './useRecorder';
/*

me =>  me/
params is being packaged

'https://www.swellcast.com/do/record
?params={
    "title":"My unforgettable trip to a theme park...",
    "tag":"#PromptsByHeidi #suppheidi3 @heidi https://www.swellcast.com/go/pages/users/heidi.html #ThemeParks",
    "referrerId":"SUPPheidi",
    "categoryId":"TS7TTyL5qHZG|TRygrAeqbOzM",
    "isPremium":false
}
&utm_source=SUPPheidi
&utm_medium=SwellUserPromptPages
&utm_campaign=SUPP_Mar2024'
*/

// ?params isReply=true, swellId

/*

Record Max Duration

- if is reply, 5min
- if master and swellcast capabilities has RECORD15, 15

*/

export interface IOgParams {
  ogImage: string;
  ogTitle: string;
  ogDescription: string;
}

export const RecordPage = () => {
  const orch = useOrchestration();
  const { setTrackingData } = useAudioMode();
  const rec = useRecorder();
  const paramsString = useSearchParam('params', '{"title":"Start a conversation on Swell..."}');
  const [params, setParams] = useState<{ alias: string; title: string; isReply: boolean; swellId?: string; swellcastAlias?: string; promptId?: string }>({ alias: '', title: '', isReply: false, swellcastAlias: '' });
  const isReply = params?.isReply === true;
  const [searchParams] = useSearchParams();
  const loc = useLocation();
  const profile = useProfile({ alias: params.alias });
  const mode = useAudioMode();

  const openRecorder = () => {
    // console.log({ params });

    const t: Partial<IRecordTrackingData> = {};
    if (params?.swellcastAlias) t.swellcastAlias = params?.swellcastAlias;
    if (params?.swellId) t.swellid = params?.swellId;
    if (params?.promptId) t.promptId = params?.promptId;
    setTrackingData(t);

    if (isReply) {
      orch.open({ mode: AudioMode.RECORD_REPLY, payload: { params: paramsString, swellId: params.swellId, swellcastAlias: params.swellcastAlias } });
    } else {
      orch.open({ mode: AudioMode.RECORD_PROMPT_RESPONSE, payload: { params: paramsString } });
    }
  };

  const og: OGModel = useMemo(() => {
    let ogParams: Partial<IOgParams> = {};
    try {
      ogParams = JSON.parse(searchParams.has('params') ? searchParams.get('params') ?? '' : '{}');
    } catch (err) {
      console.log(err);
    }
    const hasOgImage = !!ogParams?.ogImage;
    const ogTitle = hasOgImage ? ogParams?.ogTitle ?? '' : DEFAULT_OG.title;
    const ogDescription = hasOgImage ? ogParams?.ogDescription ?? '' : DEFAULT_OG.description;

    return {
      title: `${ogTitle} | ${ogDescription}`, // SA-7597
      description: 'swellcast.com',
      image: hasOgImage ? ogParams.ogImage : DEFAULT_OG.image,
      canonicalPath: `${loc.pathname}?${searchParams.toString()}`,
    };
  }, [searchParams, loc]);

  useEffect(() => {
    try {
      const data = JSON.parse(paramsString);
      setParams(data);
    } catch (err) {
      console.log(err);
    }
  }, [paramsString]);

  useEffect(() => {
    if (isMounted && !profile.isLoading) {
      openRecorder();
    }
  }, [params, profile.isLoading]);

  // wiat for profile to get ready before opening recorder
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // after profile loads, update maxDuration
    if (profile.isSuccess) {
      if (params.isReply) {
        rec.setMaxDuration(5 * 60);
      } else {
        mode.setTrackingData({ swellcastId: profile.data.id, swellcastAlias: profile.data.owner?.alias ?? (params.swellcastAlias || '') });
        rec.setMaxDuration((canRecord15(profile.data) ? 15 : 5) * 60);
      }
    }
  }, [profile.isSuccess]);

  const headerHeight = useHeaderHeight();
  const style: React.CSSProperties = { minHeight: `calc(100vh - ${headerHeight}px)` };

  if (profile.isLoading) {
    return <BounceLoaderPage />;
  }

  return (
    <>
      <HelmetBuilder {...og} />
      <div className='d-flex w-100 justify-content-center align-items-center position-relative overflow-hidden backdrop-blur-recorder' style={style}>
        {new Array(32).fill(0).map((_e, i) => (
          <div key={`circle${i}`} className='circle-container'>
            <div className='circle'></div>
          </div>
        ))}
        <div className='mx-auto' style={{ maxWidth: BreakPoints.L }}>
          <div className='text-white d-flex flex-column gap-3 p-3 position-relative' onClick={openRecorder}>
            <h3 className='h3-normal'>Record your voice</h3>
            <h1>{params?.title}</h1>
            {orch.isOpen ? (
              <p style={{ opacity: rec.isRecording || rec.hasData ? 0 : 1, transition: 'opacity 0.3s 0.3s' }}>
                Click the red microphone button below to get started.
                <br />
                You&apos;ll have a chance to listen to your recording before you submit.
              </p>
            ) : null}
            <div style={{ opacity: orch.isOpen ? 0 : 1, transition: 'opacity 0.3s 0.3s' }}>
              <SwellButton.Primary className='btn-lg' onClick={openRecorder}>
                Let&apos;s Go!
              </SwellButton.Primary>
            </div>
          </div>
          <DashSpacer style={{ transition: 'height 0.3s' }} />
        </div>
      </div>
    </>
  );
};
