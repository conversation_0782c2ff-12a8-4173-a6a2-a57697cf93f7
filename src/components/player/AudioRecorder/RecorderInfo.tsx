import { ReactNode, useEffect, useState } from 'react';
import { useCurrentSwell } from '../../../api/gql.loadSwellById';
import { SwellIcons } from '../../../assets/icons/SwellIcons';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { AudioMode } from '../../../models/models';
import { getSwellLink2, getSwellcastLink } from '../../../utils/swell-utils';
import { IPromptInfo } from '../../common/PromptCard';
import { SmartLink } from '../../common/SmartLink';
import { useAuth } from '../../login/useAuth';
import { SwellButton } from '../../swellbutton/SwellButton';
import { useRecorder } from './useRecorder';

export const RecorderInfo = () => {
  const { isError, isBlocked, isLoading: isConnecting, isProcessing, unblock } = useRecorder();
  const { audioMode } = useAudioMode();
  const orch = useOrchestration();
  const auth = useAuth();
  const swell = useCurrentSwell();
  const className = 'text-white d-flex align-items-center';
  const [message, setMessage] = useState<ReactNode>(<></>);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // if (isConnecting || isProcessing) {
    //   setMessage(null);
    //   setIsVisible(false);
    //   return;
    // }

    if (isError) {
      setMessage('Error');
    }

    if (isBlocked) {
      setMessage(
        <div className='d-flex flex-wrap justify-content-center justify-content-lg-start align-items-center gap-2 fade-in'>
          <p className='mb-0 fs-5 d-flex align-items-center'>
            Please allow microphone access{' '}
            <a href='https://www.swell.life/faqs#trouble-accessing-microphone-attempting-record' target='_blank' rel='noreferrer' className='ms-1'>
              <SwellIcons.Help style={{ width: 18, height: 18, display: 'inline-block', margin: '0 3px', '--icon-color': '#fff' }} />
            </a>
          </p>
          <div>
            <SwellButton.Primary onClick={unblock} className='btn-sm'>
              Click&nbsp;to&nbsp;Allow
            </SwellButton.Primary>
          </div>
        </div>,
      );
      setIsVisible(true);
      return;
    }

    switch (audioMode) {
      case AudioMode.RECORD_NEW:
        if (auth.loggedIn) {
          setMessage(<h3 className='fade-in m-0'>Posting as &quot;@{auth.user?.alias}&quot;</h3>);
        } else {
          setMessage(<h3 className='fade-in m-0'>Let&apos;s create a Swell!</h3>);
        }
        setIsVisible(true);
        break;

      case AudioMode.RECORD_PROMPT_RESPONSE:
        if (auth.loggedIn) {
          setMessage(
            <div className='fade-in'>
              <p className='opacity-75 m-0'>Posting as &quot;@{auth.user?.alias}&quot;</p>
              <h3>{orch?.payload?.prompt ? (orch?.payload?.prompt as IPromptInfo).title : 'Responding to prompt...'}</h3>
            </div>,
          );
        } else {
          setMessage(
            <div className='fade-in'>
              <p className='opacity-75 m-0'>Let&apos;s create a Swell!</p>
              <h3>{orch?.payload?.prompt ? (orch?.payload?.prompt as IPromptInfo).title : 'Responding to prompt...'}</h3>
            </div>,
          );
        }
        setIsVisible(true);
        break;

      case AudioMode.RECORD_REPLY:
        if (swell.isSuccess) {
          setMessage(
            <div className='fade-in'>
              <p className='opacity-75 m-0'>
                Replying to{' '}
                <SmartLink className='fix text-hover-underline' to={getSwellcastLink({ listId: swell.data.author?.alias ?? '' })}>
                  @{swell.data.author?.alias}
                </SmartLink>
              </p>
              <SmartLink className='fix h3 line-clamp-2' to={getSwellLink2(swell.data)}>
                {swell.data.title}
              </SmartLink>
            </div>,
          );
          setIsVisible(true);
        } else {
          setMessage(<></>);
          setIsVisible(false);
        }
        break;

      case AudioMode.RECORD_PROFILE:
        setMessage(
          <div className='pe-3 fade-in'>
            <h3 className='m-0'>Introduce yourself in your own voice</h3>
            <p className='fw-normal opacity-75'>Example: Hi, I&apos;m Jane. I write books about travel and I love watching movies.</p>
          </div>,
        );
        setIsVisible(true);
        break;

      default:
        setMessage(null);
        setIsVisible(false);
    }
  }, [auth.loggedIn, auth.isReady, audioMode, swell.isSuccess, isBlocked, isConnecting, isProcessing]);

  return (
    <div data-component='recorder-info' className={className} style={{ opacity: isVisible ? 1 : 0, transition: 'opacity 0.3s 0.3s' }}>
      {message}
    </div>
  );
};
