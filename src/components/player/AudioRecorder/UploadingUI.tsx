import { outputMessages } from '../../../framework/settings/outputMessages';
import { useOrchestration } from '../../../framework/useOrchestration';
import { UploadStatus } from '../../../models/models';
import { CircleLoader } from '../../common/circleloader/CircleLoader';
import { RetryUploadButton } from './buttons/RetryUploadButton';

export const UploadingUI = ({ className = '' }: { className?: string }) => {
  const orch = useOrchestration();
  const message = orch.errMsg || outputMessages[orch.uploadStatus];

  let loaderOrRetry = <RetryUploadButton />;

  if (orch.uploadStatus === UploadStatus.UPLOADING) {
    loaderOrRetry = <CircleLoader color='#ccc' />;
  } else if (orch.uploadStatus === UploadStatus.COMPLETED) {
    loaderOrRetry = <></>;
  }

  // 127 min-height hack to maintain height of player when switching modes
  return (
    <div className={`d-flex flex-column flex-center w-100 position-relative gap-3 ${className}`} style={{ minHeight: 127 }}>
      <h4 className='text-white text-center' style={{ maxWidth: 400 }}>
        {message}
      </h4>
      {loaderOrRetry}
    </div>
  );
};
