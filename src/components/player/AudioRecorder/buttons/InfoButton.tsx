import React from 'react';
import { usePopup } from '../../../popup/usePopup';
import { SwellButton } from '../../../swellbutton/SwellButton';

export const InfoButton = ({ message }: { message: React.ReactNode }) => {
  const popup = usePopup();

  const onClick: React.MouseEventHandler<HTMLElement> = (e) => {
    e.stopPropagation();
    popup.showPopup(
      <div className='bg-squash p-4 text-black rounded'>
        <div className='p-4 d-flex flex-column gap-2' style={{ width: 250, maxWidth: '90vw' }}>
          {message}
        </div>
      </div>,
    );
  };

  return <SwellButton.Info onClick={onClick} />;
};
