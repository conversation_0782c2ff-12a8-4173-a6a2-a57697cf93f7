import { useMemo } from 'react';
import { SwellIcons } from '../../../../assets/icons/SwellIcons';
import { PlayerStatus } from '../../../../models/models';
import { useMediaStatus } from '../../../../view/sound/media/hooks/useMediaStatus';
import { SwellButton } from '../../../swellbutton/SwellButton';
import { useRecorder } from '../useRecorder';

export const MediaRecorderPlayPause = ({ disabled = false, visible = true }: { disabled?: boolean; visible?: boolean }) => {
  const { $media, togglePlay } = useRecorder();
  const status = useMediaStatus($media);

  const icon = useMemo(() => {
    switch (status.status) {
      case PlayerStatus.PLAYING:
        return SwellIcons.Pause;
      default:
        return SwellIcons.Play;
    }
  }, [status.status]);

  return (
    <SwellButton.Secondary
      disabled={disabled || !visible} //
      onClick={togglePlay}
      Icon={icon}
      className='icon-white bg-grey-10 bg-hover-grey-40'
      style={{ opacity: visible ? 1 : 0, transition: 'opacity 0.3s, background-color 0.3s' }}
    />
  );
};
