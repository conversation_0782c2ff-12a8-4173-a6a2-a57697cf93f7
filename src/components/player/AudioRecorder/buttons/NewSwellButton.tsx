import React, { CSSProperties } from 'react';
import { useProfile } from '../../../../api/gql.getProfilePage';
import { useOrchestration } from '../../../../framework/useOrchestration';
import { AudioMode } from '../../../../models/models';
import { dataLayer } from '../../../../tracking/Tracking';
import { useIsIframed } from '../../../../utils/useIsIframed';
import { useIsWidget } from '../../../../utils/useIsWidget';
import { widgetUrlTo } from '../../../../utils/widgetUrlTo';
import { useAuth } from '../../../login/useAuth';
import { SwellButton } from '../../../swellbutton/SwellButton';

export const NewSwellButton = ({ alias, postIn, style, className }: { alias?: string | null; postIn?: string; style?: CSSProperties; className?: string }) => {
  const profile = useProfile({ alias });
  const canPost = profile.isSuccess ? profile.data.canPost : false;
  const auth = useAuth();
  const orch = useOrchestration();
  const framed = useIsIframed();
  const isWidget = useIsWidget();
  const duration = profile.isSuccess ? (profile.data?.capabilities?.includes?.('RECORD15') ? 15 : 5) : 5;

  // if canPost and is

  const onClick: React.MouseEventHandler<HTMLElement> = (e) => {
    e.stopPropagation();

    if (framed || isWidget) {
      const href = widgetUrlTo(window.location.pathname);
      window.open(href, '_blank');
    } else {
      orch.open({ mode: AudioMode.RECORD_NEW, maxDuration: duration * 60, payload: { postIn } });
    }

    dataLayer({
      event: 'swell_create',
      context: 'startNewSwell',
      swellcastAlias: alias,
      userAlias: auth.getAlias(),
      swellid: 'NewSwell',
    });
  };

  if (alias && canPost) {
    return <SwellButton.NewSwell onClick={onClick} style={style} className={className} />;
  }

  return null;
};
