import { SwellIcons } from '../../../../assets/icons/SwellIcons';
import { CircleLoader } from '../../../common/circleloader/CircleLoader';
// import './record-button.scss';

export const RecordButton = ({ disabled = false, onClick, isLoading = false, isRecording = false }: { disabled?: boolean; onClick?(): void; isLoading?: boolean; isRecording?: boolean }) => {
  return (
    <button
      onClick={onClick} //
      disabled={disabled}
      className={`record-btn ${isLoading ? 'loading' : ''} ${isRecording ? 'recording' : ''}`}
      style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
    >
      <div className='bg' />
      <div className='loader'>
        <CircleLoader size={60} />
      </div>
      <div className='record-icon'>
        <SwellIcons.Mic style={{ width: '36%', height: '36%', color: '#fff' }} />
      </div>
      <div className='pause-icon'>
        <SwellIcons.Pause style={{ '--icon-color': '#fff', width: '100%' }} />
      </div>
    </button>
  );
};
