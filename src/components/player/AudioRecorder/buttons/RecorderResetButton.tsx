import { SwellIcons } from '../../../../assets/icons/SwellIcons';
import { SwellButton } from '../../../swellbutton/SwellButton';

export const RecorderResetButton = ({ disabled = false, visible = true, onClick }: { onClick?(): void; disabled?: boolean; visible?: boolean }) => {
  return (
    <SwellButton.Secondary
      disabled={disabled || !visible} //
      onClick={onClick}
      Icon={SwellIcons.Bin}
      className='icon-white bg-grey-10 bg-hover-grey-40'
      style={{ opacity: visible ? 1 : 0, transition: 'opacity 0.3s, background-color 0.3s' }}
    />
  );
};
