import { UseQueryResult } from '@tanstack/react-query';
import classNames from 'classnames';
import { useCallback, useMemo } from 'react';
import { useProfile } from '../../../../api/gql.getProfilePage';
import { OpenApiSwellResponseEnhanced, useSwell } from '../../../../api/gql.loadSwellById';
import { SwellIcons } from '../../../../assets/icons/SwellIcons';
import { useOrchestration } from '../../../../framework/useOrchestration';
import { SubscriptionStatus } from '../../../../generated/graphql';
import { AudioMode, OpenAPIError } from '../../../../models/models';
import { dataLayer } from '../../../../tracking/Tracking';
import { getSwellContentType } from '../../../../utils/swell-utils';
import { useIsIframed } from '../../../../utils/useIsIframed';
import { useIsWidget } from '../../../../utils/useIsWidget';
import { widgetUrlTo } from '../../../../utils/widgetUrlTo';
import { CircleLoader } from '../../../common/circleloader/CircleLoader';
import { DownloadAppButton } from '../../../common/downloadapp/DownloadAppButton';
import { SubscribeButton } from '../../../common/swellcastui/SubscribeButton';
import { useAuth } from '../../../login/useAuth';
import { PopupContainer, PopupMessage } from '../../../popup/PopupContainer';
import { usePopup } from '../../../popup/usePopup';
import { SwellButton } from '../../../swellbutton/SwellButton';

export const ReplyButton = ({ canonicalId, replyId, context = 'swell' }: { canonicalId: string; replyId?: string; context?: string }) => {
  // swell
  const swell = useSwell({ canonicalId });
  const alias = swell.isSuccess ? swell.data.swellcast?.owner?.alias : null;
  const isQAndA = swell.isSuccess ? swell.data.isQAndA : false;
  // swellcast
  const profile = useProfile({ alias });
  const duration = 5 * 60;
  // reply
  const reply = swell.isSuccess ? swell.data.replies.find((r) => r.id === replyId) : null;
  const isParent = reply?.isParent ?? false;
  const requiresSubscription = swell.isSuccess && swell.data.subscriptionOnly && profile?.data?.subscriptionState != SubscriptionStatus.Subscribed;
  const canReply = !isQAndA && swell.data?.canReply;
  const disabled = !canReply;
  const promptId = swell.data?.promptId ?? '';
  const payload = reply ? { mention: swell.data?.author?.alias, promptId } : { promptId };

  const auth = useAuth();
  const orch = useOrchestration();
  const popup = usePopup();
  const framed = useIsIframed();
  const isWidget = useIsWidget();
  const contentType = useMemo(() => getSwellContentType(swell.data!), [swell.data]);

  const trackClick = useCallback(() => {
    dataLayer({
      event: 'swell_reply',
      context,
      swellcastAlias: alias,
      userAlias: auth.getAlias(),
      swellid: swell.data?.id ?? 'NewSwell',
      promptId,
      type: contentType,
    });
  }, [auth.isReady, swell.isSuccess]);

  const onClickQAndA: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    trackClick();
    popup.showPopup(<QandAPopup />);
  };

  const onClickSubscription: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    trackClick();
    popup.showPopup(<SubscriptionPopup swell={swell} />);
  };

  const onClickRestricted: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    trackClick();
    popup.showPopup(<NoReplyPopup />);
  };

  const onClickReply: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    trackClick();
    if (framed || isWidget) {
      const href = widgetUrlTo(window.location.href);
      window.open(href, '_blank');
    } else {
      orch.open({ mode: AudioMode.RECORD_REPLY, payload, maxDuration: duration });
    }
  };

  let onClick: React.MouseEventHandler<HTMLButtonElement> = onClickReply;
  let label = 'Reply';
  let ButtonComponent = SwellButton.Primary;

  if (isQAndA) {
    onClick = onClickQAndA;
    if (isParent) {
      label = 'Answer';
      ButtonComponent = SwellButton.Blue;
    } else {
      if (reply) {
        ButtonComponent = SwellButton.Secondary;
      } else {
        label = 'Ask';
        ButtonComponent = SwellButton.Blue;
      }
    }
  } else if (requiresSubscription) {
    onClick = onClickSubscription;
    label = 'Reply';
    if (reply) {
      ButtonComponent = SwellButton.Secondary;
    } else {
      ButtonComponent = SwellButton.Primary;
    }
  } else if (canReply) {
    label = 'Reply';
    if (reply) {
      ButtonComponent = SwellButton.Secondary;
    } else {
      ButtonComponent = SwellButton.Primary;
    }
  } else {
    label = 'Reply';
    onClick = onClickRestricted;
    ButtonComponent = SwellButton.Secondary;
  }

  return (
    <ButtonComponent Icon={SwellIcons.Mic} className={classNames({ disabled }, 'clickable')} onClick={onClick}>
      {label}
    </ButtonComponent>
  );
};

export const QandAPopup = () => (
  <PopupContainer close={() => null}>
    <PopupMessage
      showLogo={true} //
      title="We'd love to hear from you!"
      description='This specific type of Swell requires the Swell app to post a reply.'
    >
      <DownloadAppButton context={'QandASwell'} />
    </PopupMessage>
  </PopupContainer>
);

export const SubscriptionPopup = ({ swell }: { swell: UseQueryResult<OpenApiSwellResponseEnhanced, OpenAPIError> }) => {
  // TODO: test this popup - subscribe is not showing in design popup view
  if (swell.isSuccess) {
    const alias = swell.data.swellcast?.owner?.alias ?? '';
    return (
      <PopupContainer close={() => null}>
        <PopupMessage
          showLogo={true} //
          title="We'd love to hear from you!"
          description='Click subscribe to record your&nbsp;reply.'
        >
          <SubscribeButton alias={alias} />
        </PopupMessage>
      </PopupContainer>
    );
  }

  return (
    <PopupContainer close={() => null}>
      <div className='flex-center' style={{ width: 250, aspectRatio: '1/0.66' }}>
        <CircleLoader />
      </div>
    </PopupContainer>
  );
};

export const NoReplyPopup = () => (
  <PopupContainer close={() => null}>
    <PopupMessage
      title="We'd love to hear from you!" //
      description='Currently, you can not reply to this&nbsp;swell.'
    />
  </PopupContainer>
);
