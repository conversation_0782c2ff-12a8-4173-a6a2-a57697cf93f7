@use '../../../../assets/css/base' as *;

.record-btn {
  @extend .btn-reset, .rounded-circle, .text-white, .overflow-hidden;
  $scale: 1;
  position: relative;
  width: $scale * 60px;
  --icon-fill: #fff;
  height: $scale * 60px;
  > .bg {
    @extend .absolute-fill;
  }
  > .record-icon {
    @extend .absolute-center, .d-flex;
    width: 100%;
    height: 100%;
    transition: opacity 0.2s;

    svg {
      @extend .absolute-center;
    }
  }
  > .pause-icon {
    @extend .absolute-center, .d-flex;
    font-size: $scale * 24px;
    opacity: 0;
    transition: opacity 0.2s;
  }
  > .loader {
    @extend .absolute-fill;
    opacity: 0;
  }
  &:not(:disabled) .bg {
    @extend .bg-record-red;
  }
  &:disabled .bg {
    @extend .bg-grey;
    opacity: 0.5;
    transition: opacity 0.2s linear 0.3s;
  }
  &.loading {
    .loader {
      opacity: 1;
      transition: opacity 0.2s;
    }
    .pause-icon {
      opacity: 0;
    }
  }
  &.recording {
    .bg {
      animation: undulate 2.5s infinite;
    }
    .record-icon {
      opacity: 0;
    }
    .pause-icon {
      opacity: 1;
    }
  }
}
