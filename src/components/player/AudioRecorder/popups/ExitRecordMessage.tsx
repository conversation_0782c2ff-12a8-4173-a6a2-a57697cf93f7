import { PopupMessage } from '../../../popup/PopupContainer';

export const ResetRecordMessage = () => (
  <PopupMessage
    title='Are you sure?' //
    description='You will lose your current recording.'
  />
);

export const ExitRecordMessage = ({ context = '' }: { context?: string }) => (
  <PopupMessage
    style={{ maxWidth: '90vw', width: 400 }}
    title='Friendly reminder...' //
    description='Your recording will be saved as a draft. Come back to this page and click "reply" to continue recording.'
  >
    <div className='d-none'>{context ? context : null}</div>
  </PopupMessage>
);
