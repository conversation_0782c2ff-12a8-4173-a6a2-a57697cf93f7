import { PopupContainer, PopupMessage } from '../../../popup/PopupContainer';

export const GenericRecordErrorMessage = ({ msg = '' }: { msg?: string }) => {
  return (
    <PopupContainer close={() => null}>
      <PopupMessage
        title='Something went wrong while attempting to record.' //
        description='Please check your browser settings and try again'
      >
        <small>{msg}</small>
      </PopupMessage>
    </PopupContainer>
  );
};
