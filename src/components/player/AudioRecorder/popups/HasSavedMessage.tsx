import { PopupContainer, PopupMessage } from '../../../popup/PopupContainer';
import { SwellButton } from '../../../swellbutton/SwellButton';
import { IAudioRecorder } from '../AudioRecorderContext';

export const HasSavedMessage = ({ rec, stashId }: { rec: IAudioRecorder; stashId: string }) => {
  return (
    <PopupContainer close={() => null}>
      <PopupMessage
        title='You have a saved recording.' //
        description='What would you like to do?'
      >
        <div className='d-flex flex-column gap-2 w-100'>
          <SwellButton.Primary className='w-100' onClick={() => rec.restore(stashId)}>
            Restore
          </SwellButton.Primary>
          <SwellButton.Secondary className='w-100' onClick={() => rec.flush(stashId)}>
            Delete
          </SwellButton.Secondary>
        </div>
      </PopupMessage>
    </PopupContainer>
  );
};
