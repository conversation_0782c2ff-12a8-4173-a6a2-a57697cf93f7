import { outputMessages } from '../../../../framework/settings/outputMessages';
import { UploadStatus } from '../../../../models/models';
import { PopupContainer, PopupMessage } from '../../../popup/PopupContainer';

export const UploadMessage = ({ status }: { status: UploadStatus }) => {
  return (
    <PopupContainer close={() => null}>
      <PopupMessage description={outputMessages[status]} />
    </PopupContainer>
  );
};
