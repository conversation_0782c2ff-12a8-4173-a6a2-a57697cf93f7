@use '../../../assets/css/base' as *;

.recorder-container {
  display: grid;
  flex-wrap: wrap;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 8px;
}
.recorder-container > div:nth-child(1) {
  overflow: hidden;
}
.recorder-container > div:nth-child(2) {
  display: flex;
  justify-content: center;
}
.recorder-container > div:nth-child(3) {
  display: flex;
  justify-content: end;
  align-items: center;
}
[data-component='recorder-info'] {
  justify-content: start;
}

.recorder-info-mobile [data-component='recorder-info'] > div {
  @extend .pb-3, .mb-2, .w-100;
  border-bottom: 1px solid rgba(128, 128, 128, 0.25);
}

.recorder-info-mobile {
  padding-bottom: 1rem;
  display: none;
  justify-content: center;
}
@media (max-width: 640px) {
  [data-component='recorder-info'] {
    justify-content: center;
  }
  .recorder-info-mobile {
    display: block;
  }
  .recorder-container {
    grid-template-columns: 1fr min-content 1fr;
  }
  .recorder-container > div:nth-child(1) > * {
    // order: 1;
    // justify-content: center;
    display: none;
  }
  //   .recorder-container > div:nth-child(2) {
  // order: 3;
  // grid-column: 1/3;
  //   }
  //   .recorder-container > div:nth-child(3) {
  // order: 2;
  //   }
}
