// hooks/useRecordStash.ts
import { isServer } from '@tanstack/react-query';
import { createStore, del, get, keys, set } from 'idb-keyval';
import { useCallback } from 'react';
import { AudioQuery } from '../AudioPlayer/IAudioPlayer';

export interface RecordStash {
  id: string;
  chunks: Blob[];
  created: number;
  audioQuery: AudioQuery;
}

const RECORDINGS_DB = isServer ? null! : createStore('swell', 'recordings');
const SENTINEL_PREFIX = '$recmeta$:';
const SENTINEL = (id: string) => `${SENTINEL_PREFIX}${id}`;
const MAX_AGE_DAYS = 3;
const MAX_KEEP = 5;
const isSentinel = (k: unknown): k is string => typeof k === 'string' && k.startsWith(SENTINEL_PREFIX);

export function useRecordStash() {
  const stash = useCallback(async (id: string, chunks: Blob[], audioQuery: AudioQuery) => {
    if (!RECORDINGS_DB || !chunks.length) return;
    try {
      await set(id, { id, chunks, created: Date.now(), audioQuery }, RECORDINGS_DB);
      await set(SENTINEL(id), true, RECORDINGS_DB);
    } catch (err) {
      console.error('[stash] stash() error', err);
    }
  }, []);

  const restore = useCallback(async (id: string): Promise<RecordStash | null> => {
    if (!RECORDINGS_DB) return null;
    const sentinel = await get<boolean>(SENTINEL(id), RECORDINGS_DB);
    if (!sentinel) return null;
    return (await get<RecordStash>(id, RECORDINGS_DB)) ?? null;
  }, []);

  const flush = useCallback(async (id: string): Promise<void> => {
    if (!RECORDINGS_DB) return;
    await Promise.all([del(id, RECORDINGS_DB), del(SENTINEL(id), RECORDINGS_DB)]);
  }, []);

  const hasStash = useCallback(async (id: string): Promise<boolean> => {
    if (!RECORDINGS_DB) return false;
    return (await get<boolean>(SENTINEL(id), RECORDINGS_DB)) === true;
  }, []);

  const cleanup = useCallback(async (): Promise<void> => {
    if (!RECORDINGS_DB) return;
    try {
      const allKeys = await keys(RECORDINGS_DB);
      const recordKeys = allKeys.filter((k) => !isSentinel(k));
      const now = Date.now();
      const maxAgeMs = MAX_AGE_DAYS * 86_400_000;

      const meta = await Promise.all(
        recordKeys.map(async (key) => {
          const rec = await get<RecordStash>(key, RECORDINGS_DB);
          return rec ? { key: key as string, created: new Date(rec.created).getTime() } : null;
        }),
      ).then((arr) => arr.filter(Boolean) as { key: string; created: number }[]);

      const tooOld = meta.filter((m) => now - m.created > maxAgeMs);
      await Promise.all(tooOld.flatMap((m) => [del(m.key, RECORDINGS_DB), del(SENTINEL(m.key), RECORDINGS_DB)]));

      const remaining = meta.filter((m) => now - m.created <= maxAgeMs).sort((a, b) => b.created - a.created);

      if (remaining.length > MAX_KEEP) {
        const excess = remaining.slice(MAX_KEEP);
        await Promise.all(excess.flatMap((m) => [del(m.key, RECORDINGS_DB), del(SENTINEL(m.key), RECORDINGS_DB)]));
      }
    } catch (err) {
      console.error('[stash] cleanup() failed', err);
    }
  }, []);

  return { stash, restore, flush, hasStash, cleanup };
}
