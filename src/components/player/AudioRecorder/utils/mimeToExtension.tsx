// the order is important here
// Chrome fails on mp4, so it needs to allow any one of the other 3 first

function getKeyInMap<K extends string, V>(key: K | undefined | null, map: Record<K, V>, defaultValue: V): V {
  if (!key) return defaultValue;
  return key in map ? map[key] : defaultValue;
}

export const allowMimeTypes = [
  'audio/weba', //
  'audio/m4a',
  'audio/webm', // Mac/Chrome, Mac/Edge, Win/Chrome, Win/Edge, Android/Chrome
  'audio/mp4', // Mac/Safari, iOS/Chrome, iOS/Safari
];

// Swell API requires webm to have extension weba
const extMap = {
  weba: '.weba',
  webm: '.weba',
  mp4: '.m4a',
  'x-m4a': '.m4a',
};

const mimeMap = {
  weba: 'audio/weba',
  webm: 'audio/weba',
  mp4: 'audio/m4a',
  m4a: 'audio/m4a',
  'x-m4a': 'audio/m4a',
};

export const explodeMimeType = (mime: string) => {
  const [kind, codec] = mime.split(';');
  const [channel, filetype] = kind.split('/');
  if (!channel || !filetype) {
    throw new Error(`Invalid MIME type: ${mime}`);
  }
  return { channel, filetype, codec };
};

export const mimeToExtension = (mime: string) => {
  // Converts a MIME type to OUR preferred extension
  const { filetype } = explodeMimeType(mime);
  const ext = getKeyInMap(filetype, extMap, filetype);
  return ext.startsWith('.') ? ext : `.${ext}`;
};

export const mimeToMime = (mime: string) => {
  // Converts a MIME type to OUR preferred mime type
  const { filetype, channel } = explodeMimeType(mime);
  return getKeyInMap(filetype, mimeMap, `${channel}/${filetype}`);
};

// console.table(
//   ['audio/mp4', 'audio/m4a', 'audio/x-m4a', 'audio/mpeg', 'audio/webm'].reduce((s, mimeType) => {
//     const extension = mimeToExtension(mimeType);
//     const type = mimeToMime(mimeType);
//     const filename = `audio${extension}`;
//     s.push({ mimeType, filename, output_type: type });
//     return s;
//   }, [] as Array<unknown>),
// );
