import { audioBufferToWavBlob } from './audioBufferToWavBlob';

export const patchBlobWithDuration = async (blob: Blob, ac?: AudioContext | null | undefined): Promise<Blob> => {
  const arrayBuffer = await blob.arrayBuffer();

  const audioContext = ac ? ac : new AudioContext();
  const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

  const offlineCtx = new OfflineAudioContext(audioBuffer.numberOfChannels, audioBuffer.length, audioBuffer.sampleRate);

  const source = offlineCtx.createBufferSource();
  source.buffer = audioBuffer;
  source.connect(offlineCtx.destination);
  source.start();

  const renderedBuffer = await offlineCtx.startRendering();

  const wavBlob = audioBufferToWavBlob(renderedBuffer);
  return wavBlob;
};
