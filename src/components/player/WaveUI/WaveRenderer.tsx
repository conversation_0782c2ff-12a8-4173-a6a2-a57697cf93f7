import classNames from 'classnames';
import React, { CSSProperties, createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useResizeObserver } from 'usehooks-ts';
import { useOrchestration } from '../../../framework/useOrchestration';
import { useDebug } from '../../debug/useDebug';
import { AudioQuery } from '../AudioPlayer/IAudioPlayer';
import { useAudioTrack } from '../AudioPlayer/useAudioTrack';
import { IGear, gearBox } from './gearBox';

function drawCurve(ctx: CanvasRenderingContext2D, points: { x: number; y: number }[]) {
  let xc: number;
  let yc: number;
  let i = 1;
  ctx.moveTo(points[0].x, points[0].y);
  for (i = 1; i < points.length - 2; i++) {
    xc = (points[i].x + points[i + 1].x) / 2;
    yc = (points[i].y + points[i + 1].y) / 2;
    ctx.quadraticCurveTo(points[i].x, points[i].y, xc, yc);
  }
  ctx.quadraticCurveTo(points[i].x, points[i].y, points[i + 1].x, points[i + 1].y);
}

function reflectPoints(points: { x: number; y: number }[], centerY = 0) {
  return points.map((p) => ({ x: p.x, y: centerY + (centerY - p.y) }));
}

function fillWaveSection($canvas: HTMLCanvasElement, context: CanvasRenderingContext2D, data: number[], offset: number, padding = 5, scale = 1) {
  const centerX = $canvas.width * 0.5;
  const centerY = $canvas.height * 0.5;
  context.beginPath();
  context.moveTo(padding, centerY);
  const xStep = ($canvas.width - padding * 2) / data.length;
  const startIndex = ~~(offset / xStep);
  const xOffset = offset % xStep;
  const points = [];
  let y: number, i: number, n: number;
  let heightFactor: number;
  let x = padding;
  const hFactor = Math.pow(centerX, 3);
  const minX = padding;
  const maxX = $canvas.width - padding;
  const maxH = $canvas.height * 0.5;

  points.push({ x: padding, y: centerY });

  for (n = 1; n < data.length - 1; n++) {
    i = (startIndex + n) % data.length;
    x = padding + (xStep * n - xOffset);
    x = ~~Math.max(minX, Math.min(maxX, x));
    heightFactor = 1 - Math.pow(Math.abs(centerX - x), 3) / hFactor;
    y = centerY + ~~(data[i] * maxH * heightFactor * scale);
    points.push({ x, y });
    x += xStep;
  }
  points.push({ x: $canvas.width - padding, y: centerY });

  drawCurve(context, points);
  drawCurve(context, reflectPoints(points, centerY));
  context.fill();
}

export const RAD = Math.PI / 180;

const gears: IGear[] = [
  {
    angle: 0 * RAD,
    radius: 0.35,
    speed: 11 * RAD,
  },
  {
    angle: 53 * RAD,
    radius: 0.25,
    speed: 23 * RAD,
  },
  {
    angle: -61 * RAD,
    radius: 0.15,
    speed: 17 * RAD,
  },
  {
    angle: 200 * RAD,
    radius: 0.25,
    speed: 4 * RAD,
  },
];

// ChatGPT
function stringToRandomIntegers(input: string) {
  function simpleHash(str: string) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) - hash + str.charCodeAt(i);
      hash |= 0; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  // Generate a base hash
  const baseHash = simpleHash(input);

  // Use the base hash to derive three integers
  const int1 = (baseHash & 0xffff) % 1000; // Use lower 16 bits
  const int2 = ((baseHash >> 16) & 0xffff) % 1000; // Use upper 16 bits
  const int3 = ((baseHash >> 8) & 0xffff) % 1000; // Use shifted middle 16 bits

  return [int1, int2, int3];
}

const WaveContext = createContext({ offset1: 0, offset2: 0, gearOffset: 0, advance: () => {} });

const WaveContextProvider = ({ salt, children }: { salt: string; children: React.ReactNode }) => {
  const seeds = useRef(stringToRandomIntegers(salt));

  const [offsets, setOffsets] = useState({
    offset1: seeds.current[0],
    offset2: seeds.current[1],
    gearOffset: seeds.current[2],
  });

  const advance = () => {
    // rotate gears at predefined rates (basically slow, medium, fast)
    setOffsets((o) => ({
      offset1: o.offset1 + 4,
      offset2: o.offset2 + 8,
      gearOffset: o.gearOffset + 1,
    }));
  };

  return <WaveContext.Provider value={{ ...offsets, advance }}>{children}</WaveContext.Provider>;
};

const useCanvasLoader = () => {
  const [canvas, setCanvas] = useState<HTMLCanvasElement>();
  useEffect(() => {
    const cvs = document.createElement('canvas');
    cvs.width = 500;
    cvs.height = 2;
    const ctx = cvs.getContext('2d');
    if (ctx) {
      const g = ctx.createLinearGradient(0, 0, 500, 0);
      g.addColorStop(0, 'rgba(255,255,255,0)');
      g.addColorStop(0.2, 'rgba(255,255,255,1)');
      g.addColorStop(0.8, 'rgba(255,255,255,1)');
      g.addColorStop(1, 'rgba(255,255,255,0)');
      ctx.fillStyle = g;
      ctx.fillRect(0, 0, 500, 2);
      setCanvas(cvs);
    }
  }, []);
  return canvas;
};

const WaveRenderer = ({ active = false, isLoading = false, onClick, style = {}, className = '' }: { className?: string; active?: boolean; isLoading: boolean; onClick?: React.MouseEventHandler<HTMLCanvasElement>; style?: CSSProperties }) => {
  const offsets = useContext(WaveContext);
  const $canvas = useRef<HTMLCanvasElement>(null!);
  const context = $canvas?.current?.getContext('2d');
  const eq = useRef([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
  const { width = 0, height = 0 } = useResizeObserver({ ref: $canvas });
  const loadValue = useRef(0);
  const $canvasLoader = useCanvasLoader();

  const draw = useCallback(() => {
    if (!$canvasLoader || !$canvas.current || !context) return;

    loadValue.current = Math.max(0, Math.min(1, loadValue.current + 0.1 * (isLoading ? 1 : -1)));
    context.clearRect(0, 0, width, height);
    context.globalAlpha = loadValue.current * 0.4;
    context.drawImage($canvasLoader, width - ((offsets.gearOffset * 30) % (width + 500)), height * 0.5 - 2);
    context.globalAlpha = (1 - loadValue.current) * 0.4;

    let i: number;
    i = eq.current.length;
    while (i--) {
      eq.current[i] = (gearBox(gears, offsets.gearOffset + i * 8).x + 1) / 2;
    }
    fillWaveSection($canvas.current, context, eq.current, offsets.offset1, 5, 0.8);
    fillWaveSection($canvas.current, context, eq.current, offsets.offset2, 5, 0.5);
  }, [$canvasLoader, context, isLoading, offsets.gearOffset, offsets.offset1, offsets.offset2, height, width]);

  useEffect(() => {
    if (context) {
      draw();
    }
  }, [context, draw, offsets.offset1]);

  const prepCanvas = useCallback(() => {
    if (context) {
      context.fillStyle = '#ffffff';
      context.globalAlpha = 0.4;
    }
  }, [context]);

  useEffect(() => {
    if (context) {
      prepCanvas();
      draw();
    }
  }, [width, height, context, prepCanvas, draw]);

  useEffect(() => {
    if ($canvas.current && context && (active || isLoading)) {
      prepCanvas();
      let anim: number;
      const timer = setInterval(() => {
        anim = requestAnimationFrame(offsets.advance);
      }, 50);

      return () => {
        cancelAnimationFrame(anim);
        clearInterval(timer);
      };
    }
  }, [context, active, isLoading, prepCanvas, offsets.advance]);

  return (
    <canvas
      ref={$canvas} //
      onClick={onClick}
      {...(onClick ? { role: 'button' } : {})}
      aria-label='toggle play pause'
      tabIndex={0}
      style={{
        // position: 'absolute', //
        // inset: 0,
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        position: 'relative',
        ...style,
      }}
      className={className}
      width={width}
      height={height}
    />
  );
};

export const DynamicWave = ({ active, audioQuery }: { active: boolean; audioQuery?: AudioQuery }) => {
  const { togglePlay } = useOrchestration();
  const { isPlaying, isLoading, isBlocked, togglePlay: toggleTrack } = useAudioTrack();
  const { debug } = useDebug('autoplay');
  const onClick = useCallback(() => {
    if (active) {
      if (isBlocked) {
        if (debug) console.log('play is blocked');
      }
      toggleTrack();
    } else {
      if (audioQuery) {
        if (debug) console.log('DynamicWave::togglePlay');
        togglePlay(audioQuery, true);
      }
    }
  }, [active, isBlocked, toggleTrack, debug, audioQuery, togglePlay]);

  return (
    <WaveContextProvider salt={`${audioQuery?.playlistId}${audioQuery?.trackId}`}>
      <div data-component='audiowave' className={classNames({ active })} aria-label='toggle play pause'>
        <WaveRenderer
          active={active && isPlaying} //
          isLoading={active && isLoading}
          onClick={onClick}
        />
      </div>
    </WaveContextProvider>
  );
};
