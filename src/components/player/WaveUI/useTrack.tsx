import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { AudioMode } from '../../../models/models';
import { AudioQuery } from '../AudioPlayer/IAudioPlayer';
import { useAudioTrack } from '../AudioPlayer/useAudioTrack';

export const useTrack = (audioQuery: AudioQuery) => {
  const { audioQuery: selectedAudioQuery, audioMode } = useAudioMode();
  const { isOpen, togglePlay } = useOrchestration();
  const playlistActive = isOpen && audioMode === AudioMode.PLAY && audioQuery.playlistId === selectedAudioQuery.playlistId;
  const trackActive = audioQuery.trackId ? playlistActive && audioQuery.trackId === selectedAudioQuery.trackId : playlistActive;
  const active = isOpen && audioMode === AudioMode.PLAY && trackActive;
  const { isPlaying: trackIsPlaying, isLoading: trackIsLoading, isBlocked: trackIsBlocked, togglePlay: trackTogglePlay } = useAudioTrack();
  const isPlaying = active && trackIsPlaying;
  const isLoading = active && trackIsLoading;
  const isBlocked = active && trackIsBlocked;

  const onClick = () => {
    console.log('onClick');
    if (active) {
      if (isBlocked) {
        console.log('play is blocked');
      }
      trackTogglePlay();
    } else {
      if (audioQuery) {
        togglePlay(audioQuery, true);
      }
    }
  };

  return { onClick, active, isPlaying, isLoading, isBlocked, trackActive };
};
