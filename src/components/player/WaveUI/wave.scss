@use '../../../assets/css/base' as *;

[data-bs-theme='light'] {
  [data-component='audiowave'] {
    background-color: #bbb;
  }
}
[data-bs-theme='dark'] {
  [data-component='audiowave'] {
    background-color: #333;
  }
}
[data-component='audiowave'] {
  @extend .rounded-pill, .pointer, .overflow-hidden;
  position: relative;
  width: 100%;
  height: 42px;
  max-width: 100%;
  transition: background-color 0.5s;
}
[data-component='audiowave']::before {
  @extend .bruno-colors;
  content: '';
  display: block;
  position: absolute;
  inset: 0;
  opacity: 0;
  transition: opacity 1s;
}
[data-component='audiowave'].active::before {
  opacity: 1;
}
