import React, { ReactNode } from 'react';
import { SwellIcons } from '../../assets/icons/SwellIcons';
import { SwellLogo } from '../common/SwellLogo';

export const PopupMessage = ({ title, description, showLogo = false, children, style = {} }: { title?: ReactNode; description?: ReactNode; showLogo?: boolean; children?: ReactNode; style?: React.CSSProperties }) => {
  return (
    <div data-component='popupmessage' style={{ width: 250, ...style }} className='text-center d-flex flex-column gap-3 align-items-center'>
      <div>
        {showLogo ? <SwellLogo id='PopupMessageInner' style={{ height: 30 }} className='mx-auto mb-3' /> : null}
        {title ? <h2>{title}</h2> : null}
        {description ? <p>{description}</p> : null}
      </div>
      {children}
    </div>
  );
};

export const PopupContainer = ({ close, children, className = 'p-4 rounded-2 overflow-hidden popup-colors text-white shadow-lg' }: { close?(): void; children: React.ReactNode; className?: string }) => {
  return (
    <div className='p-2'>
      <div data-component='popup-container' className='text-white position-relative' style={{ maxWidth: '96vw' }}>
        <div className={className}>{children}</div>
        {close ? (
          <button
            style={{
              position: 'absolute', //
              top: 0,
              right: 0,
              transform: 'translate(32%, -32%)',
              border: '1px solid rgba(128,128,128,0.3)',
              aspectRatio: '1/1',
            }}
            className='shadow bg-grey-darkest rounded-pill p-1 text-white btn'
            onClick={close}
          >
            <SwellIcons.Close style={{ '--icon-color': '#fff' }} />
          </button>
        ) : null}
      </div>
    </div>
  );
};
