import React, { useEffect, useState } from 'react';
import { zIndex } from '../../framework/settings/zIndex';
import { useNavSettings } from '../header/useNavSettings';
import { PopupContext } from './PopupContext';

export const PopupProvider = ({ children }: { children: React.ReactNode }) => {
  const [opened, setOpened] = useState(false);
  const [closeOnClickBg, setCloseOnClickBg] = useState(false);
  const [element, setElement] = useState<React.ReactNode>(<div />);
  const nav = useNavSettings();
  const [nextPopup, setNextPopup] = useState<{ element: React.ReactNode; clickBackgroundToClose: boolean } | null>(null);

  const showPopup = (element?: React.ReactNode, clickBackgroundToClose = true) => {
    if (opened) {
      // popup already opened
      setNextPopup({ element, clickBackgroundToClose });
    } else {
      setCloseOnClickBg(clickBackgroundToClose);
      if (element) setElement(element);
      setOpened(true);
    }
  };

  const onClickBackground = () => {
    if (closeOnClickBg) {
      setOpened(false);
    }
  };

  //   const close = () => setOpened(false);

  useEffect(() => {
    // handle situation where nav is opened, then a popup shows. When the popup is hidden it should not allow scroll on the body
    if (!nav.navIsOpen) {
      document.body.style.overflow = opened ? 'hidden' : 'auto';
    }
    if (!opened) {
      if (nextPopup) {
        showPopup(nextPopup.element, nextPopup.clickBackgroundToClose);
        setNextPopup(null);
      }
    }
  }, [opened]);

  return (
    <PopupContext.Provider value={{ opened, close: () => setOpened(false), setOpened, showPopup }}>
      <div
        data-component='popupprovider'
        style={{
          zIndex: zIndex.modal,
          backgroundColor: 'rgba(0,0,0,0.8)',
          position: 'fixed',
          inset: 0,
          maxHeight: '100%',
          maxWidth: '100%',
          transition: 'all 0.3s ease-out, visible 0s 0.3s',
          display: 'flex',
          visibility: opened ? 'visible' : 'hidden',
          opacity: opened ? 1 : 0,
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'auto',
          backdropFilter: 'blur(5px)',
        }}
        onClick={onClickBackground}
      >
        <div style={{ transition: 'all 0.3s', transform: `translateY(${opened ? '0vh' : '-5vh'})`, maxWidth: '100%', maxHeight: '100%' }}>{element}</div>
      </div>
      {children}
    </PopupContext.Provider>
  );
};
