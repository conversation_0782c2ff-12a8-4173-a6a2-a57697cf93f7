import classNames from 'classnames';
import { CSSProperties, useState } from 'react';
import PixelImage from '../../assets/images/pixel.png';
import { HomePageRenderType } from '../../generated/graphql';
import { StaticImage } from '../common/StaticImage';

const sizeLookup = {
  [HomePageRenderType.Square]: '100%',
  [HomePageRenderType.Horizontal]: '76.06%',
  [HomePageRenderType.Vertical]: '190%',
  [HomePageRenderType.HorizontalLandscape]: '33.33%',
  [HomePageRenderType.HorizontalPortrait]: '140%',
  [HomePageRenderType.Audio]: '100%', // unused - needed for typescript
};

export const CardContainer = ({
  size = HomePageRenderType.Square, //
  children = null,
  image = '',
  imageFallback = PixelImage,
  style,
  className = '',
  theme = '',
}: {
  size?: HomePageRenderType;
  image?: string | null;
  imageFallback?: string;
  children?: React.ReactNode;
  style?: CSSProperties;
  className?: string;
  theme?: string;
}) => {
  const imageSrc = image || imageFallback;
  const hasImage = !!image && imageFallback !== PixelImage;
  const [isFallback, setIsFallback] = useState(!hasImage);

  return (
    <div
      className={classNames('card-content', className, { 'card-image-fallback': isFallback })}
      style={
        {
          '--aspect-padding-top': sizeLookup[size],
          ...style,
        } as CSSProperties
      }
      data-bs-theme={theme}
    >
      <StaticImage
        className='card-image' //
        src={imageSrc}
        imageFallback={imageFallback}
        onUseFallback={() => setIsFallback(true)}
      />
      <div className='card-image-cover' />
      <div className='card-content-outer'>
        <div className='card-content-inner'>{children}</div>
      </div>
    </div>
  );
};
