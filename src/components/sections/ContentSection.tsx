import classNames from 'classnames';
import { SwellButton } from '../swellbutton/SwellButton';
import { ScrollableRow } from './ScrollableRow';
import { SectionEmpty } from './SectionEmpty';
import { ContentSectionProps, renderTypeLookup, SectionDataItem, SectionDataKeys } from './sections-models';

export function ContentSection({ section, Renderer, className, style, showDots = false }: ContentSectionProps) {
  const key = section.sectionDataType?.toLowerCase() as SectionDataKeys;
  const data = section[key] as SectionDataItem<typeof key>[];

  if ((data?.length ?? 0) === 0) {
    return <SectionEmpty section={section} />;
  }

  const title = section?.label ?? '';
  const id = `section-${key}-${section.id}`;
  const sectionClassNames = classNames(`section-${key}`, `section-type-${section.type.toLowerCase()}`, className);
  const hasSeeAll = (section?.sectionParams?.seeAllUrl?.length ?? 0) > 0;
  const seeAll = hasSeeAll ? <SwellButton.SeeAll section={section} className='w-100' /> : null;
  const items = data.filter((item) => (item as { id: string })?.id !== 'BADITEM').map((item, i) => <Renderer key={`${sectionClassNames}-${section.id}-${i}`} item={item} section={section} />);

  return (
    <ScrollableRow
      style={style}
      title={title} //
      className={sectionClassNames}
      seeAll={seeAll}
      id={id}
      renderType={renderTypeLookup[section.type]}
      showDots={showDots}
    >
      {items}
    </ScrollableRow>
  );
}
