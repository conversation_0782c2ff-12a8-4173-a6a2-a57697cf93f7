import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { OpenAPIError } from '../../models/models';
import { BounceLoader } from '../common/bounceloader/BounceLoader';

// export const InfinitePaginator = ({ query }: { query: UseInfiniteQueryResult<InfiniteData<OpenApiSwellcastResponseEnhanced, Partial<IPaginate>>, OpenAPIError> }) => {
//   const { ref, inView } = useInView({ rootMargin: '1000px' });
//   const isLoading = query.fetchStatus !== 'idle';

//   useEffect(() => {
//     if (query.isSuccess && query.hasNextPage && !isLoading && inView) {
//       query.fetchNextPage();
//     }
//   }, [query.hasNextPage, isLoading, inView]);

//   if (!query.hasNextPage) {
//     return null;
//   }

//   return (
//     <div ref={ref} className='d-flex justify-content-center align-items-center full-width' style={{ height: 100 }}>
//       {isLoading || query.hasNextPage ? (
//         <div className='text-center fix'>
//           <BounceLoader />
//         </div>
//       ) : (
//         <div className='bg-grey'>
//           <Paginator params={{ offset: query.data!.pageParams[0].offset, limit: query.data!.pageParams[0].limit }} />
//         </div>
//       )}
//     </div>
//   );
// };

export const InfiniteQueryLoader = ({ query }: { query: UseInfiniteQueryResult<InfiniteData<unknown>, OpenAPIError> }) => {
  const { ref, inView } = useInView({ rootMargin: '50%' });
  const isLoading = query.fetchStatus !== 'idle';

  useEffect(() => {
    if (inView && query.hasNextPage && query.fetchStatus === 'idle') {
      query.fetchNextPage();
    }
  }, [query.hasNextPage, query.fetchStatus, inView]);

  if (!query.hasNextPage) {
    return null;
  }

  return (
    <div ref={ref} className='d-flex justify-content-center align-items-center full-width' style={{ height: 100 }}>
      {isLoading || query.hasNextPage ? (
        <BounceLoader />
      ) : (
        <div className='bg-grey'>
          <hr />
        </div>
      )}
    </div>
  );
};
