import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query';
import classNames from 'classnames';
import { CSSProperties } from 'react';
import { Fragment } from 'react/jsx-runtime';
import { HomePageItemModel, InputMaybe, Maybe, OpenApiHomePageSectionResponse, OpenApiSeeAllSectionResponse, Scalars } from '../../generated/graphql';
import { OpenAPIError } from '../../models/models';
import { SwellcastError } from '../../view/website/SwellcastError';
import { BounceLoaderPage } from '../common/bounceloader/BounceLoaderPage';
import { Spacer } from '../common/Spacer';
import { TopLevelPromos } from './blocks/SectionPromoTop';
import { InfiniteQueryLoader } from './InfinitePaginator';
import { SectionsView } from './SectionsView';

export type NormalSection = OpenApiHomePageSectionResponse | OpenApiSeeAllSectionResponse;

// export type SectionsResponse = Partial<Pick<OpenApiStationWithSectionsResponse, 'sections' | 'promos'>>; // | Pick<OpenApiSearchWithSectionsResponse, 'sections'>;

// export type PageArgs = Pick<QueryGetPromptPageArgs, 'sinceSectionId' | 'id'>;

export type SectionQuery = UseInfiniteQueryResult<
  InfiniteData<
    {
      promos?: Maybe<Array<HomePageItemModel>>;
      sections?: Maybe<Array<OpenApiHomePageSectionResponse>>;
    },
    {
      id: Scalars['String']['input'];
      sinceSectionId?: InputMaybe<Scalars['String']['input']>;
    }
  >,
  OpenAPIError
>;

export const InfiniteSectionsView = ({ query: _query, style, className, error }: { query: unknown; style?: CSSProperties; className?: string; error?: React.ReactNode }) => {
  const query = _query as SectionQuery;
  if (query.isSuccess) {
    // handle empty data
    if ((query.data?.pages?.[0]?.sections?.length ?? 0) === 0) {
      return error ?? <SwellcastError description={'Nothing to see here!'} />;
    }
    // return (
    //   <div>
    //     <Pre data={query} />
    //     <button onClick={() => query.fetchNextPage()}>NEXT</button>
    //   </div>
    // );
    return (
      <div className={classNames('sections-container', className)} style={style}>
        {query?.data?.pages?.map((page, i) => (
          <Fragment key={`page-${i}`}>
            <TopLevelPromos promos={page?.promos ?? []} />
            <SectionsView sections={page?.sections ?? []} />
          </Fragment>
        ))}
        <InfiniteQueryLoader query={query} />
        <Spacer />
      </div>
    );
  }

  if (query.isError) {
    return <SwellcastError description='Dude... this is like totally whack.' />;
  }

  return <BounceLoaderPage />;
};
