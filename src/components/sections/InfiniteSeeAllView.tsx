import classNames from 'classnames';
import { CSSProperties, Fragment } from 'react';
import { OpenApiHomePageSectionResponse } from '../../generated/graphql';
import { SwellcastError } from '../../view/website/SwellcastError';
import { BounceLoaderPage } from '../common/bounceloader/BounceLoaderPage';
import { Spacer } from '../common/Spacer';
import { InfiniteQueryLoader } from './InfinitePaginator';
import { SectionQuery } from './InfiniteSectionsView';
import { ScrollableRow } from './ScrollableRow';
import { renderTypeLookup, SectionDataItem, sectionProps } from './sections-models';

export const InfiniteSeeAllView = ({ query: _query, style, className, error }: { query: unknown; style?: CSSProperties; className?: string; error?: React.ReactNode }) => {
  const query = _query as SectionQuery;
  if (query.isSuccess) {
    // handle empty data
    if ((query.data?.pages?.[0]?.sections?.length ?? 0) === 0) {
      return error ?? <SwellcastError description={'Nothing to see here!'} />;
    }

    const section = query?.data?.pages?.[0].sections?.[0];

    if (section) {
      const key = section.sectionDataType?.toLowerCase() as keyof OpenApiHomePageSectionResponse;
      const props = section.sectionDataType ? sectionProps?.[section.sectionDataType] : null;
      const sectionClassNames = classNames(`section-${key}`, `section-type-${section.type.toLowerCase()}`, className);
      const Renderer = props!.Renderer;

      return (
        <div className={classNames('sections-container', className)} style={style}>
          <ScrollableRow style={style} className={sectionClassNames} renderType={renderTypeLookup[section!.type]}>
            {query?.data?.pages?.map((page, i) => {
              const data = (page?.sections?.[0][key] as SectionDataItem<typeof key>[]) ?? [];
              const items = data.map((item, i) => <Renderer key={`${sectionClassNames}-${section.id}-${i}`} item={item} section={section} />);
              return <Fragment key={`seeall${i}`}>{items}</Fragment>;
            })}
          </ScrollableRow>
          <InfiniteQueryLoader query={query} />
          <Spacer />
        </div>
      );
    }
  }

  if (query.isError) {
    return <SwellcastError description='Dude... this is like totally whack.' />;
  }

  return <BounceLoaderPage />;
};
