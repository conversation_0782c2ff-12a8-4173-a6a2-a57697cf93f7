import classNames from 'classnames';
import React, { CSSProperties, useRef } from 'react';
import { SwellIcons } from '../../assets/icons/SwellIcons';
import { useScrollable } from '../common/scrollerblock/useScrollable';
import { NavDots } from '../imageviewer/ImageViewer';
import { SectionHeader } from './SectionHeader';
import { SectionRenderType } from './sections-models';

export const ScrollableRow = ({
  id = '', //
  title = '',
  className = '',
  style = {},
  children,
  seeAll = null,
  renderType = SectionRenderType.HORIZONTAL,
  showDots = false,
}: {
  id?: string;
  title?: string;
  className?: string;
  style?: CSSProperties;
  children: React.ReactNode;
  seeAll?: React.ReactNode;
  renderType?: SectionRenderType;
  showDots?: boolean;
}) => {
  const $scroller = useRef<HTMLDivElement>(null!);
  const scr = useScrollable($scroller);
  const fadeClass = scr.canScrollLeft && scr.canScrollRight ? 'fade-mask-both' : scr.canScrollLeft ? 'fade-mask-left' : scr.canScrollRight ? 'fade-mask-right' : '';
  const showArrows = renderType === SectionRenderType.HORIZONTAL && React.Children.count(children) > 1;
  const showHeader = showArrows || !!title;
  const showBottomSeeAll = renderType === SectionRenderType.VERTICAL && seeAll;
  const noScroll = showArrows && !scr.canScroll;
  const $arrows = showArrows ? (
    <div className={classNames('scrollable-row-arrows d-flex', { 'no-scroll': noScroll })}>
      <button className='btn btn-link rounded-pill shadow-none btn-link p-0' onClick={scr.onClickLeft} disabled={!scr.canScrollLeft}>
        <div className='btn-icon-container'>
          <SwellIcons.ChevronLeft />
        </div>
      </button>
      <button className='btn btn-link rounded-pill shadow-none btn-link p-0' onClick={scr.onClickRight} disabled={!scr.canScrollRight}>
        <div className='btn-icon-container'>
          <SwellIcons.ChevronRight />
        </div>
      </button>
    </div>
  ) : null;

  return (
    <div data-component='scrollablerow' className={classNames('section-row', className, renderType)} style={style}>
      {showHeader ? (
        <SectionHeader
          title={title}
          buttons={
            <>
              {renderType === SectionRenderType.HORIZONTAL ? seeAll : null}
              {$arrows}
            </>
          }
        />
      ) : null}
      <div ref={$scroller} id={id} className={classNames('section-row-outer', fadeClass)}>
        {children}
      </div>

      {showDots && scr.canScroll ? (
        <NavDots
          className='p-2' //
          slides={Array.from({ length: React.Children.count(children) })}
          onClick={scr.scrollTo}
          index={scr.currentIndex}
        />
      ) : null}

      {showBottomSeeAll ? (
        <div className='d-flex justify-content-center p-4'>
          <div style={{ width: 240 }}>{renderType === SectionRenderType.VERTICAL ? seeAll : null}</div>
        </div>
      ) : null}
    </div>
  );
};
