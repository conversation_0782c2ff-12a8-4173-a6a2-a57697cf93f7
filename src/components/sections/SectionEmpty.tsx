import classNames from 'classnames';
import { OpenApiHomePageSectionResponse } from '../../generated/graphql';
import { SectionHeader } from './SectionHeader';

export const SectionEmpty = ({ section }: { section: OpenApiHomePageSectionResponse }) => {
  const key = section.sectionDataType?.toLowerCase() ?? 'none';
  const title = section.label;
  const className = `section-${key}`;

  if (section.emptyMessage) {
    return (
      <div className={classNames('section-row', className)}>
        <SectionHeader title={title} />
        <div className='section-row-outer'>
          <p className='w-100' style={{maxWidth:'100%'}}>{section?.emptyMessage ?? ''}</p>
        </div>
      </div>
    );
  }
  return null;
};
