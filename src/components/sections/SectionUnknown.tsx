import { OpenApiHomePageSectionResponse } from '../../generated/graphql';
import { useDebug } from '../debug/useDebug';

export const SectionUnknown = ({ section }: { section: OpenApiHomePageSectionResponse }) => {
  const { debug } = useDebug('sections');

  if (debug) {
    return (
      <div>
        <h2>{section?.label}</h2>
        <pre>{JSON.stringify({ debug, section }, null, 2)}</pre>
      </div>
    );
  }

  return null;
};
