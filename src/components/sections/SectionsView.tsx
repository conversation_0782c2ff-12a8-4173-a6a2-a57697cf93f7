import { ContentSection } from './ContentSection';
import { NormalSection } from './InfiniteSectionsView';
import { sectionProps } from './sections-models';
import { SectionUnknown } from './SectionUnknown';

export const SectionsView = ({ sections = [] }: { sections: NormalSection[] }) => {
  if (sections.length === 0) {
    return null;
  }

  return (
    <div className='sections-view'>
      {sections.map((section, i) => {
        if (section.id === 'BADITEM') {
          // https://anecure.atlassian.net/browse/SA-7678
          return null;
        }
        const props = section.sectionDataType ? sectionProps?.[section.sectionDataType] : null;
        if (props) {
          return <ContentSection key={`section-${i}`} section={section} {...props} />;
        }
        return <SectionUnknown key={`section-${i}`} section={section} />;
      })}
    </div>
  );
};
