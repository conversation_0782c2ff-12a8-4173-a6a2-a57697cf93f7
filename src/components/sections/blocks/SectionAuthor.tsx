import { OpenApiAuthorModel, PodcastSubType } from '../../../generated/graphql';
import { deorphan } from '../../../utils/Utils';
import { getAuthorFullname, getAuthorLink } from '../../../utils/swell-utils';
import { Mugshot } from '../../common/Mugshot';
import { SmartLink } from '../../common/SmartLink';

export const SectionAuthor = ({ item }: { item: OpenApiAuthorModel }) => {
  const podcastSubType = item?.swellcast?.podcastSubType ?? PodcastSubType.Free;
  const isPro = podcastSubType === PodcastSubType.Pro;

  return (
    <SmartLink data-component='speakerblockui' to={getAuthorLink(item)} className='fix text-center'>
      <Mugshot image={item.image} size={148} className='border mx-auto mb-2' style={{maxWidth:'100%'}} thickness={0} isPro={isPro} />
      <h2 className='text-truncate'>{getAuthorFullname(item)}</h2>
      <h4 className='text-truncate opacity-75'>@{item.alias}</h4>
      <div className='fs-4 line-clamp-2 opacity-75' title={item.swellcast?.description ?? ''}>
        {deorphan(item.swellcast?.description)}
      </div>
    </SmartLink>
  );
};
