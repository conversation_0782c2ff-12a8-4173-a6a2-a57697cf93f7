import { CSSProperties } from 'react';
import { Link } from 'react-router-dom';
import { HomePageRenderType, ImageResponse, OpenApiHomePageSectionResponse } from '../../../generated/graphql';
import { toBgUrl } from '../../../utils/Utils';
import { getLocalPath, isLocalPath } from '../../../utils/isLocalUrl';

const imageBaseStyle: CSSProperties = {
  backgroundSize: 'cover',
  backgroundPosition: 'center',
};

const imageStyles: Record<HomePageRenderType, CSSProperties> = {
  [HomePageRenderType.Horizontal]: {
    aspectRatio: '1 / 0.7606',
  },
  [HomePageRenderType.HorizontalLandscape]: {
    aspectRatio: '1 / 3',
  },
  [HomePageRenderType.Square]: {
    aspectRatio: '1 / 1',
  },
  [HomePageRenderType.Vertical]: {
    aspectRatio: '1 / 1.9',
  },
  [HomePageRenderType.Audio]: {
    aspectRatio: '1 / 1',
  },
  [HomePageRenderType.HorizontalPortrait]: {
    aspectRatio: '1 / 1.9',
  },
};

export const SectionImage = ({ item, section }: { item: ImageResponse; section?: OpenApiHomePageSectionResponse }) => {
  const style = { ...imageBaseStyle, ...(section && section.type ? imageStyles[section.type] : {}), backgroundImage: toBgUrl(item.image) };
  return (
    <Link
      to={isLocalPath(item.url) ? getLocalPath(item.url) : item.url} //
      className='section-image fix d-flex flex-column gap-3'
    >
      <div className='rounded-1 shadow' style={style}></div>
      <div className='fs-4'>{item.label}</div>
    </Link>
  );
};
