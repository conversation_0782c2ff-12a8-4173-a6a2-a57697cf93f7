import classNames from 'classnames';
import { CSSProperties } from 'react';
import { HomePageActionModel, HomePageHeaderModel } from '../../../generated/graphql';
import { dataLayer } from '../../../tracking/Tracking';
import { deorphan, toBgUrl } from '../../../utils/Utils';
import { usePromoAction } from '../../../view/website/home/<USER>';
import { useAuth } from '../../login/useAuth';
import { SwellButton } from '../../swellbutton/SwellButton';

function hexToColor(hex: string) {
  if (hex.length === 6) {
    return `#${hex}`;
  }
  const int = parseInt(hex, 16);
  // Extract ARGB components
  const a = (int >> 24) & 0xff; // Alpha
  const r = (int >> 16) & 0xff; // Red
  const g = (int >> 8) & 0xff; // Green
  const b = int & 0xff; // Blue
  // Convert alpha to 0–1 range
  const alpha = (a / 255).toFixed(2);
  // Return as rgba() string
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

export const SectionPromo = ({ item }: { item: HomePageHeaderModel }) => {
  const auth = useAuth();
  const promoAction = usePromoAction();
  const onClick = () => {
    const generalAction = (item?.actions ?? []).find((a) => !a.label);
    if (generalAction) {
      dataLayer({
        context: generalAction.referrerId,
        type: generalAction.type,
        name: item.text,
        userAlias: auth.getAlias(),
        url: generalAction.params?.url,
      });
      promoAction(generalAction);
    }
  };
  const hasImage = !!item.style?.backgroundImageUrl;
  const hasButtons = (item?.actions ?? []).some((a) => !!a.label);
  const hasCopy = item.text || item.description;
  const buttons = (item?.actions ?? []).filter((a) => !!a.label); //  (item.actions?.length ?? 0) > (hasImage ? 1 : 0) ? item.actions! : [];

  if (hasImage && !hasButtons && !hasCopy) {
    return (
      <div
        onClick={onClick} //
        className='w-100'
      >
        <img src={item.style!.backgroundImageUrl!} className='w-100' />
      </div>
    );
  }

  if (hasImage && (hasButtons || hasCopy)) {
    return (
      <div
        onClick={onClick} //
        className='d-grid w-100 position-relative rounded-3'
        style={{ minHeight: 'fit-content' }}
      >
        <img style={{ gridArea: '1 / 1' }} src={item.style!.backgroundImageUrl!} className='w-100' />

        {/* <div className='d-lg-none w-100' style={{ bottom: 0 }}>
          <div className='bg-white-90 p-3 d-flex flex-column gap-3'>
            <PromoCopy item={item} />
            <PromoButtons buttons={buttons} />
          </div>
        </div> */}

        <div className='w-100' style={{ gridArea: '1 / 1', alignSelf: 'end' }}>
          <div style={{ height: 16, background: 'linear-gradient(0deg,rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0) 100%);' }}></div>
          <div className='bg-white-90 p-3 d-flex flex-column gap-3'>
            <PromoCopy item={item} />
            <PromoButtons buttons={buttons} item={item} />
          </div>
        </div>
      </div>
    );
  }

  if (hasImage && (hasButtons || hasCopy)) {
    return (
      <div
        onClick={onClick} //
        className='w-100 position-relative rounded-3'
        style={{ minHeight: 'fit-content' }}
      >
        <img src={item.style!.backgroundImageUrl!} className='w-100' />

        {/* <div className='d-lg-none w-100' style={{ bottom: 0 }}>
          <div className='bg-white-90 p-3 d-flex flex-column gap-3'>
            <PromoCopy item={item} />
            <PromoButtons buttons={buttons} />
          </div>
        </div> */}

        <div className='Xd-none d-lg-block position-absolute w-100' style={{ bottom: 0 }}>
          <div style={{ height: 16, background: 'linear-gradient(0deg,rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0) 100%);' }}></div>
          <div className='bg-white-90 p-3 d-flex flex-column gap-3'>
            <PromoCopy item={item} />
            <PromoButtons buttons={buttons} item={item} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      onClick={onClick} //
      className='w-100 flex-center d-flex flex-column p-3 gap-3'
      style={
        hasImage
          ? {
              backgroundImage: toBgUrl(item.style?.backgroundImageUrl ?? ''), //
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }
          : {}
      }
    >
      {item.text ? <div className='display-5 m-0 text-center'>{item.text}</div> : null}
      {item.description ? <div className='fs-3 text-center'>{deorphan(item.description)}</div> : null}
      {buttons.length ? <PromoButtons buttons={buttons} item={item} className='align-items-center justify-content-center' /> : null}
    </div>
  );
};

const PromoCopy = ({ item }: { item: HomePageHeaderModel }) => {
  return (
    <div>
      {item.text ? <div className='display-5 text-black'>{item.text}</div> : null}
      {item.description ? <div className='fs-3 text-black'>{deorphan(item.description)}</div> : null}
    </div>
  );
};

const PromoButtons = ({ item, buttons, className = '' }: { buttons: HomePageActionModel[]; className?: string; item: HomePageHeaderModel }) => {
  const promoAction = usePromoAction();
  const auth = useAuth();

  const onClick = (action: HomePageActionModel) => {
    dataLayer({
      event: 'promo',
      context: action.referrerId,
      type: action.type,
      name: item.text,
      userAlias: auth.getAlias(),
      url: action.params?.url,
    });
    promoAction(action);
  };

  return (
    <div className={classNames('d-flex gap-2 w-100', className)}>
      {buttons.map((action, i) => {
        const style: CSSProperties = {};
        const cls = [];

        if (action.style?.fontColorHex) {
          style.color = hexToColor(action.style.fontColorHex);
        } else {
          cls.push('text-mode-reverse');
        }

        if (action.style?.backgroundColorHex) {
          style.backgroundColor = hexToColor(action.style.backgroundColorHex);
        } else {
          cls.push('bg-mode-reverse');
        }

        return (
          <SwellButton.Base key={`btn-${i}`} style={style} className={classNames(cls, 'fw-bold border-grey-30 Xw-100 btn-lg text-nowrap')} onClick={() => onClick(action)}>
            {action.label}
          </SwellButton.Base>
        );
      })}
    </div>
  );
};
