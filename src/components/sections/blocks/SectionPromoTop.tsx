import { ALLOWED_ACTIONS, WebViewAction } from '../../../finder/models';
import { HomePageHeaderModel, HomePageItemModel, HomePageRenderType, HomePageSectionDataType, OpenApiHomePageSectionResponse } from '../../../generated/graphql';
import { dataLayer } from '../../../tracking/Tracking';
import { deorphan, toBgUrl } from '../../../utils/Utils';
import { usePromoAction } from '../../../view/website/home/<USER>';
import { useAuth } from '../../login/useAuth';
import { CardContainer } from '../CardContainer';
import { ContentSection } from '../ContentSection';

const SectionPromoTop = ({ item }: { item: HomePageHeaderModel }) => {
  const auth = useAuth();
  const header = item;
  const promoAction = usePromoAction();
  const style = header?.style ?? {};
  style.backgroundColorHex = parseInt('ffffff', 16).toString();
  const backgroundImage = style?.backgroundImageUrl ? toBgUrl(style.backgroundImageUrl) : '';
  const action = header.actions?.[0];

  const onClick = () => {
    if (action) {
      dataLayer({
        event: 'promo',
        context: action.referrerId,
        type: action.type,
        name: header.text,
        userAlias: auth.getAlias(),
        url: action.params?.url,
      });
      promoAction(action);
    }
  };

  return (
    <CardContainer className='border-grey-30 pointer'>
      <div className='d-flex flex-column justify-content-end w-100 overflow-hidden' onClick={onClick}>
        <div className='fade-mask-bottom-20' style={{ position: 'relative', flex: '1 0 auto', backgroundImage, backgroundSize: 'cover', backgroundPosition: 'center' }}></div>
        <div className='p-3' style={{ flex: '0 1 25%', marginBottom: -1 }}>
          <h4 className='text-blue'>{header.heading}</h4>
          <h2 className='line-clamp-2' style={{ minHeight: '2.4em' }}>
            {deorphan(header.text)}
          </h2>
        </div>
      </div>
    </CardContainer>
  );
};

export const TopLevelPromos = ({ promos }: { promos: HomePageItemModel[] }) => {
  if (promos.length === 0) return null;

  const headers = (promos.find((p) => p.type === 'HEADERS')?.headers?.items?.filter((h) => Object.values(ALLOWED_ACTIONS).includes(h.header?.actions?.[0].type as WebViewAction)) ?? []).map((h) => h.header);

  const section: OpenApiHomePageSectionResponse = {
    sectionDataType: HomePageSectionDataType.Promos,
    authors: [],
    hashtags: [],
    images: [],
    promos: headers as HomePageHeaderModel[],
    prompts: [],
    sectionType: '',
    stations: [],
    swellcasts: [],
    swells: [],
    type: HomePageRenderType.Horizontal,
    id: '99999',
  };

  return <ContentSection section={section} Renderer={SectionPromoTop} className='section-promos-top' showDots={true} />;
};
