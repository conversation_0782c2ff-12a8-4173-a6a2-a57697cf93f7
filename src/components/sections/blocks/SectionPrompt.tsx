import { OpenApiPromptResponse } from '../../../generated/graphql';
import { getPromptLink2 } from '../../../utils/swell-utils';
import { SmartLink } from '../../common/SmartLink';

export const SectionPrompt = ({ item }: { item: OpenApiPromptResponse }) => {
  return (
    <SmartLink to={getPromptLink2(item)} className='prompt-card w-100 d-flex flex-column gap-2 mb-3'>
      <img src={item.promptImageUrl} style={{ width: '100%' }} />
    </SmartLink>
  );
};
