// import BrunoWaveCardImage from '../../../assets/images/bruno-wave-card.svg?url';
import { StationInfoResponse } from '../../../generated/graphql';
import { getStationSquareImage } from '../../../utils/getStationImage';
import { prettifyTitle } from '../../../utils/prettifyTitle';
import { getStationLink } from '../../../utils/swell-utils';
import { useWaveImage } from '../../../utils/useWaveImage';
import { SmartLink } from '../../common/SmartLink';
import { CardContainer } from '../CardContainer';

export const SectionStation = ({ item: station }: { item: StationInfoResponse }) => {
  const url = getStationLink(station);
  const image = getStationSquareImage(station);
  const { dynamicWave } = useWaveImage();

  return (
    <SmartLink title={station.name} data-component='stationcard' to={url} className='section-station fix d-block'>
      <CardContainer image={image} imageFallback={dynamicWave} className='rounded-4' />
      <div className='fs-4 fw-bold line-clamp-2 text-center mt-2'>{prettifyTitle(station.name)}</div>
    </SmartLink>
  );
};
