// import CardImage from '../../../assets/images/card.png';
import { Article } from '@mui/icons-material';
import classNames from 'classnames';
import { SwellIcons } from '../../../assets/icons/SwellIcons';
import { HomePageRenderType, OpenApiHomePageSectionResponse, OpenApiHomePageSwellResponse, PinStatus } from '../../../generated/graphql';
import { PlayerStatus, ReactType } from '../../../models/models';
import { formatDate, formatDuration } from '../../../utils/Utils';
import { extractSwellMeta } from '../../../utils/extractSwellMeta';
import { getAuthorFullname } from '../../../utils/swell-utils';
import { timeago } from '../../../utils/timeago';
import { useWaveImage } from '../../../utils/useWaveImage';
import { Mugshot } from '../../common/Mugshot';
import { SmartLink } from '../../common/SmartLink';
import { useOwner } from '../../common/owner/useOwner';
import { LivePlayButton, PlayButton } from '../../player/AudioPlayerUI/PlayButton';
import { useSettings } from '../../settings/useSettings';
import { FormatCopy } from '../../swell-card/CardDescription';
import { Snippet } from '../../swell-card/snippet/Snippet';
import { SwellButton } from '../../swellbutton/SwellButton';
import { CardContainer } from '../CardContainer';

export const SectionSwell = ({ item: swell, section }: { item: OpenApiHomePageSwellResponse; section?: OpenApiHomePageSectionResponse }) => {
  // NOTE: only HorizontalPortrait cards get to fallback to the swellcast image
  const image = swell?.articles?.[0]?.image ?? (section?.type == HomePageRenderType.HorizontalPortrait ? swell?.swellcast?.image : undefined); // ?? BrunoWaveCard;
  const totalReactions = swell?.reactions?.reduce((n, r) => n + (r?.count ?? 0), 0) ?? 0;
  const pressState = swell?.reactions?.find((r) => r.reaction === ReactType.HEART)?.pressState;
  const title = swell.title;
  const mugshot = swell.author?.image ?? '';
  const alias = swell.author?.alias ?? '';
  const snippet = swell.snippet;
  const showReplyFaces = swell?.repliesCount ?? 0 > 0;
  const showLikeButton = totalReactions > 0;
  const owner = useOwner();
  const showPin = owner.alias?.toLowerCase() === alias.toLowerCase() && swell.pinState === PinStatus.Pinned;
  const hasImage = !!image;
  const isPrompt = !!swell?.promptId;
  const settings = useSettings();
  const { isText, link: swellLink } = extractSwellMeta(swell);
  const { dynamicWave } = useWaveImage();

  if (section?.type == HomePageRenderType.HorizontalPortrait) {
    return (
      <div className='position-relative'>
        <CardContainer image={image} imageFallback={dynamicWave} className='section-swell section-swell-portrait rounded-4' size={HomePageRenderType.HorizontalPortrait}>
          <div className='w-100 d-flex flex-column gap-2 overflow-visible'>
            <div style={{ flex: '1 1 auto' }} />
            <div className='d-flex flex-column gap-4 p-1'>
              <div className='w-100 d-grid gap-3 align-items-center backdrop-blur bg-grey-30 p-1 rounded-pill' style={{ gridTemplateColumns: 'auto min-content', gap: 5, paddingLeft: 0, paddingRight: 0 }}>
                <div className='fix w-100 d-grid align-items-center gap-2 ' style={{ gridTemplateColumns: 'min-content auto', gap: 5, paddingLeft: 0, paddingRight: 0 }}>
                  <div>
                    <Mugshot thickness={0} size={40} image={mugshot} alt={`@${alias}`} />
                  </div>
                  <div className='d-flex flex-column overflow-hidden text-left' style={{ minWidth: 0 }}></div>
                </div>
                {isText ? <WriteButton /> : <PlayButton status={PlayerStatus.NONE} className='play-button-squash' />}
              </div>
            </div>
          </div>
        </CardContainer>
        <div className='p-2'>
          <div className='line-clamp-3 fs-4'>
            <FormatCopy text={swell.title} />
          </div>
        </div>
        <SmartLink to={swellLink} className='absolute-fill' style={{ zIndex: 1 }} />
      </div>
    );
  }

  return (
    <CardContainer image={image} className={classNames('section-swell section-swell-square border-grey-50', { 'swell-has-image': hasImage })} theme={hasImage ? 'dark' : ''}>
      <SmartLink to={swellLink} className='absolute-fill' style={{ zIndex: 1 }} />
      {showPin ? <SwellIcons.Pin className='section-swell-pin' style={{ position: 'absolute', top: '4%', right: '4%' }} width={14} color='#ccc' /> : null}
      <div className='w-100 d-flex flex-column overflow-visible' style={{ padding: 30, paddingTop: 25 }}>
        <div className='d-flex flex-column gap-3'>
          <PremiumCardHeader swell={swell} />
          <div className='w-100 d-grid gap-3 align-items-center' style={{ gridTemplateColumns: 'auto min-content', gap: 5, paddingLeft: 0, paddingRight: 0 }}>
            <div className='fix w-100 d-grid align-items-center gap-3' style={{ gridTemplateColumns: 'min-content auto', gap: 5, paddingLeft: 0, paddingRight: 0 }}>
              <div>
                <Mugshot thickness={0} size={35} image={mugshot} alt={`@${alias}`} />
              </div>
              <div className='d-flex flex-column overflow-hidden text-left gap-1' style={{ minWidth: 0 }}>
                <div className='fs-3 text-truncate'>@{alias}</div>
                <div className='fs-5 text-truncate opacity-75'>
                  {isText ? null : <span className='text-nowrap'>{formatDuration(swell?.audio?.duration)}&nbsp;·&nbsp;</span>}
                  <time title={formatDate(swell.createdOn)} className='text-nowrap' dateTime={swell.createdOn ?? ''}>
                    {timeago(swell.createdOn)}
                  </time>
                </div>
              </div>
            </div>
            {isText ? <WriteButton /> : settings.allowPlayInCard ? <LivePlayButton canonicalId={swell?.canonicalId ?? ''} /> : <PlayButton status={PlayerStatus.PAUSED} className='play-button-squash' />}
            {/* {settings.allowPlayInCard ? <LivePlayButton canonicalId={swell?.canonicalId ?? ''} /> : <PlayButton status={PlayerStatus.PAUSED} className='play-button-squash' />} */}
          </div>
          <div>
            {title ? (
              <h3 className='line-clamp-2 text-left' title={title} style={{ overflowWrap: 'anywhere' }}>
                <FormatCopy text={title} />
              </h3>
            ) : null}
            {snippet ? <Snippet snippet={snippet} className='fs-4 line-clamp-3 lh-normal' /> : null}
          </div>
        </div>
        <div style={{ flex: '1 1 auto' }} />
        <div className='d-flex gap-2 justify-content-between'>
          <div className='d-flex gap-2'>
            {showLikeButton ? <SwellButton.LikeXS state={pressState} count={totalReactions} /> : null}
            {showReplyFaces ? <SwellButton.ReplyFacesXS faces={swell.faces} count={swell.repliesCount} /> : null}
          </div>
          <div style={{ position: 'relative', zIndex: 999 }}>
            {isPrompt ? (
              <SwellButton.Prompt
                small={true}
                alias={swell.swellcastOwner?.alias ?? ''}
                promptId={swell?.promptId ?? ''}
                slug={swell?.promptSlug ?? ''}
                tracking={{
                  swellId: swell.id,
                  swellcastAlias: swell.swellcastOwner?.alias ?? '',
                }}
              />
            ) : null}
          </div>
        </div>
      </div>
    </CardContainer>
  );
};

const WriteButton = () => (
  <button className='btn write-btn write-btn-circle'>
    <Article />
  </button>
);

const PremiumCardHeader = ({ swell }: { swell: OpenApiHomePageSwellResponse }) => {
  //   const isCommunity = swell?.swellcast?.podcastType === PodcastType.Community;
  const isPremium = swell?.subscriptionOnly === true;
  const swellcastName = swell?.swellcastOwner?.firstName;
  const fullname = getAuthorFullname(swell?.swellcastOwner);

  if (isPremium) {
    return (
      <div className='fs-4 line-clamp-1 text-truncate text-center d-flex gap-2 justify-content-center mx-auto' style={{ width: 'min-content', maxWidth: '100%' }}>
        <span style={{ flex: '0 0 min-content' }} className='text-premium-blue fw-bold'>
          Premium
        </span>
        <span style={{ flex: '0 0 min-content' }}>-</span>
        <span className='text-truncate' style={{ flex: '1 1 auto' }}>
          {swellcastName}
        </span>
      </div>
    );
  }

  if (fullname) {
    return (
      <div className='fs-4 line-clamp-1 text-truncate text-center d-flex gap-2 justify-content-center mx-auto' style={{ width: 'min-content', maxWidth: '100%' }}>
        <span className='text-truncate' style={{ flex: '1 1 auto' }}>
          {fullname}
        </span>
      </div>
    );
  }

  return (
    <div className='fs-4 line-clamp-1 text-truncate text-center d-flex gap-2 justify-content-center mx-auto' style={{ width: 'min-content', maxWidth: '100%' }}>
      &nbsp;
    </div>
  );

  //   return (
  //     <div className='fs-4 line-clamp-1 text-truncate text-center d-flex gap-2 justify-content-center mx-auto' style={{ width: 'min-content', maxWidth: '100%' }}>
  //       &nbsp;
  //       {isPremium ? (
  //         <span style={{ flex: '0 0 min-content' }} className='text-squash-dark '>
  //           Premium
  //         </span>
  //       ) : null}
  //       {isCommunity && isPremium && swellcastName ? <span style={{ flex: '0 0 min-content' }}>-</span> : null}
  //       {isCommunity && swellcastName ? (
  //         <span className='text-truncate opacity-75' style={{ flex: '1 1 auto' }}>
  //           {swellcastName}
  //         </span>
  //       ) : null}
  //       &nbsp;
  //     </div>
  //   );
};
