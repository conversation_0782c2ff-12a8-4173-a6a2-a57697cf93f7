import { OpenApiSwellcastResponse, PodcastSubType } from '../../../generated/graphql';
import { getAuthorFullname, getAuthorLink } from '../../../utils/swell-utils';
import { useWaveImage } from '../../../utils/useWaveImage';
import { Mugshot } from '../../common/Mugshot';
import { SmartLink } from '../../common/SmartLink';
import { CardContainer } from '../CardContainer';

export const SectionSwellcast = ({ item: swellcast }: { item: OpenApiSwellcastResponse }) => {
  const alias = `@${swellcast.owner?.alias}`;
  // TODO: unheard count indicator
  const unheardCount = swellcast?.unheardCount ?? 0;
  const { dynamicWave } = useWaveImage();

  return (
    <SmartLink to={getAuthorLink(swellcast?.owner)} className='section-swellcast position-relative'>
      <CardContainer image={swellcast?.image} imageFallback={dynamicWave} className='section-swellcast rounded-4'>
        <div className='fix d-flex flex-column justify-content-between p-1 text-left w-100 text-white text-shadow'>
          <div className='d-flex justify-content-end p-1'>
            <UnheardDot unheardCount={unheardCount} />
          </div>
          <div className='flex-fill' />
          <div className='w-100 d-grid align-items-center gap-2 p-1 text-white text-shadow rounded-pill backdrop-blur-darken' style={{ gridTemplateColumns: 'min-content auto', zIndex: 1, gap: 5, paddingLeft: 0, paddingRight: 0 }}>
            <div className='position-relative'>
              <Mugshot thickness={0} size={40} image={swellcast.owner?.image} alt={alias} isPro={swellcast.podcastSubType === PodcastSubType.Pro} />
            </div>
            <div className='d-flex flex-column justify-content-center overflow-hidden text-left' style={{ minWidth: 0 }}>
              <div className='h4 m-0 text-truncate' style={{ lineHeight: 1.2 }}>
                {getAuthorFullname(swellcast.owner)}
              </div>
              <div className='h3 fw-normal m-0 text-truncate opacity-75' style={{ lineHeight: 1.2 }}>
                {alias}
              </div>
            </div>
          </div>
          <div></div>
        </div>
      </CardContainer>
    </SmartLink>
  );
};

const UnheardDot = ({ unheardCount }: { unheardCount: number }) => {
  if (unheardCount > 0) {
    return (
      <div className='bg-squash rounded-pill py-1 px-2' title={`${unheardCount}`}>
        <div className='rounded-circle bg-notification-red' style={{ width: 5, height: 5 }} />
      </div>
    );
  }
  return null;
};
