import { CSSProperties } from 'react';
import { HomePageRenderType, HomePageSectionDataType, OpenApiHomePageSectionResponse } from '../../generated/graphql';
import { SectionAuthor } from './blocks/SectionAuthor';
import { SectionHashtag } from './blocks/SectionHashtag';
import { SectionImage } from './blocks/SectionImage';
import { SectionPromo } from './blocks/SectionPromo';
import { SectionPrompt } from './blocks/SectionPrompt';
import { SectionStation } from './blocks/SectionStation';
import { SectionSwell } from './blocks/SectionSwell';
import { SectionSwellcast } from './blocks/SectionSwellcast';
import { NormalSection } from './InfiniteSectionsView';

export type SectionDataKeys = keyof OpenApiHomePageSectionResponse;

export type SectionDataItem<K extends SectionDataKeys> = OpenApiHomePageSectionResponse[K] extends Array<infer U> ? U : never;

type SectionRenderer = React.FC<{ item: SectionDataItem<keyof OpenApiHomePageSectionResponse>; section?: OpenApiHomePageSectionResponse }>;

export type ContentSectionProps = {
  section: NormalSection; //
  Renderer: SectionRenderer;
  className?: string;
  style?: CSSProperties;
  showDots?: boolean;
};

export enum SectionRenderType {
  HORIZONTAL = 'section-horizontal',
  VERTICAL = 'section-vertical',
}

export const renderTypeLookup: Record<HomePageRenderType, SectionRenderType> = {
  [HomePageRenderType.Square]: SectionRenderType.HORIZONTAL,
  [HomePageRenderType.Horizontal]: SectionRenderType.HORIZONTAL,
  [HomePageRenderType.Vertical]: SectionRenderType.VERTICAL,
  [HomePageRenderType.HorizontalLandscape]: SectionRenderType.HORIZONTAL,
  [HomePageRenderType.Audio]: SectionRenderType.HORIZONTAL,
  [HomePageRenderType.HorizontalPortrait]: SectionRenderType.HORIZONTAL, // what is this??
};

export const sectionProps: Record<HomePageSectionDataType, Pick<ContentSectionProps, 'Renderer' | 'showDots'>> = {
  [HomePageSectionDataType.Authors]: { Renderer: SectionAuthor },
  [HomePageSectionDataType.Hashtags]: { Renderer: SectionHashtag },
  [HomePageSectionDataType.Images]: { Renderer: SectionImage },
  [HomePageSectionDataType.Prompts]: { Renderer: SectionPrompt },
  [HomePageSectionDataType.Stations]: { Renderer: SectionStation },
  [HomePageSectionDataType.Swellcasts]: { Renderer: SectionSwellcast },
  [HomePageSectionDataType.Swells]: { Renderer: SectionSwell },
  [HomePageSectionDataType.Promos]: { Renderer: SectionPromo, showDots: true },
};

export const EMPTY_SECTION = {
  swells: [],
  type: HomePageRenderType.Horizontal,
  label: '',
  id: '',
  sectionParams: {},
  authors: [],
  hashtags: [],
  images: [],
  promos: [],
  prompts: [],
  sectionType: '',
  stations: [],
  swellcasts: [],
  sectionDataType: HomePageSectionDataType.Swells,
};
