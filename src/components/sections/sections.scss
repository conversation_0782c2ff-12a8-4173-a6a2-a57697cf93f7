@use '../../assets/css/base' as *;

$max-width-sections: ($swell-card-width * 4px) + ($grid-gap * 3px) + ($grid-pad * 2px);

:root {
  --grid-col-min: 100vw;
  --grid-col-width: #{$grid-col-width};
  --grid-gap: #{$grid-gap};
  --grid-pad: #{$grid-pad};
  --card-padding: #{$card-padding};
  --aspect-padding-top: 100%;
  --max-width-sections: #{$max-width-sections};
}

.max-width-sections {
  max-width: #{$max-width-sections};
}

[data-component='scrollablerow'] {
  max-width: 100vw;
}

.scrollable-row-arrows {
  .btn {
    --bs-btn-disabled-opacity: 0.25;
    transition: all 0.2s;
  }
  &.no-scroll {
    display: none !important;
  }
}

.sections-container {
  @extend .max-width-sections;
  display: flex;
  flex-direction: column;
  //   gap: calc(var(--grid-gap) * 1px);
  margin: 0 auto 50px auto;
  width: 100%;
}

.sections-view {
  display: flex;
  flex-direction: column;
  //   gap: calc(var(--grid-gap) * 1px);
  position: relative;
}
/*

mobile response

*/
@media (max-width: $mobile-width) {
  .section-type-vertical .section-row-outer {
    justify-content: center;
  }
  .section-stations {
    & > .section-row-outer {
      grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
  }
  .section-row.section-swells .section-row-outer {
    grid-template-columns: 1fr !important;
  }
  .section-promos-top {
    --grid-gap: 0;
    --grid-pad: 0;
    --grid-col-width: 100vw;

    .card-content {
      scroll-snap-align: start;
      border-radius: inherit;
      border: none;
    }
  }
  .card-content {
    scroll-snap-align: center;
  }
  .section-row.section-vertical {
    .section-row-outer {
      justify-content: center;
    }
  }
}
/*

card base

*/
.card-content {
  @extend .pointer, .border-grey-dark-20;
  border-radius: 22px;
  height: fit-content;
  position: relative;
  max-width: 100%;
  min-width: var(--grid-col-width);
  width: 100%;
  box-sizing: border-box;
  overflow: auto;
  flex: 0 0 min(var(--grid-col-width) * 1px, 100vw - (var(--grid-gap) * 2px));

  .card-image {
    position: absolute;
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
  }

  .card-image-cover {
    position: absolute;
    inset: 0;
  }

  .card-content-outer {
    @extend .min-aspect, .cover;
    position: relative;
    z-index: 1;

    .card-content-inner {
      @extend .min-aspect;
      display: flex;
      align-items: stretch;
      justify-content: center;
      overflow: auto;
    }
  }
}
/*

section-row

*/
.section-header {
  @extend .d-flex, .grid-pad, .justify-content-between, .align-items-center, .mb-3; // , .mt-5
}
.section-header-buttons {
  @extend .d-flex, .align-items-center, .gap-2;
}
.section-row {
  .section-row-outer {
    overflow: auto;
    -ms-overflow-style: none; /* For Internet Explorer and Edge */
    scrollbar-width: none; /* For Firefox */
    &::-webkit-scrollbar {
      display: none; /* For Chrome, Safari, and Opera */
    }
    gap: calc(var(--grid-gap) * 1px);
    padding-left: calc(var(--grid-pad) * 1px);
    padding-right: calc(var(--grid-pad) * 1px);
    margin-bottom: calc(var(--grid-pad) * 1px);
  }
  // horizontal scroller
  &.section-horizontal {
    .section-row-outer {
      display: flex;
      scroll-snap-type: x mandatory;
      scroll-snap-stop: always;

      & > * {
        flex: 0 0 calc(var(--grid-col-width) * 1px);
        max-width: 100%;
        overflow: hidden;
      }
    }
  }
  // vertical grid
  &.section-vertical {
    .section-row-outer {
      display: grid;
      grid-template-columns: repeat(auto-fit, min(var(--grid-col-width) * 1px, 100vw - (var(--grid-gap) * 2px)));
    }
  }
}
/*

section-row: fade away sides of scroller when overflowing

*/
@media (min-width: $mobile-width) {
  .fade-mask-both {
    -webkit-mask-image: linear-gradient(90deg, transparent 0%, black min(5vw, 35px), black calc(100% - min(5%, 35px)), transparent 100%);
    mask-image: linear-gradient(90deg, transparent 0%, black min(5%, 35px), black calc(100% - min(5%, 35px)), transparent 100%);
  }

  .fade-mask-right {
    -webkit-mask-image: linear-gradient(90deg, transparent 0%, black 0%, black calc(100% - min(5%, 35px)), transparent 100%);
    mask-image: linear-gradient(90deg, transparent 0%, black 0%, black calc(100% - min(5%, 35px)), transparent 100%);
  }

  .fade-mask-left {
    -webkit-mask-image: linear-gradient(90deg, transparent 0%, black min(5%, 35px), black 100%, transparent 100%);
    mask-image: linear-gradient(90deg, transparent 0%, black min(5%, 35px), black 100%, transparent 100%);
  }
}

.fade-mask-bottom {
  backdrop-filter: brightness(30%);
  -webkit-backdrop-filter: brightness(30%);
  //   -webkit-mask-image: linear-gradient(0deg, transparent 0%, black 80%, black 100%, transparent 100%);
  //   mask-image: linear-gradient(0deg, transparent 0%, black 80%, black 100%, transparent 100%);
  -webkit-mask-image: linear-gradient(0deg, transparent 0%, rgba(0, 0, 0, 0.6) 80%, black 100%);
  mask-image: linear-gradient(0deg, transparent 0%, rgba(0, 0, 0, 0.6) 80%, black 100%);
}
.fade-mask-bottom-20 {
  -webkit-mask-image: linear-gradient(to top, rgba(0, 0, 0, 0) 0px, rgba(0, 0, 0, 1) 20px);
  mask-image: linear-gradient(to top, rgba(0, 0, 0, 0) 0px, rgba(0, 0, 0, 1) 20px);

  /* Ensure no overflow beyond the faded area */
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;

  /* Optional: For images that may exceed container size */
  -webkit-mask-size: cover;
  mask-size: cover;
}
/*

custom scrollers/grids

*/
.section-promos.section-promos-top {
  --grid-col-width: 350;
  .section-header {
    margin-top: calc(var(--grid-pad) * 1px) !important;
  }
}
.section-promos:not(.section-promos-top) {
  //   padding-top: 3em;
  .section-row-outer:nth-child(1) {
    // catches promos with no header so it doesn't get too close to the row above it
    padding-top: 1em; // 4em;
  }
  --grid-col-width: 2000;
  .section-row-outer > * {
    scroll-snap-align: center;
  }
}
.section-prompts {
  --grid-col-width: 160;
  --grid-col-min: 40vw;
}
/*

Swells list

*/
.section-swells {
  --grid-col-width: #{$swell-card-width};
  --card-padding: 15px;
}
.section-swells.section-type-horizontal_portrait {
  --grid-col-width: 150;
}
@media (max-width: #{$mobile-width}) {
  .section-swells.section-type-vertical {
    --card-padding: 15px;
    --grid-col-width: 900; // fill width
  }
}
@media (max-width: #{$max-width-sections}) {
  .section-swells.section-type-vertical .section-row-outer {
    justify-content: center;
  }
}

:root {
  --swell-card-zoom: 1;
}

@media (min-width: $mobile-width) {
  .section-promos-top .nav-dots {
    display: none;
  }
}

@media (max-width: $mobile-width) {
  .section-row.section-horizontal.section-promos-top > .section-row-outer > * {
    flex: 0 0 100vw;
  }
  .section-row.section-swells .section-row-outer {
    grid-template-columns: 1fr;
  }
  .section-swells.section-type-vertical .section-row-outer .card-content {
    zoom: var(--swell-card-zoom);
  }
}
/*

authors

*/
.section-authors {
  --grid-col-width: 190;
  --grid-gap: 20;
  --grid-col-min: 50vw;
}
@media (max-width: $mobile-width) {
  .section-authors {
    --grid-col-width: 120;
  }
}
/*

other

*/
.section-swellcasts {
  --grid-col-width: 200;
}
.section-stations {
  --grid-col-width: 130;
  &.section-type-vertical {
    --grid-gap: 18;
  }
}
.section-images {
  --grid-col-width: 200;
}
/*

custom cards

*/
body[data-bs-theme='light'].card-content.card-image-fallback,
body[data-bs-theme='light'].card-content.section-swell.section-swell-portrait.card-image-fallback {
  background-color: rgba(220, 220, 220, 1) !important;
}
body[data-bs-theme='light'].card-content.section-swell,
body[data-bs-theme='light'].card-content.section-promo-top {
  background-color: rgba(250, 250, 250, 1) !important;
}

.section-station .card-image-fallback,
.section-swellcast.card-image-fallback,
.section-swell-portrait.card-image-fallback {
  .card-image {
    object-fit: contain;
  }
}

.section-swells.section-type-horizontal_portrait {
  div.section-swell.card-content {
    @extend .bg-grey;
  }
}

.section-swell-square {
  //   @extend .bg-grey-10;
  &.card-content {
    [data-component='play-button'] {
      width: 35px;
      height: 35px;
    }
  }
  .card-image {
    filter: opacity(0.2);
    // filter: blur(1px) brightness(1.5) opacity(0.15);
  }
}

// .section-swell-portrait {
//   @extend .bg-grey-10;
// }

[data-bs-theme='light'] .card-content,
[data-bs-theme='light'].card-content {
  //   background-color: rgba(0, 0, 0, 0.8) !important;
  background-color: #f3f3f3 !important;
}
[data-bs-theme='dark'] .card-content,
[data-bs-theme='dark'].card-content {
  //   background-color: rgba(255, 255, 255, 0.8) !important;
  color: #fff;
  background-color: #0a0a0a !important;
}
