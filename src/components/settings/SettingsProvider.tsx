import { useQueryClient } from '@tanstack/react-query';
import { debounce, isEqual } from 'lodash';
import { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useLocalStorage } from 'usehooks-ts';
import { APIType } from '../../api/gql.common';
import { serverSettingsKey } from '../../framework/settings/serverSettingsKey';
import { SWELL_API_PROD, SWELL_API_STAGE } from '../../framework/settings/settings';
import { useEnv } from '../../framework/useEnv';
import { IServerSettings } from '../../models/models';
import { getLocalApiUrl } from '../../utils/getLocalApiUrl';
import { useIsWidgetPath } from '../../utils/useIsWidget';
import { useSettingsData } from '../../utils/useSettingsData';
import { clearUTMCookies, DEFAULT_UTM, getInitialUTM, readUTMFromCookies, readUTMFromQuery, saveUTM, UTMParams } from '../../utils/UTM.class';
import { useColorMode } from '../common/colormode/useColorMode';
import { useDebug } from '../debug/useDebug';
import { normalizeParams } from './normalizeParams';
import { SettingsContext } from './SettingsContext';

// Settings allowed to be set through the URL search params
const allowSearchParams: (keyof Partial<IServerSettings>)[] = ['countryCode', 'debug', 'stage', 'allowPlayInCard', 'autoPlay'];

// Settings allowed to be saved in localStorage
const allowLocalParams: (keyof Partial<IServerSettings>)[] = ['countryCode', 'debug', 'stage', 'playbackRate', 'colorMode', 'volume'];

export const SettingsProvider = ({ children }: { children: ReactNode }) => {
  const { debug, prepareArgs } = useDebug('settings');
  const { userMode } = useColorMode();
  const env = useEnv();
  const queryClient = useQueryClient();
  const settings = useSettingsData();
  const [searchParams] = useSearchParams();
  const [localSettings, setLocalSettings] = useLocalStorage<Partial<IServerSettings>>(serverSettingsKey[0], {});
  const isWidget = useIsWidgetPath();
  const isLocal = env?.STAGE === 'local';
  const isStage = env?.STAGE === 'stage';
  const isProd = !isLocal && !isStage;
  const [isReady, setIsReady] = useState(false);

  const getApiUrl = useCallback(
    (stage: APIType) => {
      if (!isProd) {
        if (isLocal) {
          return getLocalApiUrl(stage);
        } else {
          return stage === 'prod' ? SWELL_API_PROD : SWELL_API_STAGE;
        }
      }
      return env?.SWELL_API;
    },
    [env?.SWELL_API, isLocal, isProd],
  );

  const apiUrl = useMemo(() => getApiUrl(settings.stage), [getApiUrl, settings.stage]);

  const update = useCallback(
    (delta: Partial<IServerSettings> = {}) => {
      const currentData = queryClient.getQueryData<IServerSettings>(serverSettingsKey) ?? ({} as IServerSettings);
      const normalizedDelta = normalizeParams(delta, [...allowLocalParams, ...allowSearchParams]);
      const cleaned = {
        ...currentData,
        ...normalizedDelta,
        // apiUrl: getApiUrl(normalizedDelta.stage ?? currentData.stage),
        isWidget,
        colorMode: userMode,
      };
      // Only update if settings have changed (excluding colorMode for comparison)
      const currentWithoutColorMode = { ...currentData, colorMode: undefined };
      const cleanedWithoutColorMode = { ...cleaned, colorMode: undefined };

      if (!isEqual(currentWithoutColorMode, cleanedWithoutColorMode)) {
        if (debug) console.log(...prepareArgs('update()', { delta, currentData, cleaned }));
        queryClient.setQueryData(serverSettingsKey, cleaned);

        // Update localSettings only for allowed params
        const localDelta = normalizeParams(cleaned, allowLocalParams);
        if (!isEqual(localDelta, localSettings)) {
          if (debug) console.log(...prepareArgs('update localSettings', { localDelta, localSettings }));
          setLocalSettings(localDelta);
        }
      }
    },
    [debug, isWidget, localSettings, prepareArgs, queryClient, setLocalSettings, userMode],
  );

  // Sync colorMode changes
  useEffect(() => {
    if (settings.colorMode !== userMode) {
      update({ colorMode: userMode });
    }
  }, [userMode, settings.colorMode, update]);

  const initialized = useRef(false);

  // Initialize settings from localData and searchData
  useEffect(() => {
    if (initialized.current) return;
    const searchData = normalizeParams(Object.fromEntries(searchParams.entries()), allowSearchParams);
    const localData = normalizeParams(localSettings, allowLocalParams);
    const delta = { ...localData, ...searchData };

    // Only update if delta introduces changes
    const currentWithoutColorMode = { ...settings, colorMode: undefined };
    const deltaWithoutColorMode = { ...delta, colorMode: undefined };
    if (!isEqual(currentWithoutColorMode, deltaWithoutColorMode)) {
      //   if (debug) console.log(...prepareArgs('init settings', { localData, searchData, delta }));
      if (debug) console.log(...prepareArgs('init settings', { local: localData?.stage ?? null, search: searchData?.stage ?? null, delta: delta?.stage ?? null }));
      update(delta);
      setIsReady(true);
      initialized.current = true;
    }
  }, [searchParams, localSettings, settings, debug, prepareArgs, update]);

  const [utmParams, setUTMParams] = useState<UTMParams>(() => getInitialUTM());

  // UTM Params
  useEffect(() => {
    let query = readUTMFromQuery(window.location.search);
    let cookies: UTMParams = {};
    const hasUTMSource = !!query['utm_source'];
    const hasUTMMedium = !!query['utm_medium'];

    if (!hasUTMSource && !hasUTMMedium) {
      query = {};
    }

    if (hasUTMSource || hasUTMMedium) {
      clearUTMCookies();
    } else {
      cookies = readUTMFromCookies();
    }

    const delta = { ...DEFAULT_UTM, ...cookies, ...query };

    setUTMParams((prev) => {
      const merged = { ...prev, ...delta };
      return isEqual(prev, merged) ? prev : merged;
    });
  }, []);

  const debouncedSaveUTM = useMemo(() => debounce((p: UTMParams) => saveUTM(p), 500), []);

  useEffect(() => {
    debouncedSaveUTM(utmParams);
    return () => debouncedSaveUTM.cancel();
  }, [utmParams, debouncedSaveUTM]);

  return <SettingsContext.Provider value={{ ...settings, isReady, utmParams, isLocal, isStage, isProd, apiUrl, update }}>{isReady ? children : null}</SettingsContext.Provider>;
};
