import { APIType, validApiTypes } from '../../api/gql.common';
import { ColorMode, IServerSettings } from '../../models/models';

const isEmpty = (val: unknown) => {
  return val == null || val === undefined || (typeof val === 'string' && val.trim() === '');
};

/**
 * Normalize and validate input parameters from query strings, cookies, etc.
 */
export function normalizeParams(input: Record<string, unknown>, allow: (keyof Partial<IServerSettings>)[]) {
  const output: Partial<IServerSettings> = {};
  const allowMap = allow.reduce((s, k) => ({ ...s, [k.toLowerCase()]: k }), {}) as Record<string, keyof IServerSettings>;
  const allowKeys = Object.keys(allowMap);
  const normalObj: Partial<Record<keyof IServerSettings, unknown>> = Object.keys(input)
    .filter((k) => allowKeys.includes(k.toLowerCase()))
    .reduce((s, k) => ({ ...s, [allowMap[k.toLowerCase()]]: input[k] }), {});

  // Validate
  if (Object.prototype.hasOwnProperty.call(normalObj, 'autoPlay') && !isEmpty(normalObj.autoPlay)) {
    output.autoPlay = normalObj.autoPlay == true || normalObj.autoPlay == '1';
  }
  if (normalObj?.playbackRate && Object.prototype.hasOwnProperty.call(normalObj, 'playbackRate') && !isEmpty(normalObj.playbackRate) && !isNaN(parseFloat(normalObj.playbackRate as string))) {
    output.playbackRate = Math.max(1, Math.min(2, parseFloat(normalObj.playbackRate as string)));
  }
  if (normalObj?.volume && Object.prototype.hasOwnProperty.call(normalObj, 'volume') && !isEmpty(normalObj.volume) && !isNaN(parseFloat(normalObj.volume as string))) {
    output.volume = Math.max(0, Math.min(1, parseFloat(normalObj.volume as string)));
  }
  if (normalObj?.countryCode && Object.prototype.hasOwnProperty.call(normalObj, 'countryCode') && !isEmpty(normalObj.countryCode) && (normalObj.countryCode as string).length === 2) {
    output.countryCode = normalObj.countryCode as string;
  }
  if (Object.prototype.hasOwnProperty.call(normalObj, 'allowPlayInCard') && !isEmpty(normalObj.allowPlayInCard)) {
    output.allowPlayInCard = normalObj.allowPlayInCard == true || normalObj.allowPlayInCard == '1';
  }
  if (Object.prototype.hasOwnProperty.call(normalObj, 'debug')) {
    output.debug = normalObj.debug as string;
  }
  if (Object.prototype.hasOwnProperty.call(normalObj, 'stage') && !isEmpty(normalObj.stage) && validApiTypes.includes(normalObj.stage as APIType)) {
    output.stage = normalObj.stage as APIType;
  }
  if (Object.prototype.hasOwnProperty.call(normalObj, 'colorMode') && !isEmpty(normalObj.colorMode) && Object.values(ColorMode).includes(normalObj.colorMode as ColorMode)) {
    output.colorMode = normalObj.colorMode as ColorMode;
  }

  return output;
}
