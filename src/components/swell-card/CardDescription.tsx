import classNames from 'classnames';
import urlJoin from 'proper-url-join';
import React, { CSSProperties, Fragment, useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { buildUrl } from '../../framework/buildUrl';
import { useActionBaseUrl } from '../../utils/useIsWidget';

const UserLink = ({ baseUrl, to, text }: { baseUrl: string; to: string; text: string }) => {
  const username = useMemo(() => {
    let result = to;
    // strip 's | ’s from end of words
    if (/['’`]s$/.test(result)) {
      result = result.replace(/['’`]s$/, '');
    }
    // remove all non-word characters from end of word
    if (/(\W)+$/.test(result)) {
      result = result.replace(/(\W)+$/, '');
    }
    return result.toLowerCase();
  }, [to]);

  if (baseUrl) {
    return (
      <a
        href={urlJoin(baseUrl, username)} //
        target='_blank'
        onClick={(e) => e.stopPropagation()}
        rel='noreferrer'
      >
        {text}
      </a>
    );
  } else {
    return (
      <Link
        to={`/${username}`} //
        onClick={(e) => e.stopPropagation()}
      >
        {text}
      </Link>
    );
  }
};

const getHashtagLink = (to: string, username: string | null = null) => {
  let result = to;
  // strip 's | ’s from end of words
  if (/['’`]s$/.test(result)) {
    result = result.replace(/['’`]s$/, '');
  }
  // remove some non-word characters from end of word ex: #test! #test"
  if (/[!?*"'}{#@$%^&*()<>,./;:\\|+=\-_]+$/.test(result)) {
    result = result.replace(/[!?*"'}{#@$%^&*()<>,./;:\\|+=\-_]+$/, '');
  }

  if (username) {
    result = `/${username}/hashtag/` + encodeURIComponent(result.slice(1));
  } else {
    result = '/search/' + encodeURIComponent(result);
  }

  return result;
};

export const HashtagLink = ({ to, text, username, className = '', title = '', style = {} }: { to: string; text: string; username?: string; className?: string; title?: string; style?: CSSProperties }) => {
  const baseUrl = useActionBaseUrl();
  const _to = getHashtagLink(to, username);

  if (baseUrl) {
    const href = buildUrl({ href: baseUrl, pathname: _to });
    return (
      <a
        href={href} //
        className={className}
        title={title}
        onClick={(e) => e.stopPropagation()}
        target='_blank'
        rel='noreferrer'
        style={style}
      >
        {text}
      </a>
    );
  }
  // NOTE: if _to contains a "%" that is not part of a encoded string - this breaks
  return (
    <Link
      to={_to} //
      className={className}
      title={title}
      onClick={(e) => e.stopPropagation()}
    >
      {text}
    </Link>
  );
};

const WebLink = ({ to, text }: { to: string; text: string }) => {
  return (
    <a href={to} target='_blank' rel='noreferrer' onClick={(e) => e.stopPropagation()}>
      {text}
    </a>
  );
};

const fixSpacing = (str: string) => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return str.replace(urlRegex, (match) => {
    // Insert a space before the matched URL
    return ' ' + match;
  });
};

export const FormatCopy = ({ text = '', username }: { text?: string | null; username?: string }) => {
  const baseUrl = useActionBaseUrl();
  const [parts, setParts] = useState<React.ReactNode[]>([]);

  useEffect(() => {
    if (text) {
      // Normalize line endings to \n and preserve newlines
      let processedText = text.replace(/\r\n|\r/g, '\n');
      processedText = fixSpacing(processedText);
      // Avoid collapsing newlines in hashtag/pipe replacements
      processedText = processedText.replace(/(^|\s)#/g, '$1 #'); // Ensure space before hashtags
      processedText = processedText.replace(/\|/g, ' | ').replace(/\s+\|\s+/g, ' | ');
      // Split text into lines, preserving empty lines
      const lines = processedText.split('\n');

      const newParts = lines.map((line, lineIndex) => {
        // Split each line into words, handle empty lines
        const words = line.trim().length > 0 ? line.split(/\s+/g) : [''];
        const wordNodes = words.map((word, wordIndex) => {
          let inner = <>{word}</>;
          if (/^#/.test(word) && word.length > 1 && !/^[0-9,]{1,}$/.test(word.slice(1))) {
            const cutAt = word.slice(1).search(/[^a-zA-Z0-9_-]/);
            if (cutAt > 1) {
              const hashtag = word.slice(0, cutAt + 1);
              const postfix = word.slice(cutAt + 1);
              inner = (
                <>
                  <HashtagLink to={hashtag} text={hashtag} username={username} />
                  {postfix}
                </>
              );
            } else {
              inner = <HashtagLink to={word} text={word} username={username} />;
            }
          } else if (/^@/.test(word) && word.length > 1) {
            inner = <UserLink baseUrl={baseUrl} to={word.slice(1)} text={word} />;
          } else if (/^http(s?):/.test(word)) {
            inner = <WebLink to={word} text={word} />;
          } else if (/^www./.test(word)) {
            inner = <WebLink to={`http://${word}`} text={word} />;
          } else if (/([a-zA-Z]{2,16}\.[a-zA-Z]{2,6})$/.test(word)) {
            inner = <WebLink to={`http://${word}`} text={word} />;
          } else if (/([a-zA-Z]{2,16}\.[a-zA-Z]{2,16}\.[a-zA-Z]{2,6})$/.test(word)) {
            inner = <WebLink to={`http://${word}`} text={word} />;
          }
          return <Fragment key={`word_${lineIndex}_${wordIndex}`}>{inner} </Fragment>;
        });
        return (
          <Fragment key={`line_${lineIndex}`}>
            {wordNodes}
            {lineIndex < lines.length - 1 && <br />}
          </Fragment>
        );
      });

      setParts(newParts);
    } else {
      setParts([]);
    }
  }, [text, username, baseUrl]);

  return parts.length > 0 ? <span data-component='formatcopy'>{parts}</span> : null;
};

interface SwellDescriptionProps {
  description?: string | null;
  username?: string | null;
  className?: string;
}

export const SwellDescription = ({ className = '', description, username, style }: SwellDescriptionProps & React.HTMLAttributes<HTMLDivElement>) => {
  if (!description) return null;
  // TODO: de we need line clamp on description?
  return (
    <div
      data-component='carddescription'
      className={classNames(className, 'fs-4')}
      style={{
        // WebkitLineClamp: 12,
        lineClamp: 12,
        WebkitBoxOrient: 'vertical',
        display: '-webkit-box',
        // overflow: 'hidden',
        wordBreak: 'break-word',
        // maxWidth: '60ch',
        whiteSpace: 'pre-wrap', // TODO: test this - added to support lines in .txt
        ...style,
      }}
    >
      <FormatCopy text={description} username={username?.toLowerCase()} />
    </div>
  );
};
