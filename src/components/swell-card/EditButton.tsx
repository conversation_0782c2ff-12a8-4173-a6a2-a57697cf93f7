import { buildUrl } from '../../framework/buildUrl';
import { SmartLink } from '../common/SmartLink';
import { SwellButton } from '../swellbutton/SwellButton';

interface IEditButtonProps {
  canonicalId: string;
  replyId?: string;
}

export const EditButton = ({ canonicalId, replyId }: IEditButtonProps) => {
  const url = buildUrl({ pathname: '/me/', searchParams: { op: 'editswell', swl_source: 'website', lid: canonicalId, ...(replyId ? { rid: replyId } : {}) } });

  return (
    <SmartLink to={url} reloadDocument={true}>
      <SwellButton.Edit />
    </SmartLink>
  );
};
