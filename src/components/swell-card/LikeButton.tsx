import { useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';

import { OpenApiSwellResponseEnhanced } from '../../api/gql.loadSwellById';
import { isLocal } from '../../framework/settings/settings';
import { OpenApiReactionResponse, ReactState } from '../../generated/graphql';
import { ReactType } from '../../models/models';
import { dataLayer } from '../../tracking/Tracking';
import { useAuth } from '../login/useAuth';
import { useSettings } from '../settings/useSettings';
import { SwellButton } from '../swellbutton/SwellButton';

function getNextPressedState(pressState: ReactState) {
  return pressState === ReactState.Notpressed ? ReactState.Pressed : ReactState.Notpressed;
}

function getNextTotalReactions(totalReactions = 0, pressState: ReactState) {
  return Math.max(0, totalReactions + (pressState === ReactState.Pressed ? 1 : -1));
}

const useReaction = (canonicalId: string, replyId: string = '', currentPressedState: ReactState, _currentTotalReactions: number) => {
  const auth = useAuth();
  const queryClient = useQueryClient();
  const settings = useSettings();

  const updateLikeData = (canonicalId: string, replyId: string = '', nextState: ReactState, nextCount: number) => {
    const queryKey = ['loadSwellById', canonicalId, settings.stage, settings.countryCode, auth.loggedIn];

    queryClient.setQueryData<OpenApiSwellResponseEnhanced>(queryKey, (data) => {
      let reactions: OpenApiReactionResponse[] = [];
      if (data) {
        if (!replyId) {
          const item = data;

          reactions = item.reactions ? [...item.reactions] : [];
          let heartIndex = reactions.findIndex((r) => r.reaction === ReactType.HEART);
          if (heartIndex === -1) {
            heartIndex = reactions.push({ reaction: ReactType.HEART, count: 0, pressState: ReactState.Notpressed }) - 1;
          }
          reactions[heartIndex] = { ...reactions[heartIndex], count: nextCount, pressState: nextState };

          return { ...data, reactions };
        } else {
          const replies = data.replies ? [...data.replies] : [];
          const replyIndex = replies.findIndex((r) => r?.id === replyId);

          if (replyIndex > -1) {
            const item = { ...replies[replyIndex] };

            reactions = item.reactions ? [...item.reactions] : [];
            let heartIndex = reactions.findIndex((r) => r.reaction === ReactType.HEART);
            if (heartIndex === -1) {
              heartIndex = reactions.push({ reaction: ReactType.HEART, count: 0, pressState: ReactState.Notpressed }) - 1;
            }
            reactions[heartIndex] = { ...reactions[heartIndex], count: nextCount, pressState: nextState };

            item.reactions = reactions;
            replies[replyIndex] = item;
            return { ...data, replies: [...replies] };
          }
        }
      }
      return data;
    });
  };

  const like = async () => {
    const nextPressedState = getNextPressedState(currentPressedState);

    // if (isLocal) await sleep(3000);

    if (window?.sw_ia) {
      const loggedIn = await window.sw_ia();

      if (loggedIn) {
        const updatedPressState = (await window.sw_react({ swellId: canonicalId, ...(replyId ? { replyId } : {}), pressedState: isLocal ? ReactState.Notpressed : undefined })) as ReactState;

        // press state failed so revert
        if (updatedPressState !== nextPressedState) {
          //   queryClient.invalidateQueries();
          return false;
        }
        // queryClient.invalidateQueries();
        return true;
      } else {
        const sp = new URLSearchParams();
        sp.set('op', 'react');
        sp.set('lid', canonicalId); // lid means "swellId" which needs to equal canonicalId, NOT swell.id. - Just accept it.
        if (replyId) sp.set('rid', replyId);

        window.location.href = `/me/?${sp}`;
      }
    } else {
      return false;
    }
  };

  return { like, updateLikeData };
};

interface ILikeMasterReplyButtonProps {
  totalReactions: number;
  canonicalId: string;
  replyId?: string;
  pressState: ReactState;
  tracking?: Record<string, string>;
}

export const LikeButton = ({ totalReactions, canonicalId, replyId, pressState, tracking = {} }: ILikeMasterReplyButtonProps) => {
  const reaction = useReaction(canonicalId, replyId, pressState, totalReactions);
  const [isLoading, setIsLoading] = useState(false);
  //   const initialPressState = pressState;
  //   const initialCount = totalReactions;
  //   const countUI = totalReactions;
  //   const pressStateUI = pressState;

  const [_count, setCount] = useState(totalReactions);
  const [_pressState, setPressState] = useState(pressState);

  const onClick: React.MouseEventHandler<HTMLElement> = async (e) => {
    e.stopPropagation();

    // track event
    dataLayer(tracking);

    // save state in case we need to revert
    // const initialPressState = pressState;
    // const initialCount = totalReactions;

    // optimistic ui
    const nextPressState = getNextPressedState(pressState);
    const nextCount = getNextTotalReactions(totalReactions, nextPressState);

    setPressState(nextPressState);
    setCount(nextCount);
    // reaction.updateLikeData(canonicalId, replyId, nextPressState, nextCount);

    // disable while call is made
    setIsLoading(true);
    const success = await reaction.like();

    if (success) {
      reaction.updateLikeData(canonicalId, replyId, nextPressState, nextCount);
    } else {
      setPressState(pressState);
      setCount(totalReactions);
    }

    // if (!success) {
    // revert optimistic UI
    // reaction.updateLikeData(canonicalId, replyId, initialPressState, initialCount);
    // }
    setIsLoading(false);
  };

  return (
    <SwellButton.Like
      onClick={onClick} //
      disabled={isLoading}
      state={_pressState}
      count={_count}
    />
  );
};
