import { OpenApiSwellResponseEnhanced } from '../../api/gql.loadSwellById';
import { LANGUAGE_CODES } from '../../framework/settings/LANGUAGE_CODES';
import { slugify } from '../../utils/Utils';
import { SmartLink } from '../common/SmartLink';
import { SwellButton } from '../swellbutton/SwellButton';

export const SwellCategories = ({ swell, className }: { swell: OpenApiSwellResponseEnhanced; className?: string }) => {
  if (!swell?.categories?.length && !swell?.languages?.length) return <div />;

  const languages =
    swell.languages?.reduce((acc, lang) => {
      if (LANGUAGE_CODES[lang] && lang !== 'en') {
        acc.push({ id: lang.toLowerCase(), name: LANGUAGE_CODES[lang] });
      }
      return acc;
    }, [] as { id: string; name: string }[]) ?? [];

  const cats = [
    ...(swell?.categories?.map((c) => ({ ...c, href: `/category/${c.id}/${slugify(c.name)}` })) ?? []), //
    ...languages.map((c) => ({ ...c, href: `/language/${c.id}/${slugify(c.name)}` })),
  ];

  return (
    <div className={className}>
      <div className='d-flex flex-wrap gap-2'>
        {cats.map((c, i) => {
          return (
            <SmartLink key={`tag_${i}`} to={c.href}>
              <SwellButton.Tag>{c.name}</SwellButton.Tag>
            </SmartLink>
          );
        })}
      </div>
    </div>
  );
};
