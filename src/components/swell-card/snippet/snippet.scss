[data-component='snippet'] {
  &::before,
  &::after {
    content: '\2026';
  }
}

.wrap-quotes {
  display: inline-block;
  position: relative;
}
.wrap-quotes::before {
  content: '\201C';
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  transform: translate(-100%);
}
.wrap-quotes::after {
  content: '\201D';
  display: inline-block;
  position: absolute;
  bottom: 0;
  right: 0;
  transform: translate(100%);
}
/*
[data-component='snippet'] {
  &::before,
  &::after {
    content: '\201C\2026';
    display: inline-block;
  }
}
.ellipsis {
  position: relative;
}

.ellipsis::before,
.ellipsis::after {
  content: '…';
  position: absolute;
  color: inherit;
}

.quotes {
  &::before {
    content: '\201C';
  }
  &::after {
    content: '\201D';
  }
}


*/
