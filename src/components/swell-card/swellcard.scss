@use 'sass:map';
@use '../../assets/css/base' as *;

.card-pad {
  padding: #{$card-padding};
}
@media (max-width: $mobile-width) {
  .card-pad {
    padding: #{$card-padding * 0.75};
  }
}

.card-pad-x {
  padding-left: #{$card-padding};
  padding-right: #{$card-padding};
}

.bg-image-colors {
  background-image: url(../../assets/images/blurry-colors.jpg);
  background-size: cover;
  background-attachment: fixed;
}

[data-bs-theme='light'] {
  [data-pressedstate='NOTPRESSED'] .like-icon {
    --icon-color: black;
    --icon-fill: transparent;
  }
}

[data-bs-theme='dark'] {
  [data-pressedstate='NOTPRESSED'] .like-icon {
    --icon-color: white;
    --icon-fill: transparent;
  }
}

[data-pressedstate='PRESSED'] .like-icon {
  --icon-color: #ee5a8f;
  --icon-fill: #ee5a8f;
}

.backdrop-blur-dark {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(100, 100, 100, 0.25);
}

.swell-card {
  @extend .rounded;
  height: fit-content;
  position: relative;
  max-width: 100%;
  min-width: var(--grid-col-width);
  width: 100%;
  box-sizing: border-box;
  border: 1px solid rgba(128, 128, 128, 0.5);

  & > a {
    z-index: 3;
  }

  & > a:hover {
    transition: background-color 0.3s;
    background-color: rgba(128, 128, 128, 0.1);
  }

  .swell-card-curtain {
    @extend .absolute-fill;
  }

  .swell-card-content-container {
    @extend .min-aspect;
    position: relative;

    .swell-card-content {
      @extend .min-aspect;
      display: flex;
      align-items: stretch;
      justify-content: center;
      text-align: center;
      overflow: auto;
    }
  }

  [data-component='snippet'] {
    @extend .text-shadow-mode, .h4, .fw-normal, .opacity-75, .line-clamp-3, .text-start;
  }
}

[data-bs-theme='light'] {
  .swell-card {
    @extend .text-black, .bg-grey-lighter;
  }
  .swell-card-curtain {
    background: rgba(255, 255, 255, 0.85);
  }
}

[data-bs-theme='dark'] {
  .swell-card {
    @extend .text-white, .bg-grey-dark;
  }
  .swell-card-curtain {
    background: rgba(0, 0, 0, 0.9);
  }
}

[data-bs-theme='light'] .backdrop-blur-mode {
  @extend .backdrop-blur-lighten;
}

[data-bs-theme='dark'] .backdrop-blur-mode {
  @extend .backdrop-blur-darken;
}

// reply button

[data-component='replybuttonui'] {
  &.bg-squash,
  &.bg-grey-30 {
    --icon-color: #000;
    --icon-fill: #000;
    color: #000;
  }
  &.bg-premium-blue {
    --icon-color: #000;
    --icon-fill: #000;
    color: #000;
  }
}

[data-bs-theme='dark'] {
  [data-component='replybuttonui'] {
    &.bg-grey-30 {
      --icon-color: #fff;
      --icon-fill: #fff;
      color: #fff;
    }
    &.bg-premium-blue {
      --icon-color: #fff;
      --icon-fill: #fff;
      color: #fff;
    }
  }
}
/*

premium star

*/
.card-premium-star {
  @extend .text-premium-blue;
  --size: 20px;
  border-bottom-left-radius: 100vw;
  border-bottom-right-radius: 100vw;
  width: calc(var(--size) + var(--size));
  height: calc(var(--size));
  display: flex;
  justify-content: center;
  align-items: center;
}
.card-premium-star svg {
  transform: translateY(-2px);
}
[data-bs-theme='light'] .card-premium-star {
  background-color: #fff;
}
[data-bs-theme='dark'] .card-premium-star {
  background-color: #000;
}
.badge-premium-star {
  @extend .text-black, .rounded-circle, .bg-premium-blue, .flex-center;
  min-width: fit-content;
  --size: 23px;
  width: var(--size);
  height: var(--size);
}
.likebutton:disabled {
  opacity: 0.5;
  cursor: wait;
}
