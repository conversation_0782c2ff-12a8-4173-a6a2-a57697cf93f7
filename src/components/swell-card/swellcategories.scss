@use '../../assets/css/base' as *;
/*

category

*/
.tag {
  @extend .text-break, .text-truncate, .fw-normal, .shadow-sm, .px-3, .rounded-pill;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  line-height: 30px;
}
[data-bs-theme='light'] .tag {
  background-color: rgba(200, 200, 200, 0.6);
  backdrop-filter: blur(5px) brightness(150%);
  -webkit-backdrop-filter: blur(5px) brightness(150%);
  color: #000;
  & > * {
    color: #000;
  }
  &:not(.disabled):hover,
  &:not(:disabled):hover {
    background-color: rgba(200, 200, 200, 1);
  }
}
[data-bs-theme='dark'] .tag {
  background-color: rgba(65, 65, 65, 0.6);
  backdrop-filter: blur(5px) brightness(80%);
  -webkit-backdrop-filter: blur(5px) brightness(80%);
  & > * {
    color: #fff;
  }
  &:not(.disabled):hover,
  &:not(:disabled):hover {
    background-color: rgba(65, 65, 65, 1);
  }
}
