import { CropPortrait } from '@mui/icons-material';
import classNames from 'classnames';
import { CSSProperties } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import { SwellIcons } from '../../assets/icons/SwellIcons';
import { useIsSwellApp } from '../../finder/FinderUtils';
import { OpenApiSwellcastSwellFacesResponse, ReactState } from '../../generated/graphql';
import { dataLayer } from '../../tracking/Tracking';
import { getLocalPath, isLocalPath } from '../../utils/isLocalUrl';
import { getPromptLink } from '../../utils/swell-utils';
import { Mugshot } from '../common/Mugshot';
import { SmartLink } from '../common/SmartLink';
import { NormalSection } from '../sections/InfiniteSectionsView';

type SwellAnchorProps = { Icon?: React.FC } & React.AnchorHTMLAttributes<HTMLAnchorElement>;
type SwellButtonProps = { Icon?: React.FC } & React.ButtonHTMLAttributes<HTMLButtonElement>;
type SwellLinkProps = { Icon?: React.FC } & LinkProps;

export const SwellButton = () => {
  return <></>;
};

export const SwellLink = () => {
  return <></>;
};

export const SwellAnchor = () => {
  return <></>;
};

//
// Base Styles
//
SwellButton.Base = ({ className, Icon, children, type = 'button', ...props }: { Icon?: React.FC } & React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  return (
    <button {...props} type={type} className={classNames(className, 'btn fs-4 d-flex gap-1')}>
      {Icon ? <Icon /> : null}
      {children}
    </button>
  );
  // fw-bold
};

SwellButton.Primary = ({ className, ...props }: SwellButtonProps) => (
  <SwellButton.Base {...props} className={classNames(className, 'btn-primary border-primary-subtle')}>
    {props?.children}
  </SwellButton.Base>
);

SwellButton.Secondary = ({ className, ...props }: SwellButtonProps) => (
  <SwellButton.Base {...props} className={classNames(className, 'btn-secondary border-secondary-subtle')}>
    {props?.children}
  </SwellButton.Base>
);

SwellButton.Blue = ({ className, ...props }: SwellButtonProps) => (
  <SwellButton.Base {...props} className={classNames(className, 'btn-premium')}>
    {props?.children}
  </SwellButton.Base>
);

const SwellLinkBase = ({ className, Icon, children, ...props }: SwellLinkProps) => {
  const isApp = useIsSwellApp();
  const reloadDocument = Object.prototype.hasOwnProperty.call(props, 'reloadDocument') ? props.reloadDocument : isApp;
  return (
    <Link {...props} className={classNames(className, 'btn fs-4 fw-bold d-flex gap-1')} reloadDocument={reloadDocument} target={isApp ? '_blank' : '_self'}>
      {Icon ? <Icon /> : null}
      {children}
    </Link>
  );
};

SwellLink.Base = SwellLinkBase;

SwellLink.Primary = ({ className, ...props }: SwellLinkProps) => (
  <SwellLink.Base {...props} className={classNames(className, 'btn-primary border-secondary-subtle')}>
    {props?.children}
  </SwellLink.Base>
);

SwellLink.Secondary = ({ className, ...props }: SwellLinkProps) => (
  <SwellLink.Base {...props} className={classNames(className, 'btn-secondary border-secondary-subtle')}>
    {props?.children}
  </SwellLink.Base>
);

SwellLink.Blue = ({ className, ...props }: SwellLinkProps) => (
  <SwellLink.Base {...props} className={classNames(className, 'btn-premium border-secondary-subtle')}>
    {props?.children}
  </SwellLink.Base>
);

const SwellAnchorBase = ({ className, Icon, children, ...props }: SwellAnchorProps) => {
  const isApp = useIsSwellApp();

  return (
    <a {...props} className={classNames(className, 'btn fs-4 fw-bold d-flex gap-1')} target={isApp ? '_blank' : '_self'}>
      {Icon ? <Icon /> : null}
      {children ? children : null}
    </a>
  );
};

SwellAnchor.Base = SwellAnchorBase;

SwellAnchor.Primary = ({ className, ...props }: SwellAnchorProps) => (
  <SwellAnchor.Base {...props} className={classNames(className, 'btn-primary')}>
    {props?.children}
  </SwellAnchor.Base>
);

SwellAnchor.Secondary = ({ className, ...props }: SwellAnchorProps) => (
  <SwellAnchor.Base {...props} className={classNames(className, 'btn-secondary')}>
    {props?.children}
  </SwellAnchor.Base>
);

SwellAnchor.Blue = ({ className, ...props }: SwellAnchorProps) => (
  <SwellAnchor.Base {...props} className={classNames(className, 'btn-premium')}>
    {props?.children}
  </SwellAnchor.Base>
);
//
// Simple buttons
//
SwellButton.Black = (props: SwellButtonProps) => <SwellButton.Base {...props} className={classNames('bg-black text-white', props.className)} style={{ '--icon-color': '#fff', ...(props?.style ?? {}) } as CSSProperties} />;

SwellButton.Info = (props: SwellButtonProps) => <SwellButton.Blue Icon={SwellIcons.QuestionMark} {...props} />;

SwellButton.Edit = (props: SwellButtonProps) => <SwellButton.Secondary Icon={SwellIcons.Edit} aria-label='edit' {...props} />;

SwellLink.Edit = (props: SwellLinkProps) => <SwellLink.Secondary Icon={SwellIcons.Edit} aria-label='edit' {...props} />;

SwellButton.Pin = (props: SwellButtonProps) => <SwellButton.Secondary Icon={SwellIcons.Pin} className='rounded-pill' style={{ pointerEvents: 'none' }} {...props} />;

SwellButton.Share = (props: SwellButtonProps) => <SwellButton.Secondary {...props} Icon={SwellIcons.Upload} style={{ width: 'min-content' }} />;

SwellButton.Premium = (props: SwellButtonProps) => <SwellButton.Blue Icon={SwellIcons.Star} title='subscription only' className='btn-sm rounded-circle' style={{ pointerEvents: 'none' }} {...props} />;

SwellButton.Panel = (props: SwellButtonProps) => <SwellButton.Primary Icon={SwellIcons.People} className='btn-sm rounded-circle' style={{ pointerEvents: 'none' }} {...props} />;

SwellButton.Unlock = (props: SwellButtonProps) => <SwellButton.Blue {...props}>{props.children}</SwellButton.Blue>;

SwellButton.Cancel = ({ label = 'Cancel', ...props }: SwellButtonProps & { label?: string }) => <SwellButton.Secondary {...props}>{label}</SwellButton.Secondary>;

SwellButton.Confirm = ({ label = 'Confirm', ...props }: SwellButtonProps & { label?: string }) => <SwellButton.Primary {...props}>{label}</SwellButton.Primary>;

SwellButton.Next = (props: SwellButtonProps) => (
  <SwellButton.Primary Icon={SwellIcons.ChevronRight} {...props}>
    Next
  </SwellButton.Primary>
);

SwellButton.NewSwell = (props: SwellButtonProps) => (
  <SwellButton.Primary Icon={SwellIcons.Mic} {...props} className={props?.className}>
    Record a new swell
  </SwellButton.Primary>
);

SwellButton.Tag = (props: SwellButtonProps) => (
  <SwellButton.Base {...props} className='btn-tag btn-secondary btn-sm'>
    {props.children}
  </SwellButton.Base>
);

SwellButton.Loading = (props: SwellButtonProps) => (
  <SwellButton.Secondary {...props} disabled={true} className='fw-normal'>
    Loading...
  </SwellButton.Secondary>
);

SwellButton.Follow = (props: SwellButtonProps) => (
  <SwellButton.Secondary Icon={SwellIcons.Add} {...props}>
    Follow
  </SwellButton.Secondary>
);

SwellButton.Following = (props: SwellButtonProps) => (
  <SwellButton.Secondary Icon={SwellIcons.Check} {...props} disabled>
    Following
  </SwellButton.Secondary>
);

SwellButton.Subscribe = (props: SwellButtonProps) => (
  <SwellButton.Blue Icon={SwellIcons.Star} {...props}>
    Subscribe
  </SwellButton.Blue>
);

SwellAnchor.Subscribe = (props: SwellAnchorProps) => (
  <SwellAnchor.Blue Icon={SwellIcons.Star} {...props}>
    Subscribe
  </SwellAnchor.Blue>
);

SwellButton.Subscribed = (props: SwellButtonProps) => (
  <SwellButton.Blue Icon={SwellIcons.Check} {...props}>
    Subscribed
  </SwellButton.Blue>
);

SwellButton.ReplyPrimary = (props: SwellButtonProps) => (
  <SwellButton.Primary Icon={SwellIcons.Mic} {...props}>
    Reply
  </SwellButton.Primary>
);

SwellButton.Ask = (props: SwellButtonProps) => (
  <SwellButton.Blue Icon={SwellIcons.Mic} {...props}>
    Ask
  </SwellButton.Blue>
);

SwellButton.Answer = (props: SwellButtonProps) => (
  <SwellButton.Blue Icon={SwellIcons.Mic} {...props}>
    Answer
  </SwellButton.Blue>
);
//
// Advanced Styles
//
SwellButton.ReplyFacesXS = ({ count = 0, faces = [], className, ...props }: { count?: number | null; faces?: OpenApiSwellcastSwellFacesResponse[] | null } & React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  return (
    <button {...props} className={classNames(className, 'btn btn-secondary btn-xs')}>
      <div className='d-flex' style={{ display: 'flex', gap: 5 }}>
        {faces?.slice(0, 4).map((face, i) => (
          <div className='d-inline-block' style={{ width: 'fit-content', marginLeft: i == 0 ? 0 : -12 }} key={`${face.alias}_face_${i}`}>
            <Mugshot size={12} image={face.url ?? ''} alt={`@${face.alias}`} thickness={0} />
          </div>
        ))}
      </div>
      <div className='opacity-75' style={{ lineHeight: 'normal' }}>
        {count} {(count ?? 0) > 1 ? 'replies' : 'reply'}
      </div>
    </button>
  );
};

SwellButton.Like = ({ state = ReactState.Notpressed, count = 0, className, disabled, ...props }: { state?: ReactState; count?: number } & SwellButtonProps) => (
  <button
    {...props} //
    aria-label='like'
    className={classNames(className, { 'disabled clickable': disabled }, 'btn btn-secondary fw-normal rounded-pill flex-center p-0')}
    data-pressstate={state}
    style={{
      //   transition: 'max-width 0.3s ease-out',
      padding: 0,
      maxWidth: count === 0 ? 34 : 100,
      overflow: 'visible',
    }}
  >
    <div className='flex-center' style={{ width: 34, height: 34, minWidth: 34 }}>
      <SwellIcons.Heart width={14} />
    </div>
    <div
      //   className='overflow-hidden'
      style={{
        marginLeft: -8,
        display: count === 0 ? 'none' : 'block',
        opacity: count === 0 ? 0 : 1,
        // transition: 'opacity 0.3s',
      }}
    >
      <div style={{ paddingRight: 10, whiteSpace: 'nowrap' }}>{count > 0 ? count : null}</div>
    </div>
  </button>
);

SwellButton.LikeXS = ({ state = ReactState.Notpressed, count = 0, className, ...props }: { state?: ReactState; count?: number } & SwellButtonProps) => (
  <button {...props} data-pressstate={state} className={classNames(className, 'btn btn-secondary btn-xs')}>
    <SwellIcons.Heart style={{ height: 9 }} />
    <div className='opacity-75' style={{ lineHeight: 'normal' }}>
      {count}
    </div>
  </button>
);

SwellButton.Prompt = ({ alias, promptId, slug, small = false, tracking, ...props }: { alias: string; promptId: string; slug: string; small?: boolean; tracking: Record<string, unknown> } & Partial<SwellLinkProps>) => {
  if (promptId) {
    const iconSize = small ? 12 : 16;
    const url = getPromptLink({ alias: alias ?? 'user', id: promptId, slug });

    const onClick = () => {
      dataLayer({
        event: 'prompt_navigate',
        promptId,
        ...tracking,
      });
    };

    return (
      <SmartLink
        to={url}
        {...props} //
        aria-label='like'
        className={classNames('btn btn-secondary fw-normal rounded-circle', { 'btn-xs': small })} //={(}'btn btn-secondary fw-normal rounded-circle'
        onClick={onClick}
      >
        <div style={{ width: 20, height: 20 }} className='flex-center'>
          <CropPortrait style={{ height: iconSize, width: iconSize }} />
        </div>
      </SmartLink>
    );
  }
  return null;
};

SwellButton.SeeAll = ({ section, className, ...props }: { section: NormalSection } & Partial<SwellLinkProps>) => {
  const seeAllUrl = section?.sectionParams?.seeAllUrl ?? null;
  // show nothing if no see-all exists

  if (!seeAllUrl) return null;

  const isInternal = isLocalPath(seeAllUrl);
  const url = isInternal ? getLocalPath(seeAllUrl) : seeAllUrl;

  const onClick = () => {
    dataLayer({
      event: 'see-all', //
      name: section?.label ?? 'none',
      type: section.sectionType,
    });
  };

  return (
    <SmartLink to={url} onClick={onClick} className={classNames('btn btn-secondary rounded-pill', className)} {...props}>
      See&nbsp;All
    </SmartLink>
  );
};
