@use 'sass:map';
@use 'sass:color';
@use '../../assets/css/base' as *;

.btn {
  @extend .fs-4, .d-flex, .gap-1, .align-items-center, .justify-content-center, .backdrop-blur;
  width: fit-content;
  line-height: 1.5; // hacky way to get text vertically centered
  --bs-btn-color: inherit;
  --bs-btn-disabled-bg: var(--bs-btn-bg);
  --bs-btn-disabled-color: var(--bs-btn-color);
  --bs-btn-border-width: 0px;
  --bs-btn-border-radius: 4px;
  --bs-btn-hover-color: var(--bs-btn-color);
  --bs-btn-active-color: var(--bs-btn-color);
  --bs-btn-active-bg: var(--bs-btn-bg);

  --icon-color: var(--bs-btn-color);
  --icon-width: 16px;
  --icon-height: 16px;

  &:not(.btn-xs) {
    min-height: 34px;
    min-width: 34px;
  }
  &:hover {
    --icon-color: var(--bs-btn-hover-color);
  }
  //   .swell-icon {
  //     // width: 16px;
  //     // height: 16px;
  //     transition: inherit;
  //   }
  &.disabled.clickable {
    pointer-events: initial;
  }
}
/*

Button Icons

*/
.btn.rounded-circle {
  --bs-btn-padding-x: 0 !important;
  --bs-btn-padding-y: 0 !important;
}
.btn .swell-icon-mic {
  --icon-fill: var(--icon-color);
}
.btn .swell-icon-mic {
  --icon-fill: var(--icon-color);
}
.btn[data-pressstate='PRESSED'] .swell-icon-heart {
  --icon-color: #ee5a8f;
  --icon-fill: #ee5a8f;
}
.btn[data-pressstate='NOTPRESSED']:hover .swell-icon-heart {
  --icon-color: #ee5a8f;
  --icon-fill: #ee5a8f;
}
/*

Primary Button

*/
.btn-primary {
  --bs-btn-font-weight: bold;
  --bs-btn-color: #000;
  --bs-btn-bg: #{map.get($theme-colors, 'squash')};
  --bs-btn-hover-bg: #{color.scale(map.get($theme-colors, 'squash'), $lightness: 20%)};
}
/*

Secondary Button

*/
.btn-secondary {
  --bs-btn-font-weight: bold;
}
[data-bs-theme='light'] .btn-secondary {
  --bs-btn-color: #000;
  --bs-btn-hover-color: var(--bs-btn-color);
  --bs-btn-bg: rgba(200, 200, 200, 0.7);
  --bs-btn-hover-bg: rgba(200, 200, 200, 0.7);
}
[data-bs-theme='dark'] .btn-secondary {
  --bs-btn-color: #fff;
  --bs-btn-hover-color: var(--bs-btn-color);
  --bs-btn-bg: rgba(60, 60, 60, 0.7);
  --bs-btn-hover-bg: rgba(60, 60, 60, 0.7);
}
/*

Premium Blue Button

*/
.btn-premium {
  --bs-btn-font-weight: bold;
  --bs-btn-bg: #{map.get($theme-colors, 'premium-blue')};
  .swell-icon-star {
    --icon-color: none;
  }
}
[data-bs-theme='light'] .btn-premium {
  --bs-btn-color: #000;
  --bs-btn-hover-bg: #{color.scale(map.get($theme-colors, 'premium-blue'), $lightness: 20%)};
  .swell-icon-star {
    --icon-fill: #000;
  }
}
[data-bs-theme='dark'] .btn-premium {
  --bs-btn-color: #fff;
  --bs-btn-hover-bg: #{color.scale(map.get($theme-colors, 'premium-blue'), $lightness: -20%)};
  .swell-icon-star {
    --icon-fill: #fff;
  }
}
/*

Link Button

*/
[data-bs-theme='light'] .btn-link {
  --bs-btn-color: #000;
}
[data-bs-theme='dark'] .btn-link {
  --bs-btn-color: #fff;
}

/*

Button SM

*/
.btn.btn-sm {
  @extend .fs-5, .d-flex, .gap-1, .align-items-center, .border-0, .backdrop-blur, .rounded-pill;
  --bs-btn-padding-x: 1em;
  --bs-btn-padding-y: 0;//0.5em;
  --bs-btn-font-weight: normal;
  --bs-btn-line-height: 1.5;
  --icon-width: 13px;
  --icon-height: 13px;
  min-height: 24px;
  min-width: 24px;
}
/*

Button XS

*/
.btn.btn-xs {
  @extend .fs-6, .d-flex, .gap-1, .align-items-center, .border-0, .backdrop-blur, .rounded-pill;
  --bs-btn-padding-x: 0.75em;
  --bs-btn-padding-y: 0.4em;
  --bs-btn-font-weight: normal;
  --icon-width: 9px;
  --icon-height: 9px;
  --bs-btn-line-height: 1.5;
}
/*

Button Tag (Category Tags)

*/
// .btn-tag {
  //   @extend .rounded-pill, .fs-4, .fw-normal, .px-3;
//   --bs-btn-color: rgba(0, 0, 0, 0.5);
// }
[data-bs-theme='light'] .btn-tag {
  --bs-btn-color: #{color.change(#000, $alpha: 75%)};
//   --bs-btn-hover-color: var(--bs-btn-color);
//   --bs-btn-bg: rgba(200, 200, 200, 0.5);
//   --bs-btn-hover-bg: rgba(200, 200, 200, 0.7);
}
[data-bs-theme='dark'] .btn-tag {
  --bs-btn-color: #{color.change(#fff, $alpha: 75%)};
//   --bs-btn-hover-color: var(--bs-btn-color);
//   --bs-btn-bg: rgba(60, 60, 60, 0.5);
//   --bs-btn-hover-bg: rgba(60, 60, 60, 0.7);
}
/*

Button Icon (Player controls)

*/
.btn-icon-white,
.btn.icon-white {
  --icon-color: #fff;
  --bs-btn-hover-color: #fff;
  --bs-btn-disabled-opacity: 0.3;
  &:hover {
    opacity: 0.8;
  }
}
