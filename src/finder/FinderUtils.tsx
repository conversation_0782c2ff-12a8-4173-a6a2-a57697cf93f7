import { useEffect, useState } from 'react';
// import { useSearchParams } from '../utils/useSearchParams';
import { useSearchParams } from 'react-router-dom';
import { inBrowser } from '../utils/Utils';
import { SwellWebviewChannelPayload, WebViewAction, WebViewActionParams } from './models';

const debug = false;

// this channel connects the document to flutter WebView
const sendToApp = (payload: SwellWebviewChannelPayload) => {
  try {
    if (debug) console.log(`window.SwellAppChannel.postMessage(${JSON.stringify(payload)})`);
    window.SwellAppChannel.postMessage(JSON.stringify(payload));
  } catch {
    if (debug) console.error('SwellAppChannel', JSON.stringify(payload));
  }
  if (!window?.SwellAppChannel) {
    console.warn('!window.SwellAppChannel not available', { payload });
  }
  return payload;
};

function hasAppChannel() {
  return inBrowser ? Object.prototype.hasOwnProperty.call(window, 'SwellAppChannel') : false;
}

export const useIsSwellApp = () => {
  const [params] = useSearchParams();
  const [isApp, setIsApp] = useState(params.get('isApp') === '1' || hasAppChannel());

  useEffect(() => {
    setIsApp(params.get('isApp') === '1' || hasAppChannel());
  }, []);

  return isApp;
};

export const useWebviewAction = (config: { id: string }) => {
  function search(searchKey: string) {
    return sendToApp({
      type: WebViewAction.NAVIGATE_TO_SEARCH,
      action: {
        type: WebViewAction.NAVIGATE_TO_SEARCH,
        params: {
          searchKey,
        } as WebViewActionParams[WebViewAction.NAVIGATE_TO_SEARCH],
      },
      id: config.id,
      key: searchKey,
      conversations: [],
      result: [],
    });
  }

  function swell(id: string) {
    return sendToApp({
      type: WebViewAction.NAVIGATE_TO_SWELL,
      action: {
        type: WebViewAction.NAVIGATE_TO_SWELL,
        params: {
          id,
        } as WebViewActionParams[WebViewAction.NAVIGATE_TO_SWELL],
      },
      id: config.id,
      key: id,
      conversations: [],
      result: [],
    });
  }

  function swellcast(id: string) {
    return sendToApp({
      type: WebViewAction.NAVIGATE_TO_SWELLCAST,
      action: {
        type: WebViewAction.NAVIGATE_TO_SWELLCAST,
        params: {
          id,
        } as WebViewActionParams[WebViewAction.NAVIGATE_TO_SWELLCAST],
      },
      id: config.id,
      key: id,
      conversations: [],
      result: [],
    });
  }

  function station(id: string) {
    return sendToApp({
      type: WebViewAction.NAVIGATE_TO_STATION,
      action: {
        type: WebViewAction.NAVIGATE_TO_STATION,
        params: {
          id,
        } as WebViewActionParams[WebViewAction.NAVIGATE_TO_STATION],
      },
      id: config.id,
      key: id,
    });
  }

  function prompt(id: string, params: WebViewActionParams[WebViewAction.NAVIGATE_TO_RECORD]) {
    return sendToApp({
      type: WebViewAction.NAVIGATE_TO_RECORD,
      action: {
        type: WebViewAction.NAVIGATE_TO_RECORD,
        params,
      },
      id: config.id,
      key: id,
    });
  }

  return { search, swell, swellcast, station, prompt };
};
