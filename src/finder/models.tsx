export enum WebViewAction {
  // extra credit
  UNKNOWN = 'unknown',
  TRACK_EVENT = 'TRACK_EVENT',
  // app actions
  NAVIGATE_TO_RECORD = 'navigateToRecord',
  NAVIGATE_TO = 'navigateTo',
  WEB_VIEW = 'webview',
  NAVIGATE_TO_SEARCH = 'navigateToSearch',
  NAVIGATE_TO_SWELL = 'navigateToSwell',
  NAVIGATE_TO_SWELLCAST = 'navigateToSwellcast',
  NAVIGATE_TO_STATION = 'navigateToStation',
  NAVIGATE_TO_USER_CONVERSATIONS = 'navigateToUserConversations',
  OPEN_SHARE = 'openShare',
  FOLLOW_SWELLCAST = 'followSwellcast',
  NAVIGATE_TO_RANDOM_SWELL = 'navigateToRandomSwell',
  LOAD_WIZARD = 'loadWizard',
  NAVIGATE_TO_PROMPT = 'navigateToPrompt',
  NAVIGATE_TO_DEEPLINK = 'navigateToDeeplink',
}

export const ALLOWED_ACTIONS = [
  WebViewAction.WEB_VIEW, //
  WebViewAction.NAVIGATE_TO_SEARCH,
  WebViewAction.NAVIGATE_TO_SWELL,
  WebViewAction.NAVIGATE_TO_SWELLCAST,
  WebViewAction.NAVIGATE_TO_STATION,
  WebViewAction.NAVIGATE_TO_PROMPT,
];

export interface WebViewActionParams {
  [WebViewAction.FOLLOW_SWELLCAST]: {
    id?: string; // swellcast id
  };
  [WebViewAction.LOAD_WIZARD]: {
    id?: string;
  };
  [WebViewAction.NAVIGATE_TO]: {
    route?: string;
  };
  [WebViewAction.NAVIGATE_TO_RANDOM_SWELL]: Record<string, unknown>;
  [WebViewAction.NAVIGATE_TO_RECORD]: {
    tag?: string; // description
    id?: string; // swellcast id
    categoryId?: string; // station id
    alias?: string; // automatic
  };
  [WebViewAction.NAVIGATE_TO_SEARCH]: {
    searchKey: string;
    categoryId: string; // don't use
  };
  [WebViewAction.NAVIGATE_TO_SWELL]: {
    id: string; // will be canonical id
    doReply: boolean;
  };
  [WebViewAction.NAVIGATE_TO_SWELLCAST]: {
    id?: string; // will be alias
  };
  [WebViewAction.NAVIGATE_TO_STATION]: {
    id?: string; // will be station id
  };
  [WebViewAction.NAVIGATE_TO_USER_CONVERSATIONS]: {
    id?: string;
  };
  [WebViewAction.OPEN_SHARE]: {
    title?: string;
    description?: string;
    url?: string;
    imageUrl?: string;
  };
  [WebViewAction.WEB_VIEW]: {
    url: string;
    title?: string;
    // channels: Array<string>;
  };
}

interface PayloadActionModel {
  type: WebViewAction;
  label?: string;
  referrerId?: string;
  params: unknown;
}

interface conversation {
  id: string;
  title: string;
}

export interface SwellWebviewChannelPayload {
  type: WebViewAction;
  action?: PayloadActionModel;
  id: string;
  key: string;
  conversations?: Array<conversation>;
  result?: Array<string>;
}
