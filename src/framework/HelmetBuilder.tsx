import { MetaHTMLAttributes } from 'react';
import { He<PERSON><PERSON> } from 'react-helmet-async';
import favicon16 from '../../public/img/favicon-16x16.png';
import favicon32 from '../../public/img/favicon-32x32.png';
import { ThemeColors } from '../assets/css/ThemeColors';
import SwellLogoAMP from '../assets/images/swell-logo-amp.png';
import touchIcon from '../assets/images/swell-touch-icon.png';
import { OGModel } from '../models/models';
import { buildUrl } from './buildUrl';
import { DEFAULT_OG } from './settings/DEFAULT_OG';
import { fbAppId, fbAppId_stage, iOSId } from './settings/settings';
import { useEnv } from './useEnv';

// NOTE: This is done solely to convince webpack to recognize this asset. It is only used in the server render so it gets tree-shook otherwise
// eslint-disable-next-line @typescript-eslint/no-unused-expressions
SwellLogoAMP;

interface DefaultMeta extends OGModel {
  twitter_name?: string;
  meta?: Array<MetaHTMLAttributes<HTMLMetaElement>>;
  schema?: Record<string, unknown>;
  fbAppId?: string;
}

const DEFAULT_META_TAGS: DefaultMeta = {
  ...DEFAULT_OG,
  keywords: 'swellcast,podcast,audio,voices,social,conversation,audience,listeners,radio,listen,talk,speak,perspective',
  twitter_name: '@swell_talk',
  meta: [],
};

// NOTE: twitter:image:alt breaks iMessage OG - leave it out

export const HelmetBuilder = (props: DefaultMeta) => {
  const env = useEnv();
  const info: DefaultMeta = { ...DEFAULT_META_TAGS, ...props };
  const canonicalPath = buildUrl({ host: env.CANONICAL_DOMAIN, protocol: 'https:', pathname: info.canonicalPath });
  const canonicalPathAlt = buildUrl({ host: env.CANONICAL_ALT_DOMAIN, protocol: 'https:', pathname: info.canonicalPath });
  const description = info?.description ?? undefined;
  info.fbAppId = env.STAGE === 'local' || env.STAGE === 'stage' ? fbAppId_stage : fbAppId;

  return (
    <>
      <Helmet>
        <meta charSet='utf-8' />
        <title>{info.title}</title>
        <link rel='stylesheet' href='https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap' />
        <link rel='stylesheet' href='https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap' />
        <script src='https://www.google.com/recaptcha/api.js?render=explicit' async defer></script>
        <meta name='viewport' content='width=device-width, initial-scale=1' />
        <meta name='theme-color' content={ThemeColors.primary} />
        <meta property='keywords' content={info.keywords} />
        <link rel='manifest' href='/manifest.json' crossOrigin='use-credentials' />
        <link rel='shortcut icon' href='/favicon.ico' />
        <link rel='icon' type='image/png' href={favicon32} />
        <link rel='icon' type='image/png' href={favicon32} sizes='32x32' />
        <link rel='icon' type='image/png' href={favicon16} sizes='16x16' />
        <link key='canonical' rel='canonical' href={canonicalPath} />
        <link key='alternate' rel='alternate' href={canonicalPathAlt} />
        {/* <link rel='preload' href={PlaceholderImage} as='image' /> */}

        <meta property='title' content={info.title} />
        <meta property='description' content={description} />

        {/* <meta property='og:type' content='website' /> */}
        <meta property='og:type' content='article' />
        <meta property='og:url' content={canonicalPath} />
        <meta property='og:title' content={info.title} />
        <meta property='og:description' content={description} />
        <meta property='og:site_name' content='swellcast.com' />
        <meta property='og:image' content={info.image} />
        <meta property='og:image:secure_url' content={info.image} />

        <meta name='twitter:card' content='summary_large_image' />
        <meta name='twitter:url' content={canonicalPath} />
        <meta name='twitter:site' content={info.twitter_name} />
        <meta name='twitter:creator' content={info.twitter_name} />
        <meta name='twitter:title' content={info.title} />
        <meta name='twitter:description' content={description} />
        <meta name='twitter:image' content={info.image} />

        <meta property='fb:app_id' content={info.fbAppId} />

        <meta name='apple-itunes-app' content={`app-id=${iOSId}`} />
        <link rel='apple-touch-icon' href={touchIcon}></link>
        {props?.schema ? <script type='application/ld+json'>{JSON.stringify(props.schema, null, 2)}</script> : null}
      </Helmet>
    </>
  );
};
