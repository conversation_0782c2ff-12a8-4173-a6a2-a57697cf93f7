import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useBlocker, useLocation } from 'react-router-dom';

import { useCurrentSwell } from '../api/gql.loadSwellById';
import { getSignS3 } from '../api/gql.signS3';
import { CircleLoader } from '../components/common/circleloader/CircleLoader';
import { IPromptInfo } from '../components/common/PromptCard';
import { useDebug } from '../components/debug/useDebug';
import { AudioQuery, AudioReadyState } from '../components/player/AudioPlayer/IAudioPlayer';
import { useAudioController } from '../components/player/AudioPlayer/useAudioController';
import { useAudioData } from '../components/player/AudioPlayer/useAudioData';
import { useAudioTrack } from '../components/player/AudioPlayer/useAudioTrack';
import { IRecordingOutput } from '../components/player/AudioRecorder/AudioRecorderContext';
import { ExitRecordMessage } from '../components/player/AudioRecorder/popups/ExitRecordMessage';
import { HasSavedMessage } from '../components/player/AudioRecorder/popups/HasSavedMessage';
import { ProblemSavingEmptyMessage } from '../components/player/AudioRecorder/popups/ProblemSavingEmptyMessage';
import { UploadMessage } from '../components/player/AudioRecorder/popups/UploadMessage';
import { useRecorder } from '../components/player/AudioRecorder/useRecorder';
import { explodeMimeType } from '../components/player/AudioRecorder/utils/mimeToExtension';
import { usePopup } from '../components/popup/usePopup';
import { useSettings } from '../components/settings/useSettings';
import { UploadFileResponse } from '../generated/graphql';
import { AudioMode, PlayerStatus, UploadStatus } from '../models/models';
import { sleep } from '../utils/sleep';
import { isTextSwell } from '../utils/swell-utils';
import { useConfirm } from '../utils/useConfirm';
import { useIsOnline } from '../utils/useIsOnline';
import { isRecordMode } from './isRecordMode';
import { OrchestrationContext } from './OrchestrationContext';
import { useAudioMode } from './useAudioMode';
import { useStashId } from './useStashId';

export interface IOrchestrationContext {
  blockNavigation: boolean;
  canNavigate(ctx?: string): Promise<boolean>;
  completeRecording(): Promise<void>;
  completeTextResponse(text: string, type: AudioMode): Promise<void>;
  togglePlay(delta: Partial<AudioQuery>, togglePlay?: boolean): void;
  open(delta: { mode: AudioMode; payload?: unknown; maxDuration?: number }): void;
  close(force?: boolean): void;
  isOpen: boolean;
  hasOpened: boolean;
  uploadStatus: UploadStatus;
  errMsg: string;
  setMode(mode: AudioMode): Promise<boolean>;
  payload: { prompt?: IPromptInfo; swellId?: string; swellcastAlias?: string } & { [key: string]: string };
  isRecording: boolean; // convenience flag
  isUploading: boolean;
  stashId: string | null;
}

const MAX_UPLOAD_TIME_IN_MS = 1000 * 60 * 3;

export const OrchestrationProvider = ({ children }: { children: React.ReactNode }) => {
  const { debug, prepareArgs } = useDebug('orch');
  const { debug: debugUpload } = useDebug('upload');
  const { debug: debugStash } = useDebug('stash');
  const stashId = useStashId();
  const rec = useRecorder();
  const player = useAudioController();
  const track = useAudioTrack();
  const settings = useSettings();
  const isLocal = settings.isLocal;
  const swell = useCurrentSwell();
  const { showConfirm } = useConfirm();
  const { audioMode, setAudioMode, audioQuery, setAudioQuery, trackingData } = useAudioMode();
  const loc = useLocation();
  const isOnline = useIsOnline();
  const popup = usePopup();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [hasOpened, setHasOpened] = useState<boolean>(false);
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>(UploadStatus.NONE);
  const [errMsg, setErrMsg] = useState<string>('');
  const [startUploadTimeout, setStartUploadTimeout] = useState(0);
  const [payload, setPayload] = useState<IOrchestrationContext['payload']>({});

  const uploadAbort = useRef(new AbortController());

  const isRecording = isOpen && isRecordMode(audioMode);
  const hasRecording = isRecording && rec.hasData; //  TODO: this should work now that rec.hasData is true on start record
  const isUploading = uploadStatus === UploadStatus.UPLOADING;
  const blockNavigation = hasRecording;
  const canNavigatePending = useRef(false);

  const canNavigate = async (context = 'UNKNOWN') => {
    if (canNavigatePending.current) return false;
    canNavigatePending.current = true;
    // consolidate the logic for navigating away from record state
    if (debug) console.log(...prepareArgs('canNavigate', { context, stashId, blockNavigation }));
    if (blockNavigation) {
      if (rec.isRecording) {
        await rec.pauseRecording();
      }
      const confirmed = await showConfirm(<ExitRecordMessage context={`${context}=>Orch::canNavigate()`} />, { confirmLabel: 'Got it!' });
      if (confirmed) {
        try {
          if (debug) console.log(...prepareArgs('stash 4'));
          await rec.stash(stashId);
        } catch {
          console.error('canNavigate::stash failed');
        }
      }
      if (debug) console.log(...prepareArgs('canNavigate::confirmed', confirmed));
      canNavigatePending.current = false;
      return confirmed;
    }
    canNavigatePending.current = false;
    return true;
  };

  const audioData = useAudioData();
  const isText = useMemo(() => (audioData ? isTextSwell(audioData) : false), [audioData]);

  const close = useCallback(
    async (force = false) => {
      if (debug) console.log(...prepareArgs('OrchestrationProvider.close()', { isOpen, isRecording, hasRecording, force, hasData: rec.hasData }));
      if (isOpen) {
        // Pause track playback first
        track.pause();

        // Handle recording state
        if (isRecordMode(audioMode) && rec.hasData) {
          if (debug) console.log(...prepareArgs('handle recorded stuff'));
          // Make sure recording is paused
          if (rec.isRecording) {
            await rec.pauseRecording();
          }

          // Stash the recording before proceeding
          try {
            if (debug) console.log(...prepareArgs('close(): stashing recording', { stashId }));
            await rec.stash(stashId);
          } catch (err) {
            console.error('close(): stash failed', err);
          }
        }

        // Handle navigation confirmation if not forced
        if (!force) {
          try {
            const confirmed = await canNavigate('Orch:close()');
            if (!confirmed) {
              if (debug) console.log(...prepareArgs('close(): navigation cancelled'));
              return;
            }
          } catch (err) {
            console.error('close()', err);
          }
        }

        // Finally close the player
        if (debug) console.log(...prepareArgs('close(): setting isOpen to false'));
        setIsOpen(false);
      }
    },
    [isOpen, hasRecording, rec.hasData, audioMode, stashId, debug, track, rec, canNavigate],
  );

  const open = async (delta: { mode: AudioMode; payload?: IOrchestrationContext['payload']; maxDuration?: number }) => {
    if (debug) console.log(...prepareArgs('OrchestrationProvider.open()', { delta, audioMode }));
    setPayload(delta?.payload ?? {});

    if (isRecordMode(delta.mode)) {
      if (delta?.maxDuration) {
        rec.setMaxDuration(delta.maxDuration);
      } else {
        rec.setMaxDuration(5 * 60);
      }
    }

    const result = await _setMode(delta.mode);
    setIsOpen(true);
    return result;
  };

  //   const [textResponse, setTextResponse ] = useState('');

  const completeTextResponse = async (text: string, type: AudioMode) => {
    if (debug) console.log(...prepareArgs('completeTextResponse', { isOnline, text }));

    let status: UploadStatus = UploadStatus.NONE;

    // nothing to save
    if (text.length == 0) {
      status = UploadStatus.EMPTY;
    }

    // not online
    if (!isOnline) {
      status = UploadStatus.OFFLINE_ERR;
    }

    // external js not available
    if (!window?.sw_cw) {
      status = UploadStatus.WEB_ERR;
    }

    if (status !== UploadStatus.NONE) {
      setUploadStatus(status);
      popup.showPopup(<UploadMessage status={status} />);
      return;
    }

    popup.showPopup(<CircleLoader />, false);
    await sleep(100);
    setUploadStatus(UploadStatus.UPLOADING);

    const output = {
      file: new File([text], 'my-text-file.txt', { type: 'text/plain; charset=utf-8' }),
    };

    // if (!window?.sw_cw) {
    //   setUploadStatus(UploadStatus.WEB_ERR);
    //   return;
    // }

    try {
      // verify our swellweb.js has loaded
      // if (isLocal) await sleep(3);
      // compress wave
      // const wave = await window.sw_cw(output.wave);

      uploadAbort.current = new AbortController();
      setErrMsg('');
      //   setUploadStatus(UploadStatus.UPLOADING);

      const onCatch = (e: Error, context: string): null => {
        if (debug) console.log(...prepareArgs('onCatch', context, e));
        setStartUploadTimeout(0);
        setErrMsg('');
        if (e.name === 'AbortError') {
          setUploadStatus(UploadStatus.TIMEOUT_ERR);
        } else if (e.name == 'TypeError') {
          setUploadStatus(UploadStatus.WEB_ERR);
        } else {
          setUploadStatus(UploadStatus.WEB_ERR);
          throw e;
        }
        return null;
      };

      // get emphmeral S3 bucket to upload file
      // const { channel, filetype } = explodeMimeType(output.file.type);
      // const fileType = `${channel}/${filetype}`; // need to remove codec from type for getSignS3
      // if (debug) console.log({ filePath: output.file.name, fileType: output.file.type, realFileType: output.file.type, swellcastId: trackingData.swellcastId }); //:output.file.type, '=>', fileType});

      let aws: UploadFileResponse | null = null;

      try {
        aws = await getSignS3(
          {
            filePath: output.file.name, //
            fileType: output.file.type,
            swellcastId: trackingData.swellcastId,
          },
          { ...settings, signal: uploadAbort.current.signal },
        );
      } catch (err) {
        popup.close();
        onCatch(err as Error, 's3');
      }

      //   const aws = await getSignS3(
      //     {
      //       filePath: output.file.name, //
      //       fileType: output.file.type,
      //       swellcastId: trackingData.swellcastId,
      //     },
      //     { ...settings, signal: uploadAbort.current.signal },
      //   ).catch((e) => onCatch(e, 's3'));

      //   if (debug) console.log({ getSignS3: aws });

      if (!aws || aws?.code !== 200) {
        // S3 data failed - we can't continue
        popup.close();
        setUploadStatus(UploadStatus.SERVER_ERR);
        setErrMsg(aws?.message ?? 'Error');
        return;
      }

      //   const endpointUrl = aws?.endpointUrl ?? '';
      const formData = new FormData();
      aws?.params?.forEach((p) => formData.append(String(p.key), String(p.value)));
      formData.append('file', output.file, output.file.name);

      let response: Response | null = null;

      if (isLocal) {
        response = { ok: true } as Response;
      } else {
        try {
          response = await fetch(aws.endpointUrl!, { method: 'POST', body: formData, signal: uploadAbort.current.signal });
        } catch (err) {
          onCatch(err as Error, 'upload');
          setUploadStatus(UploadStatus.SERVER_ERR);
        }
      }
      //   if (debug) console.log('upload to', endpointUrl);

      //
      // TEST: uncomment to test failed response
      //   response = { ok: false } as Response;

      if (response?.ok !== true) {
        // upload failed - bail out
        if (debug) console.log(...prepareArgs('upload failed', { response }));
        setUploadStatus(UploadStatus.SERVER_ERR);
        return;
      }

      // success - time to redirect to /me
      setUploadStatus(UploadStatus.COMPLETED);
      isUploadComplete.current = true;

      //   const type = audioMode === AudioMode.RECORD_PROMPT_RESPONSE ? AudioMode.RECORD_NEW : audioMode;
      const params = new URLSearchParams();
      params.set('op', 'postaudio');
      params.set('type', type);
      params.set('token', aws?.encryptedUrl ?? '');
      params.set('wave', '[]');
      params.set('duration', (rec.duration * 1000).toFixed(0));
      params.set('assetid', aws?.canonicalId ?? '');

      if (payload) {
        for (const key in payload) {
          params.set(key, payload[key]);
        }
      }

      if (swell?.data?.id) {
        // if we have an associated id send it
        params.set('lid', swell.data?.canonicalId ?? '');
      }

      if (window.location.search) {
        // params from url in case of prompt do/record page - nned to transfer these here
        const inputParams = new URLSearchParams(window.location.search);
        for (const [key, val] of inputParams) {
          params.set(key, val);
        }
      }

      if (debugUpload) {
        // && confirm('DEBUG: check url?')) {
        const p = Object.fromEntries(params.entries());
        console.log(...prepareArgs({ searchParams: { ...p, params: JSON.parse(p.params) } }));

        if (!confirm('Continue to submit?')) {
          setUploadStatus(UploadStatus.NONE);
          popup.close();
          return;
        }
      }

      // SWEB-177: fox safari browser back shows a cached version of the last page state.
      //   rec.resetRecording(true); // maybe we don't need to reset
      //   setIsOpen(false);
      setUploadStatus(UploadStatus.NONE);
      await sleep(100); // this allows the browser (safari) to have a back state with the player closed. Safari!!!
      window.location.href = '/me/?' + params;
    } catch (err) {
      console.log(...prepareArgs('OrchestrationProvider::uploadRecording() failed', err));
      setUploadStatus(UploadStatus.WEB_ERR);
    }
  };

  const completeRecording = async () => {
    if (!rec.hasData || rec.isRecording) {
      //   alert('Something went wrong. If you see this, let Dev know please!');
      setUploadStatus(UploadStatus.WEB_ERR);
      if (debug) console.log(...prepareArgs({ hasData: rec.hasData, isRecording: rec.isRecording }));
      return;
    }
    if (!isOnline) {
      setUploadStatus(UploadStatus.OFFLINE_ERR);
      popup.showPopup(<UploadMessage status={UploadStatus.OFFLINE_ERR} />);
      return;
    }
    setUploadStatus(UploadStatus.UPLOADING);
    const output = await rec.completeRecording().catch((err) => {
      if (debug) console.log(...prepareArgs('completeRecording error', err));
      //   return [];
      setUploadStatus(UploadStatus.WEB_ERR);
      return null;
    });

    rec.disconnect();

    if (!output) {
      return;
    }
    if (debug) console.log(...prepareArgs({ output }));
    if (!output) return;

    const forceError = debugUpload && confirm('DEBUG: Force 0 bytes error?');

    if (forceError || output.file.size === 0) {
      popup.showPopup(<ProblemSavingEmptyMessage />);
      rec.resetRecording(true);
    }
    output.payload = payload;
    uploadRecording(output);
  };

  // we need this to close the player before redirecting because @!#?@! Safari caches browser back states - Die Safari
  const isUploadComplete = useRef(false);

  const uploadRecording = async (output: IRecordingOutput) => {
    try {
      // verify our swellweb.js has loaded
      if (!window?.sw_cw) {
        setUploadStatus(UploadStatus.WEB_ERR);
        return;
      }
      if (isLocal) await sleep(3);
      // compress wave
      const wave = await window.sw_cw(output.wave);

      uploadAbort.current = new AbortController();
      setErrMsg('');
      setUploadStatus(UploadStatus.UPLOADING);

      const onCatch = (e: Error, context: string): null => {
        if (debug) console.log(...prepareArgs('onCatch', context, e));
        setStartUploadTimeout(0);
        setErrMsg('');
        if (e.name === 'AbortError') {
          setUploadStatus(UploadStatus.TIMEOUT_ERR);
        } else if (e.name == 'TypeError') {
          setUploadStatus(UploadStatus.WEB_ERR);
        } else {
          setUploadStatus(UploadStatus.WEB_ERR);
          throw e;
        }
        return null;
      };

      // get emphmeral S3 bucket to upload file
      const { channel, filetype } = explodeMimeType(output.file.type);
      const fileType = `${channel}/${filetype}`; // need to remove codec from type for getSignS3
      if (debug) console.log(...prepareArgs({ filePath: output.file.name, fileType, realFileType: output.file.type, swellcastId: trackingData.swellcastId })); //:output.file.type, '=>', fileType});
      const aws = await getSignS3({ filePath: output.file.name, fileType, swellcastId: trackingData.swellcastId }, { ...settings, signal: uploadAbort.current.signal }).catch((e) => onCatch(e, 's3'));

      if (debug) console.log(...prepareArgs({ getSignS3: aws }));

      if (aws?.code !== 200) {
        // S3 data failed - we can't continue
        setUploadStatus(UploadStatus.SERVER_ERR);
        setErrMsg(aws?.message ?? 'Error');
        return;
      }

      const endpointUrl = aws?.endpointUrl ?? '';
      const formData = new FormData();
      aws?.params?.forEach((p) => formData.append(String(p.key), String(p.value)));
      formData.append('file', output.file, output.file.name);
      if (debug) console.log(...prepareArgs('upload to', endpointUrl));
      let response = isLocal ? ({ ok: true } as Response) : await fetch(endpointUrl, { method: 'POST', body: formData, signal: uploadAbort.current.signal }).catch((e) => onCatch(e, 'upload'));

      //
      // DEBUG TEST
      //
      if (debugUpload && confirm('DEBUG: kill upload?')) {
        response = { ok: false } as Response;
      }

      if (response?.ok !== true) {
        // upload failed - bail out
        if (debug) console.log(...prepareArgs('upload failed', { response }));
        setUploadStatus(UploadStatus.SERVER_ERR);
        return;
      }

      // success - time to redirect to /me
      setUploadStatus(UploadStatus.COMPLETED);
      isUploadComplete.current = true;

      const type = audioMode === AudioMode.RECORD_PROMPT_RESPONSE ? AudioMode.RECORD_NEW : audioMode;
      const params = new URLSearchParams();
      params.set('op', 'postaudio');
      params.set('type', type);
      params.set('token', aws?.encryptedUrl ?? '');
      params.set('wave', JSON.stringify(wave));
      params.set('duration', (rec.duration * 1000).toFixed(0));
      params.set('assetid', aws?.canonicalId ?? '');

      if (payload) {
        for (const key in payload) {
          params.set(key, payload[key]);
        }
      }

      if (swell?.data?.id) {
        // if we have an associated id send it
        params.set('lid', swell.data?.canonicalId ?? '');
      }

      if (window.location.search) {
        // params from url in case of prompt do/record page - nned to transfer these here
        const inputParams = new URLSearchParams(window.location.search);
        for (const [key, val] of inputParams) {
          params.set(key, val);
        }
      }

      if (debugUpload && confirm('DEBUG: check url?')) {
        console.log(...prepareArgs('/me/?' + params));
        return;
      }

      // SWEB-177: fox safari browser back shows a cached version of the last page state.
      rec.resetRecording(true); // maybe we don't need to reset
      setIsOpen(false);
      setUploadStatus(UploadStatus.NONE);
      await sleep(100); // this allows the browser (safari) to have a back state with the player closed. Safari!!!

      window.location.href = '/me/?' + params;
    } catch (err) {
      console.log(...prepareArgs('OrchestrationProvider::uploadRecording() failed', err));
      setUploadStatus(UploadStatus.WEB_ERR);
    }
  };

  // TODO: we can probably simplify this muckery
  const togglePlay = async (delta: Partial<AudioQuery>, doTogglePlay = true) => {
    if (debug) console.log(...prepareArgs('togglePlay()', { delta, doTogglePlay }));
    const confirmed = await _setMode(AudioMode.PLAY);

    if (confirmed) {
      const isSamePlaylist = audioQuery.playlistId === delta?.playlistId;
      const isSameTrack = audioQuery.trackId === delta?.trackId;
      const trackNotSet = (delta?.trackId ?? '') === '';

      if (!isSamePlaylist) {
        setIsOpen(true);
        track.pause();
        setAudioQuery(delta);
        return;
      }

      if (isOpen) {
        if (isSamePlaylist && (isSameTrack || trackNotSet)) {
          // do nothing - maintain state
          return;
        }
        if (delta) {
          if (isSamePlaylist) {
            // same playlist
            if (!delta?.trackId) {
              track.play();
            } else {
              setAudioQuery(delta);
            }
          } else {
            if (isSamePlaylist && isSameTrack) {
              if (doTogglePlay) {
                track.togglePlay();
              } else {
                track.play();
              }
            } else {
              setAudioQuery(delta);
            }
          }
        } else {
          if (doTogglePlay) {
            track.togglePlay();
          } else {
            track.play();
          }
        }
      } else {
        setIsOpen(true);
        setAudioQuery(delta);
        track.play();
      }
    }
  };

  const lastModePath = useRef('');

  const _setMode = async (newMode: AudioMode) => {
    if (debug) console.log(...prepareArgs('_setMode()', { newMode, isError: swell.isError })); //, new_isRecordMode: isRecordMode(newMode), current_isRecordMode: isRecordMode(audioMode), audioMode, hasRecording });

    // no change needed
    if (newMode === audioMode && isOpen) {
      return true;
    }
    // bullocks! This prevents double confirming between react-router <Prompt /> and the confirm right below (1 star)
    const skipConfirm = loc.pathname !== lastModePath.current;
    if (skipConfirm) {
      lastModePath.current = loc.pathname;
    }
    if (debug) console.log(...prepareArgs('_setMode()', { newMode, audioMode, skipConfirm }));

    // dash is closed - no checks needed
    if (!isOpen) {
      if (isRecordMode(newMode)) {
        rec.resetRecording(true);
      }
      setIsOpen(true);
      setAudioMode(newMode);
      return true;
    }

    // switching play to record
    if (audioMode == AudioMode.PLAY && isRecordMode(newMode)) {
      track.pause();
      setIsOpen(true);
      setAudioMode(newMode);
      return true;
    }

    // switching from record to play
    if (isRecordMode(audioMode) && newMode == AudioMode.PLAY) {
      const confirmed = await canNavigate('Orch::_setMode()');
      if (confirmed) {
        setAudioMode(newMode);
        return true;
      } else {
        return false;
      }
    }

    // check if has/is recording
    // if (hasRecording) {
    //   rec.pauseRecording();
    //   const confirmed = skipConfirm ? true : await showConfirm(<ExitRecordMessage />);
    //   if (confirmed) {
    //     setAudioMode(newMode);
    //   } else {
    //     return false;
    //   }
    // } else {
    //   setAudioMode(newMode);
    // }
    // return true;
    return false;
  };

  // on mount
  useEffect(() => {
    setUploadStatus(UploadStatus.NONE);
  }, []);

  //   const onPageHide = () => {
  //     if (isRecording) {
  //       rec.pauseRecording();
  //     }
  //   };

  //   useEffect(() => {
  //     window.addEventListener('pagehide', onPageHide);
  //     return () => {
  //       window.removeEventListener('pagehide', onPageHide);
  //     };
  //   }, []);

  useEffect(() => {
    // if (debug) console.log({ isOnline });
    if (uploadStatus === UploadStatus.UPLOADING) {
      if (debug) console.log(...prepareArgs('Network dropped while uploading'));
    }
  }, [isOnline]);

  // bug where track is playing but player is not open
  useEffect(() => {
    if (track.isPlaying && !isOpen) {
      console.log(...prepareArgs('Found the play while closed issue!!!'));
      open({ mode: AudioMode.PLAY });
    }
  }, [track.isPlaying]);

  // make sure player is open if recording is active
  useEffect(() => {
    if (rec.isRecording && (!isOpen || !isRecordMode(audioMode))) {
      open({ mode: isRecordMode(audioMode) ? audioMode : AudioMode.RECORD_NEW });
    }
  }, [rec.isRecording]);

  // correct issue where the player shows but is not playable TODO: Is this needed?
  useEffect(() => {
    if (isOpen && audioMode === AudioMode.PLAY && track.shouldPlay && !track.isLoading && track.readyState === AudioReadyState.HAVE_NOTHING && !track.isPlaying) {
      if (debug) console.log(...prepareArgs('OrchestrationProvider::Found the non playable issue!!! Let Jason know'));
      togglePlay({}, false); // required to autoplay on load
      //   setIsOpen(false);
    }
  }, [track.status]);

  // close player when playlist is complete
  useEffect(() => {
    if (track.isComplete && !player.hasNext) {
      setIsOpen(false);
    }
  }, [track.isComplete]);

  // upload timeout - we need to be able to kill this when necessary
  useEffect(() => {
    if (startUploadTimeout > 0) {
      const timer = setTimeout(() => {
        uploadAbort.current.abort();
        setUploadStatus(UploadStatus.TIMEOUT_ERR);
      }, MAX_UPLOAD_TIME_IN_MS);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [startUploadTimeout]);

  // watch upload status to determine the abort timeout status
  useEffect(() => {
    switch (uploadStatus) {
      case UploadStatus.NONE:
      case UploadStatus.COMPLETED:
      case UploadStatus.SERVER_ERR:
      case UploadStatus.TIMEOUT_ERR:
        setStartUploadTimeout(0); // kill timer
        break;
      case UploadStatus.UPLOADING:
        setStartUploadTimeout(Date.now()); // start time
        break;
    }
  }, [uploadStatus]);

  // if player is closed in an error state, revert to NONE to clear the error
  useEffect(() => {
    if (!isOpen) {
      setUploadStatus(UploadStatus.NONE);
      setPayload({});
      canNavigate().then(() => {
        rec.disconnect(); // will this work?
      });
    } else {
      if (!hasOpened) {
        setHasOpened(true);
      }
      if (isRecordMode(audioMode)) {
        rec?.soundCheck?.();
      }
    }
  }, [isOpen, audioMode]);

  // recover stashed audio
  useEffect(() => {
    if (debug) console.log(...prepareArgs('Orchestration::useEffect([isOpen, audioMode, loc.pathname])', { 'rec.status': PlayerStatus[rec.status], isOpen, isRecordMode: isRecordMode(audioMode), stashId }));
    if (isOpen && isRecordMode(audioMode) && loc.pathname) {
      rec.hasStash(stashId).then((found) => {
        if (found) {
          popup.showPopup(<HasSavedMessage rec={rec} stashId={stashId} />);
        }
      });
    }
  }, [isOpen, audioMode, loc.pathname]);

  const onPageHide = (e: PageTransitionEvent) => {
    if (debug) console.log(...prepareArgs('onPageHide', { persisted: e.persisted }));
    // if the page is going into the BF‑cache we still want to stash,
    // because the user might close the tab from that state.
    if (e.persisted) {
      //   if (debug) console.log('stash 2');
      rec.stash(stashId);
    }
  };

  const savedChunksLength = useRef(0);

  useEffect(() => {
    if (savedChunksLength.current !== rec.chunksLength) {
      savedChunksLength.current = rec.chunksLength;
      if (debugStash) console.log(...prepareArgs('stash:useEffect'));
      rec.stash(stashId);
    }
  }, [rec.chunksLength]);

  const handleVisibilityChange = () => {
    if (debug) console.log(...prepareArgs('handleVisibilityChange()'));
    if (document.visibilityState === 'hidden') {
      if (debugStash) console.log(...prepareArgs('stash::handleVisibilityChange'));
      rec.stash(stashId);
    }
  };

  const handleFocus = async (_e: Event) => {
    // console.log('handleFocus', { type: e.type });
    //   if (isError && !isProcessing) soundCheck();
  };

  useEffect(() => {
    window.addEventListener('pagehide', onPageHide);
    window.addEventListener('visibilitychange', handleVisibilityChange, false);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleFocus);
    return () => {
      window.removeEventListener('pagehide', onPageHide);
      window.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleFocus);
    };
  }, []);

  // interrupt window reload if we have recorded audio
  useEffect(() => {
    const onBeforeUnload = (e: Event) => {
      if (!isUploadComplete.current) {
        // allow redirect when posting
        e.preventDefault();
      }
    };

    if (hasRecording) {
      window.addEventListener('beforeunload', onBeforeUnload);
      return () => {
        window.removeEventListener('beforeunload', onBeforeUnload);
      };
    }
  }, [rec.isRecording, rec.hasData, isOpen]);

  const blocker = useBlocker(hasRecording);

  useEffect(() => {
    if (blocker.state === 'blocked') {
      if (debug) console.log(...prepareArgs('Orchestration::useEffect([blocker.state])'));
      canNavigate('Orchestration::useEffect([blocker.state])').then((confirmed) => {
        if (!confirmed) {
          //   rec.flush(loc.pathname);
        }
        blocker.proceed();
      });
    }
  }, [blocker.state]);

  // close record on navigate
  useEffect(() => {
    if (isRecordMode(audioMode)) {
      rec.disconnect().then(() => close(true));
    }
  }, [loc]);

  // close player when swell doesn't load - also, close player is swell data is not "enabled" isValidCanonicalId() is screwing this up: test: /media/c?debug=orch
  useEffect(() => {
    if (swell.isError && !isRecording) {
      close();
    }
  }, [swell.isError, isRecording]);

  const showPlayerForText = true; //useMemo(() => debugText.debug, [debugText.debug]);
  // close dash for text swell
  useEffect(() => {
    if (swell.isSuccess && audioData && isText) {
      if (!showPlayerForText) {
        close();
      } else {
        open({ mode: AudioMode.PLAY });
      }
      //   close();
    } else if (!isOpen) {
      //   open({ mode: AudioMode.PLAY });
    }
  }, [swell.isSuccess, audioData, isText, showPlayerForText]);

  return (
    <OrchestrationContext.Provider
      value={{
        completeRecording,
        completeTextResponse,
        togglePlay,
        open,
        close,
        isOpen,
        hasOpened,
        isRecording,
        isUploading,
        uploadStatus,
        errMsg,
        setMode: _setMode,
        blockNavigation,
        canNavigate,
        payload: {},
        stashId,
      }}
    >
      {/* <pre style={{ marginTop: 100 }}>{JSON.stringify({ isOpen, hasRecording }, null, 2)}</pre> */}

      {children}
      {/* <Prompt // TODO: upgrade react-router and customize this popup
        when={hasRecording}
        message={(location, action) => {
          if (debug) console.log({ location, action });
          return 'Are you sure? You will lose your current recording.';
        }}
      /> */}
    </OrchestrationContext.Provider>
  );
};
