const DUMMY_HOST = 'https://www.domain.com';

export function buildUrl(
  parts: {
    href?: string;
    protocol?: string;
    host?: string;
    pathname?: string;
    searchParams?: Record<string, string | null> | null;
    hostname?: string;
  } = {},
) {
  try {
    // TODO: use base argument in URL object to parse entire pathname if it contains ?/#
    if (parts.pathname?.includes('?')) {
      const [pathname, searchString] = parts.pathname.split('?');
      parts.pathname = pathname;
      if (searchString) {
        const s = new URLSearchParams(searchString);
        parts.searchParams = { ...parts.searchParams, ...Object.fromEntries(s.entries()) };
      }
    }

    const url = new URL(parts.pathname || '', parts.href || DUMMY_HOST);
    // Assign other parts to the URL
    if (parts.protocol) url.protocol = parts.protocol;
    if (parts.host) url.host = parts.host;
    if (parts.hostname) url.hostname = parts.hostname;

    // Assign search parameters
    if (parts.searchParams) {
      Object.entries(parts.searchParams).forEach(([key, value]) => {
        if (value === null) {
          url.searchParams.delete(key);
        } else {
          url.searchParams.set(key, value);
        }
      });
    }
    const res = !parts?.href ? url.href.replace(DUMMY_HOST, '') : url.href;
    return res;
  } catch (err) {
    console.warn('buildUrl', err, parts);
    return '';
  }
}
