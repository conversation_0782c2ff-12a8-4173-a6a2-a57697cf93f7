type EnvPropsType = {
  ASSET_PATH: string;
  ASSET_URL: string;
  CANONICAL_ALT_DOMAIN: string;
  CANONICAL_DOMAIN: string;
  GTM_ID: string;
  SITE_DOMAIN: string;
  STAGE: string;
  SWELL_API: string;
  SWELL_DEVELOPERS_URL: string;
  TRACKING_API: string;
  TIME_BEFORE_LOGIN_POPUP: string;
  TIME_BETWEEN_LOGIN_POPUP: string;
  TIME_BEFORE_FOLLOW_POPUP: string;
};

export const exposeEnv: (keyof EnvPropsType)[] = [
  'ASSET_PATH', //
  'ASSET_URL',
  'CANONICAL_ALT_DOMAIN',
  'CANONICAL_DOMAIN',
  'GTM_ID',
  'SITE_DOMAIN',
  'STAGE',
  'SWELL_API',
  'SWELL_DEVELOPERS_URL',
  'TRACKING_API',
  'TIME_BEFORE_LOGIN_POPUP',
  'TIME_BETWEEN_LOGIN_POPUP',
  'TIME_BEFORE_FOLLOW_POPUP',
];
