import { createRoot, hydrateRoot } from 'react-dom/client';
import { RouteObject, createBrowserRouter } from 'react-router-dom';
import '../assets/css/main.scss';
import { ClientRoot } from './shared/ClientRoot';

export function renderClient(routes: RouteObject[], opts: { basename?: string } = {}) {
  const router = createBrowserRouter(routes, opts);
  const $root = document.getElementById('root');
  const forceRefresh = false;

  if ($root) {
    if (forceRefresh || $root.innerHTML === '') {
      const root = createRoot($root);
      root.render(<ClientRoot router={router} />);
    } else {
      hydrateRoot($root, <ClientRoot router={router} />);
    }
  }
}
