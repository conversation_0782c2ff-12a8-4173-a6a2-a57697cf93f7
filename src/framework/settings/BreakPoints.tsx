const $swell_card_min_width = 360;
const $swell_grid_gap = 18;

const $col_break_1 = $swell_card_min_width * 1 + 2 * $swell_grid_gap;
const $col_break_2 = $swell_card_min_width * 2 + 3 * $swell_grid_gap;
const $col_break_3 = $swell_card_min_width * 3 + 4 * $swell_grid_gap;
const $col_break_4 = $swell_card_min_width * 4 + 5 * $swell_grid_gap;

export enum BreakPoints {
  XS = $col_break_1,
  S = 576,
  M = 668, //$col_break_1,//768,
  L = $col_break_2, //992,
  XL = $col_break_3, //1200,
  XXL = $col_break_4, //1400,
}
