import { IServerSettings } from '../../models/models';
import { DEFAULT_COLOR_MODE } from './DEFAULT_COLOR_MODE';
import { DEFAULT_COUNTRY_CODE } from './settings';

export const DEFAULT_SERVER_SETTINGS: IServerSettings = {
  isLocal: false,
  isStage: false,
  isProd: true,
  stage: 'prod',
  countryCode: DEFAULT_COUNTRY_CODE,
  serverCountryCode: DEFAULT_COUNTRY_CODE,
  apiUrl: '',
  resolvedColorMode: DEFAULT_COLOR_MODE,
  colorMode: DEFAULT_COLOR_MODE,
  autoPlay: true,
  debug: '',
  volume: 1,
  playbackRate: 1,
  isWidget: false,
  version: 0,
  allowPlayInCard: false,
  utmParams: {},
};
