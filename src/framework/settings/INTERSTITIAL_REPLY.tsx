import AudioMid from '../../assets/audio/SwellcastCom_Vox_MidConvo.mp3';
import { SwellReplyModel } from '../../models/models';
import { SwellTeamMember } from './SwellTeamMember';

export const INTERSTITIAL_REPLY: SwellReplyModel = {
  id: 'interstitialAudio',
  trackId: 'interstitialAudio',
  canSubscribe: false,
  canonicalId: 'interstitialAudio',
  audio: { duration: 15, url: AudioMid, wave: [3961865494468910600, 3211712768593035300, 3995339099762519000, 2646364943810256400, 8819024485374244] },
  author: <PERSON>wellTeamMember,
  swellcastOwner: SwellTeamMember,
  description: 'Welcome to Swell!',
  masterRef: {
    params: { replyId: 'interstitialAudio' }, //
    categories: [],
    id: 'swell',
    replyId: 'interstitialAudio',
    owner: {},
    author: SwellTeamMember,
    swellcast: { id: 'swell' },
    isMaster: false,
    canReply: false,
    isPanel: false,
  },
  swells: [],
  replies: [],
  categories: [],
  reactions: [],
  index: 0,
} as unknown as SwellReplyModel;
