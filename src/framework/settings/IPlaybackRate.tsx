interface IPlaybackRate {
  value: number;
  label: string;
  next: IPlaybackRate | null;
}
const playbackRatesList: IPlaybackRate[] = [
  { value: 1, label: '1.0x', next: null }, //
  { value: 1.25, label: '1.25x', next: null },
  { value: 1.5, label: '1.5x', next: null },
  { value: 1.75, label: '1.75x', next: null },
  { value: 2, label: '2.0x', next: null },
];
playbackRatesList.forEach((e, i) => {
  e.next = playbackRatesList[(i + 1) % playbackRatesList.length];
});

export const playbackRatesListLookup: Record<number, IPlaybackRate> = playbackRatesList.reduce((e, r) => {
  return { ...e, [r.value]: r };
}, {});
