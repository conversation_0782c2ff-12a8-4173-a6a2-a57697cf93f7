// import BrunoWave from '@images/bruno-wave-card.svg?url';
import Bruno<PERSON>aveGrey from '@images/wave-greyscale-min.png?url';
import BrunoWave from '@images/wave-min.png?url';

// eslint-disable-next-line react-refresh/only-export-components
export const isLocal = process.env.STAGE === 'local';
export const isStage = process.env.STAGE === 'stage';

export const SWELL_API_STAGE = 'https://stagewidgetapi.swell.life/graphql';
export const SWELL_API_PROD = 'https://widgetapi.swell.life/graphql';
// export const SWELL_API_PREPROD = 'https://preprodwidgetapi.swell.life/graphql';

export const iOSId = '1498360152';
export const iOSUrl = `https://apps.apple.com/us/app/swell/id${iOSId}?ls=1`;
export const AndroidId = 'life.swell.swellapp';
export const AndroidUrl = `https://play.google.com/store/apps/details?id=${AndroidId}`;
export const DefaultAppUrl = 'https://www.swell.life/getapp';

export const fbAppId = '2988352507862494';
export const fbAppId_stage = '561984425145118';

export const widgetVersion = '2.0';

// export { BrunoWave };
export const BrunoWaveCard = BrunoWave;
export const BrunoWaveCardDark = BrunoWaveGrey;
export const DEFAULT_COUNTRY_CODE = 'US';
