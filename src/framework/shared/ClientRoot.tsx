import { HydrationBoundary, QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HelmetProvider } from 'react-helmet-async';
import { RouterProvider, RouterProviderProps } from 'react-router-dom';
import { ErrorBoundary } from '../../components/common/ErrorBoundary';

import { StrictMode } from 'react';
import { queryConfig } from './config';

const queryClient = new QueryClient(queryConfig);

export const ClientRoot = ({ router }: { router: RouterProviderProps['router'] }) => (
  <StrictMode>
    <ErrorBoundary>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <HydrationBoundary state={window.__REACT_QUERY_STATE__}>
            <RouterProvider router={router} />
          </HydrationBoundary>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  </StrictMode>
);
