import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HelmetProvider } from 'react-helmet-async';
import { StaticHandlerContext, StaticRouterProvider, StaticRouterProviderProps } from 'react-router-dom';
import { HelmetContext } from '../../models/models';
// TODO: put the core header tags here and only put og in the client
export const ServerRoot = ({ queryClient, router, helmetContext }: { queryClient: QueryClient; router: StaticRouterProviderProps['router']; helmetContext: HelmetContext }) => {
  return (
    <HelmetProvider context={helmetContext}>
      <QueryClientProvider client={queryClient}>
        <StaticRouterProvider router={router} context={{} as StaticHandlerContext} />
      </QueryClientProvider>
    </HelmetProvider>
  );
};
