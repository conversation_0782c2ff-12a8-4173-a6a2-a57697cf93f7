import { useLocation } from 'react-router-dom';
import { Downtime } from '../../components/common/Downtime';
import { ColorModeProvider } from '../../components/common/colormode/ColorModeProvider';
import { DebugProvider } from '../../components/debug/DebugProvider';
import { Debugger } from '../../components/debug/Debugger';
import { AuthProvider } from '../../components/login/AuthProvider';
import { AudioControllerProvider } from '../../components/player/AudioPlayer/AudioControllerProvider';
import { AudioTrackProvider } from '../../components/player/AudioPlayer/AudioTrackProvider';
import { AudioRecorderProvider } from '../../components/player/AudioRecorder/AudioRecorderProvider';
import { PopupProvider } from '../../components/popup/PopupProvider';
import { SettingsProvider } from '../../components/settings/SettingsProvider';
import { AudioContextProvider } from '../../view/sound/audio/AudioContextProvider';
import { AudioElementProvider } from '../../view/sound/audio/AudioElementProvider';
import { VideoElementProvider } from '../../view/sound/video/VideoElementProvider';
import { AudioModeProvider } from '../AudioModeProvider';
import { HelmetBuilder } from '../HelmetBuilder';
import { OrchestrationProvider } from '../OrchestrationProvider';

export const SwellBase = ({ children }: { children: React.ReactNode }) => {
  const loc = useLocation();
  return (
    <DebugProvider>
      <ColorModeProvider>
        <SettingsProvider>
          <HelmetBuilder canonicalPath={loc.pathname} />
          <AuthProvider>
            <PopupProvider>
              <VideoElementProvider>
                <AudioContextProvider>
                  <AudioElementProvider>
                    <AudioModeProvider>
                      <AudioTrackProvider>
                        <AudioRecorderProvider>
                          <AudioControllerProvider>
                            <OrchestrationProvider>
                              <Downtime>{children}</Downtime>
                              <Debugger />
                            </OrchestrationProvider>
                          </AudioControllerProvider>
                        </AudioRecorderProvider>
                      </AudioTrackProvider>
                    </AudioModeProvider>
                  </AudioElementProvider>
                </AudioContextProvider>
              </VideoElementProvider>
            </PopupProvider>
          </AuthProvider>
        </SettingsProvider>
      </ColorModeProvider>
    </DebugProvider>
  );
};
