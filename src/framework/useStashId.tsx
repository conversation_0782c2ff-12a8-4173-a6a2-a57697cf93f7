import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useCurrentSwell } from '../api/gql.loadSwellById';
import { useAuth } from '../components/login/useAuth';

const useWriteParams = () => {
  const [searchParams] = useSearchParams({ params: '' });
  const rawParams = searchParams.get('params') ?? '';
  const params = useMemo(() => {
    const _params = { promptId: '' };
    if (rawParams) {
      try {
        Object.assign(_params, JSON.parse(rawParams));
      } catch {
        //
      }
    }
    return _params;
  }, [rawParams]);

  return params;
};

export const useStashId = () => {
  const auth = useAuth();
  const swell = useCurrentSwell();
  const writeParams = useWriteParams();

  return useMemo(() => {
    const parts = [];
    if (auth.isReady) parts.push(auth.getAlias());
    if (swell.isSuccess) parts.push(swell.data.id);
    if (writeParams.promptId) parts.push(writeParams.promptId);
    return parts.join('_');
  }, [auth, swell.data?.id, swell.isSuccess, writeParams.promptId]);
};
