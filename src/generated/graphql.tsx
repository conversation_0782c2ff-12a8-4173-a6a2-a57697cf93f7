export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
};

/** ActionCategory */
export enum ActionCategory {
  ActivityrecommendationSwellAdded = 'ACTIVITYRECOMMENDATION_SWELL_ADDED',
  ActivityGeneral = 'ACTIVITY_GENERAL',
  ActivityGroupInvited = 'ACTIVITY_GROUP_INVITED',
  ActivityGroupJoined = 'ACTIVITY_GROUP_JOINED',
  ActivityGroupJoinedChild = 'ACTIVITY_GROUP_JOINED_CHILD',
  ActivityGroupJoinedParent = 'ACTIVITY_GROUP_JOINED_PARENT',
  ActivityPromptAdded = 'ACTIVITY_PROMPT_ADDED',
  ActivityPromptResponded = 'ACTIVITY_PROMPT_RESPONDED',
  ActivityPromptRespondedChild = 'ACTIVITY_PROMPT_RESPONDED_CHILD',
  ActivityPromptRespondedParent = 'ACTIVITY_PROMPT_RESPONDED_PARENT',
  ActivityReplyMentioned = 'ACTIVITY_REPLY_MENTIONED',
  ActivityReplyReacted = 'ACTIVITY_REPLY_REACTED',
  ActivityReplyReswelled = 'ACTIVITY_REPLY_RESWELLED',
  ActivityReplyVideoprocessed = 'ACTIVITY_REPLY_VIDEOPROCESSED',
  ActivitySwellcastFollowed = 'ACTIVITY_SWELLCAST_FOLLOWED',
  ActivitySwellcastFollowedChild = 'ACTIVITY_SWELLCAST_FOLLOWED_CHILD',
  ActivitySwellcastFollowedParent = 'ACTIVITY_SWELLCAST_FOLLOWED_PARENT',
  ActivitySwellcastSchedulefailed = 'ACTIVITY_SWELLCAST_SCHEDULEFAILED',
  ActivitySwellcastSubscribed = 'ACTIVITY_SWELLCAST_SUBSCRIBED',
  ******************************** = 'ACTIVITY_SWELLCAST_SUBSCRIBED_CHILD',
  ActivitySwellcastSubscribedParent = 'ACTIVITY_SWELLCAST_SUBSCRIBED_PARENT',
  ActivitySwellAdded = 'ACTIVITY_SWELL_ADDED',
  ActivitySwellEdited = 'ACTIVITY_SWELL_EDITED',
  ActivitySwellInvited = 'ACTIVITY_SWELL_INVITED',
  ActivitySwellPurchased = 'ACTIVITY_SWELL_PURCHASED',
  ActivitySwellPurchasedChild = 'ACTIVITY_SWELL_PURCHASED_CHILD',
  ActivitySwellPurchasedParent = 'ACTIVITY_SWELL_PURCHASED_PARENT',
  ActivitySwellReacted = 'ACTIVITY_SWELL_REACTED',
  ActivitySwellReplied = 'ACTIVITY_SWELL_REPLIED',
  ActivitySwellReswelled = 'ACTIVITY_SWELL_RESWELLED',
  ActivitySwellScheduleposted = 'ACTIVITY_SWELL_SCHEDULEPOSTED',
  ActivitySwellShared = 'ACTIVITY_SWELL_SHARED',
  ActivitySwellSharefailed = 'ACTIVITY_SWELL_SHAREFAILED',
  ActivitySwellVideoprocessed = 'ACTIVITY_SWELL_VIDEOPROCESSED',
  NotificationGeneral = 'NOTIFICATION_GENERAL',
  NotificationGroupInvited = 'NOTIFICATION_GROUP_INVITED',
  NotificationGroupJoined = 'NOTIFICATION_GROUP_JOINED',
  NotificationGroupJoinedChild = 'NOTIFICATION_GROUP_JOINED_CHILD',
  NotificationGroupJoinedParent = 'NOTIFICATION_GROUP_JOINED_PARENT',
  NotificationPromptAdded = 'NOTIFICATION_PROMPT_ADDED',
  NotificationPromptResponded = 'NOTIFICATION_PROMPT_RESPONDED',
  NotificationPromptRespondedChild = 'NOTIFICATION_PROMPT_RESPONDED_CHILD',
  NotificationPromptRespondedParent = 'NOTIFICATION_PROMPT_RESPONDED_PARENT',
  NotificationReplyMentioned = 'NOTIFICATION_REPLY_MENTIONED',
  NotificationReplyReacted = 'NOTIFICATION_REPLY_REACTED',
  NotificationReplyReswelled = 'NOTIFICATION_REPLY_RESWELLED',
  NotificationReplyVideoprocessed = 'NOTIFICATION_REPLY_VIDEOPROCESSED',
  NotificationSwellcastFollowed = 'NOTIFICATION_SWELLCAST_FOLLOWED',
  NotificationSwellcastFollowedChild = 'NOTIFICATION_SWELLCAST_FOLLOWED_CHILD',
  NotificationSwellcastFollowedParent = 'NOTIFICATION_SWELLCAST_FOLLOWED_PARENT',
  NotificationSwellcastSchedulefailed = 'NOTIFICATION_SWELLCAST_SCHEDULEFAILED',
  NotificationSwellcastSubscribed = 'NOTIFICATION_SWELLCAST_SUBSCRIBED',
  NotificationSwellcastSubscribedChild = 'NOTIFICATION_SWELLCAST_SUBSCRIBED_CHILD',
  NotificationSwellcastSubscribedParent = 'NOTIFICATION_SWELLCAST_SUBSCRIBED_PARENT',
  NotificationSwellAdded = 'NOTIFICATION_SWELL_ADDED',
  NotificationSwellEdited = 'NOTIFICATION_SWELL_EDITED',
  NotificationSwellInvited = 'NOTIFICATION_SWELL_INVITED',
  NotificationSwellPurchased = 'NOTIFICATION_SWELL_PURCHASED',
  NotificationSwellPurchasedChild = 'NOTIFICATION_SWELL_PURCHASED_CHILD',
  NotificationSwellPurchasedParent = 'NOTIFICATION_SWELL_PURCHASED_PARENT',
  NotificationSwellReacted = 'NOTIFICATION_SWELL_REACTED',
  NotificationSwellReplied = 'NOTIFICATION_SWELL_REPLIED',
  NotificationSwellReswelled = 'NOTIFICATION_SWELL_RESWELLED',
  NotificationSwellScheduleposted = 'NOTIFICATION_SWELL_SCHEDULEPOSTED',
  NotificationSwellShared = 'NOTIFICATION_SWELL_SHARED',
  NotificationSwellSharefailed = 'NOTIFICATION_SWELL_SHAREFAILED',
  NotificationSwellVideoprocessed = 'NOTIFICATION_SWELL_VIDEOPROCESSED',
  RecommendationSwellAdded = 'RECOMMENDATION_SWELL_ADDED'
}

export type ActivityMinimalResponse = {
  action?: Maybe<Scalars['String']['output']>;
  actionCategory: ActionCategory;
  actor?: Maybe<AuthorModel>;
  badge?: Maybe<Scalars['Float']['output']>;
  body?: Maybe<Scalars['String']['output']>;
  campaignId?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  forceBodyText?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  paramId: Scalars['String']['output'];
  participant?: Maybe<Scalars['Boolean']['output']>;
  refId?: Maybe<Scalars['String']['output']>;
  replyId?: Maybe<Scalars['String']['output']>;
  swellAuthorId?: Maybe<Scalars['String']['output']>;
  swellId?: Maybe<Scalars['String']['output']>;
  swellcastId?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type ActivityResponse = {
  action?: Maybe<Scalars['String']['output']>;
  actionCategory: ActionCategory;
  actor?: Maybe<AuthorModel>;
  badge?: Maybe<Scalars['Float']['output']>;
  body?: Maybe<Scalars['String']['output']>;
  campaignId?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  forceBodyText?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  paramId: Scalars['String']['output'];
  participant?: Maybe<Scalars['Boolean']['output']>;
  prompt?: Maybe<PromptResponse>;
  refId?: Maybe<Scalars['String']['output']>;
  reply?: Maybe<ReplyModel>;
  swell?: Maybe<SwellResponse>;
  swellcast?: Maybe<SwellcastResponse>;
  title?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  videoId?: Maybe<Scalars['String']['output']>;
};

export type AdminConfigItem = {
  exclusions?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['String']['output'];
  inclusions?: Maybe<Array<Scalars['String']['output']>>;
};

/** AllowedPromptResponseTypes */
export enum AllowedPromptResponseTypes {
  Audio = 'AUDIO',
  Text = 'TEXT'
}

export type ArticleInput = {
  image?: InputMaybe<Scalars['String']['input']>;
  link: Scalars['String']['input'];
  sitename?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type ArticleType = {
  image?: Maybe<Scalars['String']['output']>;
  link: Scalars['String']['output'];
  sitename?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type AudioInput = {
  duration?: InputMaybe<Scalars['Float']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  wave?: InputMaybe<Array<Scalars['Float']['input']>>;
};

/** AudioSource */
export enum AudioSource {
  Recorded = 'RECORDED',
  Rss = 'RSS',
  Uploaded = 'UPLOADED'
}

export type AudioType = {
  duration?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  wave?: Maybe<Array<Scalars['Float']['output']>>;
};

export type AuthorModel = {
  alias?: Maybe<Scalars['String']['output']>;
  audio?: Maybe<AudioType>;
  badge?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  followingCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  status: Status;
  swellcastDescription?: Maybe<Scalars['String']['output']>;
  swellcastId: Scalars['String']['output'];
  swellcastImage?: Maybe<Scalars['String']['output']>;
};

export type AuthorTalkingCircleModel = {
  alias?: Maybe<Scalars['String']['output']>;
  audio?: Maybe<AudioType>;
  badge?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  followingCount?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  status: Scalars['String']['output'];
  swellcastDescription?: Maybe<Scalars['String']['output']>;
  swellcastId: Scalars['String']['output'];
  swellcastImage?: Maybe<Scalars['String']['output']>;
  talklingCircleInfo?: Maybe<TalkingCircle>;
};

export type AuthorWithFollowingStateModel = {
  alias?: Maybe<Scalars['String']['output']>;
  audio?: Maybe<AudioType>;
  badge?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  followingCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  status: Status;
  swellcastDescription?: Maybe<Scalars['String']['output']>;
  swellcastId: Scalars['String']['output'];
  swellcastImage?: Maybe<Scalars['String']['output']>;
};

export type BgMusicResponse = {
  id: Scalars['String']['output'];
  source: BgMusicSource;
  title: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

/** BgMusicSource */
export enum BgMusicSource {
  BuiltIn = 'BUILT_IN',
  Uploaded = 'UPLOADED'
}

export type BillingPlanResponse = {
  billingState: Scalars['String']['output'];
  createdOn: Scalars['DateTime']['output'];
  endDate?: Maybe<Scalars['DateTime']['output']>;
  endedOn?: Maybe<Scalars['DateTime']['output']>;
  expiryDate?: Maybe<Scalars['DateTime']['output']>;
  freeTrialDays?: Maybe<Scalars['Float']['output']>;
  graceEndDate?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  lastPaymentOn?: Maybe<Scalars['DateTime']['output']>;
  lastStateChangeDate?: Maybe<Scalars['DateTime']['output']>;
  lastUpdatedOn?: Maybe<Scalars['DateTime']['output']>;
  pauseUntil?: Maybe<Scalars['DateTime']['output']>;
  paymentCreatedOn: Scalars['DateTime']['output'];
  paymentGatewayId: Scalars['String']['output'];
  paymentId?: Maybe<Scalars['String']['output']>;
  planId: Scalars['String']['output'];
  planState: Scalars['String']['output'];
  price?: Maybe<PriceResponse>;
  productDescription?: Maybe<Scalars['String']['output']>;
  productId?: Maybe<Scalars['String']['output']>;
  productName?: Maybe<Scalars['String']['output']>;
  reasonEnded?: Maybe<Scalars['String']['output']>;
  renewedOn?: Maybe<Scalars['DateTime']['output']>;
  startedOn?: Maybe<Scalars['DateTime']['output']>;
  swellcastId: Scalars['String']['output'];
  transactionId?: Maybe<Scalars['String']['output']>;
  userId: Scalars['String']['output'];
};

/** BlockStatus */
export enum BlockStatus {
  Blocked = 'BLOCKED',
  Unblocked = 'UNBLOCKED'
}

/** CancelStatus */
export enum CancelStatus {
  Cancelled = 'CANCELLED',
  CancelInProgress = 'CANCEL_IN_PROGRESS',
  Revoked = 'REVOKED'
}

export type CategoryChild = {
  countryCode?: Maybe<Array<Scalars['String']['output']>>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  isDefault?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
};

export type CategoryResponse = {
  boost?: Maybe<Scalars['Float']['output']>;
  children?: Maybe<Array<CategoryChild>>;
  countryCodes?: Maybe<Array<Scalars['String']['output']>>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  isDefault?: Maybe<Scalars['Boolean']['output']>;
  lastCommunityPostOn?: Maybe<Scalars['DateTime']['output']>;
  name: Scalars['String']['output'];
  ogImage?: Maybe<Scalars['String']['output']>;
  related?: Maybe<Array<Scalars['String']['output']>>;
  zoneFiltered?: Maybe<Scalars['Boolean']['output']>;
};

/** CohostingStatus */
export enum CohostingStatus {
  Allhost = 'ALLHOST',
  Cohost = 'COHOST',
  Nothost = 'NOTHOST'
}

export type CommunityPageSubscriptionResponse = {
  active?: Maybe<Scalars['Boolean']['output']>;
  cancelStatus?: Maybe<CancelStatus>;
  endsOn?: Maybe<Scalars['DateTime']['output']>;
  freetrialDays?: Maybe<Scalars['Float']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  paidOn?: Maybe<Scalars['DateTime']['output']>;
  pausedUntil?: Maybe<Scalars['DateTime']['output']>;
  pgSubscriptionId?: Maybe<Scalars['String']['output']>;
  price?: Maybe<PriceResponse>;
  product?: Maybe<ProductResponse>;
  renewalTriggeredOn?: Maybe<Scalars['DateTime']['output']>;
  renewsOn?: Maybe<Scalars['DateTime']['output']>;
  startsOn?: Maybe<Scalars['DateTime']['output']>;
  subscribers?: Maybe<Array<AuthorModel>>;
  subscriptionState?: Maybe<SubscriptionStatus>;
  subscriptionType: SubscriptionType;
  swellcasts?: Maybe<Array<SwellcastResponse>>;
};

export type CurrencyResponse = {
  id: Scalars['String']['output'];
  image: Scalars['String']['output'];
  name: Scalars['String']['output'];
  symbol: Scalars['String']['output'];
};

/** FeatureItemType */
export enum FeatureItemType {
  Promo = 'PROMO',
  Swellcast = 'SWELLCAST'
}

export type FeaturedPromoStyleModel = {
  backgroundColorHex?: Maybe<Scalars['String']['output']>;
  backgroundImageUrl?: Maybe<Scalars['String']['output']>;
  fontColorHex?: Maybe<Scalars['String']['output']>;
  headingColorHex?: Maybe<Scalars['String']['output']>;
};

export type FetchAudioTextResponse = {
  code: Scalars['Float']['output'];
  message: Scalars['String']['output'];
  text?: Maybe<Scalars['String']['output']>;
};

/** FollowingStatus */
export enum FollowingStatus {
  Followed = 'FOLLOWED',
  Unfollowed = 'UNFOLLOWED'
}

export type FullPrice = {
  monthlyValue?: Maybe<Scalars['String']['output']>;
  value: Scalars['String']['output'];
};

/** Gender */
export enum Gender {
  Empty = 'EMPTY',
  Female = 'FEMALE',
  Male = 'MALE',
  Other = 'OTHER'
}

export type HeardStatus = {
  lastHeardId?: Maybe<Scalars['String']['output']>;
  nextReplyToHear?: Maybe<Scalars['String']['output']>;
  unheardCount?: Maybe<Scalars['Float']['output']>;
};

export type HomePageActionModel = {
  label?: Maybe<Scalars['String']['output']>;
  params?: Maybe<HomePageActionParamsModel>;
  referrerId?: Maybe<Scalars['String']['output']>;
  route?: Maybe<Scalars['String']['output']>;
  style?: Maybe<HomePageStyleModel>;
  type?: Maybe<Scalars['String']['output']>;
};

export type HomePageActionParamsModel = {
  alias?: Maybe<Scalars['String']['output']>;
  canonicalId?: Maybe<Scalars['String']['output']>;
  categoryId?: Maybe<Scalars['String']['output']>;
  channels?: Maybe<Array<Scalars['String']['output']>>;
  description?: Maybe<Scalars['String']['output']>;
  doReply?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  imageUrl?: Maybe<Scalars['String']['output']>;
  isPanel?: Maybe<Scalars['Boolean']['output']>;
  isPremium?: Maybe<Scalars['Boolean']['output']>;
  languageCode?: Maybe<Scalars['String']['output']>;
  promptId?: Maybe<Scalars['String']['output']>;
  promptSlug?: Maybe<Scalars['String']['output']>;
  searchKey?: Maybe<Scalars['String']['output']>;
  tag?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type HomePageHeaderModel = {
  actions?: Maybe<Array<HomePageActionModel>>;
  description?: Maybe<Scalars['String']['output']>;
  heading?: Maybe<Scalars['String']['output']>;
  style?: Maybe<HomePageStyleModel>;
  text?: Maybe<Scalars['String']['output']>;
};

export type HomePageHeadersItemModel = {
  header?: Maybe<HomePageHeaderModel>;
};

export type HomePageHeadersModel = {
  actions?: Maybe<Array<HomePageActionModel>>;
  animationSeconds?: Maybe<Scalars['Float']['output']>;
  disableKenBurnsEffect?: Maybe<Scalars['Boolean']['output']>;
  items?: Maybe<Array<HomePageHeadersItemModel>>;
  randomizeItems?: Maybe<Scalars['Boolean']['output']>;
};

export type HomePageItemModel = {
  gap?: Maybe<Scalars['Float']['output']>;
  header?: Maybe<HomePageHeaderModel>;
  headers?: Maybe<HomePageHeadersModel>;
  id?: Maybe<Scalars['String']['output']>;
  style?: Maybe<HomePageStyleModel>;
  subTitle?: Maybe<Scalars['String']['output']>;
  tabId?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

/** HomePageRenderType */
export enum HomePageRenderType {
  Audio = 'AUDIO',
  Horizontal = 'HORIZONTAL',
  HorizontalLandscape = 'HORIZONTAL_LANDSCAPE',
  HorizontalPortrait = 'HORIZONTAL_PORTRAIT',
  Square = 'SQUARE',
  Vertical = 'VERTICAL'
}

/** HomePageSectionDataType */
export enum HomePageSectionDataType {
  Authors = 'AUTHORS',
  Hashtags = 'HASHTAGS',
  Images = 'IMAGES',
  Promos = 'PROMOS',
  Prompts = 'PROMPTS',
  Stations = 'STATIONS',
  Swellcasts = 'SWELLCASTS',
  Swells = 'SWELLS'
}

export type HomePageSectionParamsModel = {
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  seeAllUrl?: Maybe<Scalars['String']['output']>;
  type?: Maybe<StationType>;
};

export type HomePageSectionResponse = {
  authors: Array<AuthorTalkingCircleModel>;
  emptyMessage?: Maybe<Scalars['String']['output']>;
  hashtags: Array<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  images: Array<ImageResponse>;
  label?: Maybe<Scalars['String']['output']>;
  promos: Array<HomePageHeaderModel>;
  prompts: Array<PromptResponse>;
  sectionDataType?: Maybe<HomePageSectionDataType>;
  sectionParams?: Maybe<HomePageSectionParamsModel>;
  sectionType: Scalars['String']['output'];
  stations: Array<StationInfoResponse>;
  swellcasts: Array<SwellcastResponse>;
  swells: Array<SwellWithReplyResponse>;
  type: HomePageRenderType;
};

export type HomePageStyleModel = {
  alignment?: Maybe<Scalars['String']['output']>;
  backgroundColorHex?: Maybe<Scalars['String']['output']>;
  backgroundImageLayerColorHex?: Maybe<Scalars['String']['output']>;
  backgroundImageUrl?: Maybe<Scalars['String']['output']>;
  borderColorHex?: Maybe<Scalars['String']['output']>;
  descriptionFontSize?: Maybe<Scalars['Float']['output']>;
  descriptionFontWeight?: Maybe<Scalars['String']['output']>;
  fontColorHex?: Maybe<Scalars['String']['output']>;
  headingColorHex?: Maybe<Scalars['String']['output']>;
  headingFontSize?: Maybe<Scalars['Float']['output']>;
  headingFontWeight?: Maybe<Scalars['String']['output']>;
  titleFontSize?: Maybe<Scalars['Float']['output']>;
  titleFontWeight?: Maybe<Scalars['String']['output']>;
  width?: Maybe<Scalars['Float']['output']>;
};

export type ImageResponse = {
  image: Scalars['String']['output'];
  label?: Maybe<Scalars['String']['output']>;
  url: Scalars['String']['output'];
};

export type InviteSuggestionSection = {
  label: Scalars['String']['output'];
  sectionType: Scalars['String']['output'];
  users: Array<AuthorModel>;
};

export type InviteeInfo = {
  alias?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  swellcastId?: Maybe<Scalars['String']['output']>;
};

export type Invitees = {
  invitedBy?: Maybe<InviteeInfo>;
  invitedOn?: Maybe<Scalars['DateTime']['output']>;
  invitee?: Maybe<InviteeInfo>;
  repliedOn?: Maybe<Scalars['DateTime']['output']>;
};

export type Mutation = {
  ss: UploadFileResponse;
};


export type MutationSsArgs = {
  request: UploadFileRequest;
};

/** NotificationLevel */
export enum NotificationLevel {
  High = 'HIGH',
  Highest = 'HIGHEST',
  Low = 'LOW',
  Medium = 'MEDIUM'
}

export type OpenApiAudioModel = {
  duration?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
  wave?: Maybe<Array<Scalars['Float']['output']>>;
};

export type OpenApiAuthorModel = {
  alias?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  swellcast?: Maybe<OpenApiAuthorSwellcastModel>;
};

export type OpenApiAuthorSwellcastModel = {
  description?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
};

export type OpenApiCategoryModel = {
  boost?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
};

export type OpenApiFeaturedConvResponse = {
  count: Scalars['Float']['output'];
  featuredConversations?: Maybe<Array<OpenApiFeaturedItemModel>>;
};

export type OpenApiFeaturedItemModel = {
  id: Scalars['String']['output'];
  promo?: Maybe<OpenApiFeaturedPromo>;
  swellcast?: Maybe<OpenApiFeaturedSwellcast>;
  type: FeatureItemType;
};

export type OpenApiFeaturedPromo = {
  actionType?: Maybe<Scalars['String']['output']>;
  alias?: Maybe<Scalars['String']['output']>;
  className?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  heading?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  style?: Maybe<FeaturedPromoStyleModel>;
  text?: Maybe<Scalars['String']['output']>;
  type: FeatureItemType;
};

export type OpenApiFeaturedSwellcast = {
  blockState?: Maybe<BlockStatus>;
  blockingState?: Maybe<BlockStatus>;
  capabilities?: Maybe<Array<Scalars['String']['output']>>;
  className?: Maybe<Scalars['String']['output']>;
  cohostState?: Maybe<CohostingStatus>;
  cohostingState?: Maybe<CohostingStatus>;
  cohostsCount?: Maybe<Scalars['Float']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  followersCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastPromptCreatedOn?: Maybe<Scalars['DateTime']['output']>;
  lastSwellCreatedOn?: Maybe<Scalars['DateTime']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ogImage?: Maybe<Scalars['String']['output']>;
  onlyAdminCanPost?: Maybe<Scalars['Boolean']['output']>;
  openSwellcastType?: Maybe<OpenSwellcastType>;
  owner?: Maybe<SwellcastOwnerModel>;
  pageId?: Maybe<Scalars['String']['output']>;
  planIds?: Maybe<Array<Scalars['String']['output']>>;
  podcastData?: Maybe<Array<PodcastDataResponse>>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  preferredStations?: Maybe<Array<Scalars['String']['output']>>;
  product?: Maybe<ProductMinimalResponse>;
  promptCount?: Maybe<Scalars['Float']['output']>;
  subscribersCount?: Maybe<Scalars['Float']['output']>;
  subscriptionState?: Maybe<SubscriptionStatus>;
  swellCount?: Maybe<Scalars['Float']['output']>;
  type: SwellcastType;
  unheardCount?: Maybe<Scalars['Float']['output']>;
};

export type OpenApiHomePageResponse = {
  sections: Array<OpenApiHomePageSectionResponse>;
};

export type OpenApiHomePageSectionResponse = {
  authors: Array<OpenApiAuthorModel>;
  emptyMessage?: Maybe<Scalars['String']['output']>;
  hashtags: Array<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  images: Array<ImageResponse>;
  label?: Maybe<Scalars['String']['output']>;
  promos: Array<HomePageHeaderModel>;
  prompts: Array<OpenApiPromptResponse>;
  sectionDataType?: Maybe<HomePageSectionDataType>;
  sectionParams?: Maybe<HomePageSectionParamsModel>;
  sectionType: Scalars['String']['output'];
  stations: Array<StationInfoResponse>;
  swellcasts: Array<OpenApiSwellcastResponse>;
  swells: Array<OpenApiHomePageSwellResponse>;
  type: HomePageRenderType;
};

export type OpenApiHomePageSwellResponse = {
  articles?: Maybe<Array<ArticleType>>;
  audio?: Maybe<OpenApiAudioModel>;
  author?: Maybe<OpenApiAuthorModel>;
  canonicalId?: Maybe<Scalars['String']['output']>;
  cardInfo?: Maybe<SwellCardInfo>;
  categories?: Maybe<Array<OpenApiCategoryModel>>;
  createdOn?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  faces?: Maybe<Array<OpenApiSwellcastSwellFacesResponse>>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  homePageSectionType?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isQAndA?: Maybe<Scalars['Boolean']['output']>;
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  lastRepliedOn?: Maybe<Scalars['String']['output']>;
  pinState?: Maybe<PinStatus>;
  promptId?: Maybe<Scalars['String']['output']>;
  promptSlug?: Maybe<Scalars['String']['output']>;
  reactions?: Maybe<Array<OpenApiReactionResponse>>;
  repliesCount?: Maybe<Scalars['Float']['output']>;
  snippet?: Maybe<Scalars['String']['output']>;
  subscriptionOnly?: Maybe<Scalars['Boolean']['output']>;
  swellcast?: Maybe<OpenApiSwellcastInfoResponse>;
  swellcastOwner?: Maybe<OpenApiSwellcastOwnerModel>;
  title?: Maybe<Scalars['String']['output']>;
};

export type OpenApiLatestVideoResponse = {
  alias?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['String']['output']>;
  replyId?: Maybe<Scalars['String']['output']>;
  siteUrl: Scalars['String']['output'];
  swellCanonicalId?: Maybe<Scalars['String']['output']>;
  swellId?: Maybe<Scalars['String']['output']>;
  thumbnail?: Maybe<Scalars['String']['output']>;
  videoUrl: Scalars['String']['output'];
};

export type OpenApiOwnerModel = {
  alias?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  status?: Maybe<Scalars['String']['output']>;
};

export type OpenApiPopularAuthorModel = {
  alias?: Maybe<Scalars['String']['output']>;
  audio?: Maybe<OpenApiAudioModel>;
  badge?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  followingCount?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  swellcastDescription?: Maybe<Scalars['String']['output']>;
  swellcastId: Scalars['String']['output'];
  swellcastImage?: Maybe<Scalars['String']['output']>;
};

export type OpenApiPopularInfoResponse = {
  popularSpeakers?: Maybe<Array<OpenApiPopularAuthorModel>>;
  popularStations?: Maybe<Array<OpenApiCategoryModel>>;
  popularTopics?: Maybe<Array<Scalars['String']['output']>>;
};

export type OpenApiProfileWithSectionsResponse = {
  canPost?: Maybe<Scalars['Boolean']['output']>;
  canSubscribe: Scalars['Boolean']['output'];
  capabilities?: Maybe<Array<Scalars['String']['output']>>;
  createdOn?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  followersCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastSectionId?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ogImage?: Maybe<Scalars['String']['output']>;
  owner?: Maybe<OpenApiOwnerModel>;
  podcastData?: Maybe<Array<PodcastDataResponse>>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  preferredStations?: Maybe<Array<Scalars['String']['output']>>;
  promptCount?: Maybe<Scalars['Float']['output']>;
  sections?: Maybe<Array<OpenApiHomePageSectionResponse>>;
  subscriptionState?: Maybe<SubscriptionStatus>;
  swellCount?: Maybe<Scalars['Float']['output']>;
  swells?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  unheardCount?: Maybe<Scalars['Float']['output']>;
};

export type OpenApiPromoWithSectionsResponse = {
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  lastSectionId?: Maybe<Scalars['String']['output']>;
  promos?: Maybe<Array<HomePageItemModel>>;
  sections?: Maybe<Array<OpenApiHomePageSectionResponse>>;
};

export type OpenApiPromptResponse = {
  allowedResponseTypes?: Maybe<Array<AllowedPromptResponseTypes>>;
  altText: Scalars['String']['output'];
  createdOn: Scalars['String']['output'];
  credit?: Maybe<OpenApiAuthorModel>;
  editorsPickSetOn: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isEditorsPick?: Maybe<Scalars['Boolean']['output']>;
  ogImage: Scalars['String']['output'];
  postCount: Scalars['Float']['output'];
  promptImageUrl: Scalars['String']['output'];
  promptLink: Scalars['String']['output'];
  promptText: Scalars['String']['output'];
  promptTitle: Scalars['String']['output'];
  slug?: Maybe<Scalars['String']['output']>;
  swellId?: Maybe<Scalars['String']['output']>;
  swellcast: OpenApiSwellcastResponse;
};

export type OpenApiPromptWithSectionsResponse = {
  allowedResponseTypes?: Maybe<Array<AllowedPromptResponseTypes>>;
  altText: Scalars['String']['output'];
  createdOn: Scalars['String']['output'];
  credit?: Maybe<OpenApiAuthorModel>;
  editorsPickSetOn: Scalars['String']['output'];
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  isEditorsPick?: Maybe<Scalars['Boolean']['output']>;
  lastSectionId?: Maybe<Scalars['String']['output']>;
  ogImage: Scalars['String']['output'];
  postCount: Scalars['Float']['output'];
  promptImageUrl: Scalars['String']['output'];
  promptLink: Scalars['String']['output'];
  promptText: Scalars['String']['output'];
  promptTitle: Scalars['String']['output'];
  sections?: Maybe<Array<OpenApiHomePageSectionResponse>>;
  slug?: Maybe<Scalars['String']['output']>;
  swellId?: Maybe<Scalars['String']['output']>;
  swellcast: OpenApiSwellcastResponse;
};

export type OpenApiReactionResponse = {
  count?: Maybe<Scalars['Float']['output']>;
  pressState: ReactState;
  reaction?: Maybe<Scalars['String']['output']>;
};

export type OpenApiReplyResponse = {
  articles?: Maybe<Array<ArticleType>>;
  audio?: Maybe<AudioType>;
  author?: Maybe<OpenApiAuthorModel>;
  createdOn?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  isParent?: Maybe<Scalars['Boolean']['output']>;
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  message?: Maybe<Scalars['String']['output']>;
  parentId?: Maybe<Scalars['String']['output']>;
  promptId?: Maybe<Scalars['String']['output']>;
  reactions?: Maybe<Array<OpenApiReactionResponse>>;
  snippet?: Maybe<Scalars['String']['output']>;
};

export type OpenApiSearchPageResponse = {
  featured?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  newMembers?: Maybe<Array<OpenApiAuthorModel>>;
  popularCountries?: Maybe<Array<StationInfoResponse>>;
  popularLanguages?: Maybe<Array<StationInfoResponse>>;
  popularStations?: Maybe<Array<CategoryResponse>>;
  postsFromWorldOver?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  premiumSwellcasts?: Maybe<Array<OpenApiSwellcastResponse>>;
  promos?: Maybe<Array<HomePageItemModel>>;
  promosForWeb?: Maybe<Array<HomePageItemModel>>;
  swells?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  topSpeakers?: Maybe<Array<OpenApiAuthorModel>>;
  trendingHashtags?: Maybe<Array<Scalars['String']['output']>>;
  trendingSwells?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  worldwidePremiumSwellcasts?: Maybe<Array<OpenApiSwellcastResponse>>;
};

export type OpenApiSearchWithSectionsResponse = {
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  lastSectionId?: Maybe<Scalars['String']['output']>;
  sections?: Maybe<Array<OpenApiHomePageSectionResponse>>;
};

export type OpenApiSeeAllSectionResponse = {
  authors: Array<OpenApiAuthorModel>;
  emptyMessage?: Maybe<Scalars['String']['output']>;
  hashtags: Array<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  images: Array<ImageResponse>;
  label?: Maybe<Scalars['String']['output']>;
  promos: Array<HomePageHeaderModel>;
  prompts: Array<OpenApiPromptResponse>;
  sectionDataType?: Maybe<HomePageSectionDataType>;
  sectionParams?: Maybe<HomePageSectionParamsModel>;
  sectionType: Scalars['String']['output'];
  stations: Array<StationInfoResponse>;
  swellcasts: Array<OpenApiSwellcastResponse>;
  swells: Array<OpenApiHomePageSwellResponse>;
  type: HomePageRenderType;
};

export type OpenApiSeeAllWithSectionResponse = {
  description?: Maybe<Scalars['String']['output']>;
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  lastId?: Maybe<Scalars['String']['output']>;
  sections?: Maybe<Array<OpenApiSeeAllSectionResponse>>;
  title?: Maybe<Scalars['String']['output']>;
};

export type OpenApiStationResponse = {
  description?: Maybe<Scalars['String']['output']>;
  featured?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  firstTimePosts?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  newUsers?: Maybe<Array<OpenApiAuthorModel>>;
  ogImage?: Maybe<Scalars['String']['output']>;
  posts?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  postsFromWorldOver?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  topAuthors?: Maybe<Array<OpenApiAuthorModel>>;
  trendingSwells?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  type: StationType;
};

export type OpenApiStationSwellsResponse = {
  description?: Maybe<Scalars['String']['output']>;
  hasMore: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  ogImage?: Maybe<Scalars['String']['output']>;
  swells?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  type: StationType;
};

export type OpenApiStationWithSectionsResponse = {
  description?: Maybe<Scalars['String']['output']>;
  hasMore?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastSectionId?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  ogImage?: Maybe<Scalars['String']['output']>;
  promos?: Maybe<Array<HomePageItemModel>>;
  sections?: Maybe<Array<OpenApiHomePageSectionResponse>>;
  type: StationType;
};

export type OpenApiSwellResponse = {
  accessUntil?: Maybe<Scalars['String']['output']>;
  articles?: Maybe<Array<ArticleType>>;
  audio?: Maybe<AudioType>;
  author?: Maybe<OpenApiAuthorModel>;
  canReply: Scalars['Boolean']['output'];
  canonicalId?: Maybe<Scalars['String']['output']>;
  categories?: Maybe<Array<CategoryResponse>>;
  configuredPriceTxt?: Maybe<Scalars['String']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  faces?: Maybe<Array<OpenApiSwellcastSwellFacesResponse>>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  isPanel: Scalars['Boolean']['output'];
  isQAndA?: Maybe<Scalars['Boolean']['output']>;
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  languages?: Maybe<Array<Scalars['String']['output']>>;
  lastRepliedOn?: Maybe<Scalars['String']['output']>;
  ogImage?: Maybe<Scalars['String']['output']>;
  promptId?: Maybe<Scalars['String']['output']>;
  promptSlug?: Maybe<Scalars['String']['output']>;
  reactions?: Maybe<Array<OpenApiReactionResponse>>;
  replies?: Maybe<Array<OpenApiReplyResponse>>;
  repliesCount?: Maybe<Scalars['Float']['output']>;
  rssGUID?: Maybe<Scalars['String']['output']>;
  snippet?: Maybe<Scalars['String']['output']>;
  subscriptionOnly?: Maybe<Scalars['Boolean']['output']>;
  swellCount?: Maybe<Scalars['Float']['output']>;
  swellcast?: Maybe<OpenApiSwellSwellcastModel>;
  title?: Maybe<Scalars['String']['output']>;
  unlockBtnTxt?: Maybe<Scalars['String']['output']>;
};

export type OpenApiSwellSwellcastModel = {
  capabilities?: Maybe<Array<Scalars['String']['output']>>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ogImage?: Maybe<Scalars['String']['output']>;
  owner?: Maybe<OpenApiSwellcastOwnerModel>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
};

export type OpenApiSwellcastInfoResponse = {
  capabilities?: Maybe<Array<Scalars['String']['output']>>;
  description?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
};

export type OpenApiSwellcastOwnerModel = {
  alias?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
};

export type OpenApiSwellcastResponse = {
  canPost?: Maybe<Scalars['Boolean']['output']>;
  canSubscribe: Scalars['Boolean']['output'];
  capabilities?: Maybe<Array<Scalars['String']['output']>>;
  createdOn?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  followersCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ogImage?: Maybe<Scalars['String']['output']>;
  owner?: Maybe<OpenApiOwnerModel>;
  podcastData?: Maybe<Array<PodcastDataResponse>>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  preferredStations?: Maybe<Array<Scalars['String']['output']>>;
  promptCount?: Maybe<Scalars['Float']['output']>;
  subscriptionState?: Maybe<SubscriptionStatus>;
  swellCount?: Maybe<Scalars['Float']['output']>;
  swells?: Maybe<Array<OpenApiSwellcastSwellResponse>>;
  unheardCount?: Maybe<Scalars['Float']['output']>;
};

export type OpenApiSwellcastSectionModel = {
  label?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  param?: Maybe<HomePageActionParamsModel>;
  sectionType: SwellcastSectionType;
  swellcast: OpenApiSwellcastWithSwellResponse;
  type?: Maybe<Scalars['String']['output']>;
};

export type OpenApiSwellcastSectionsResponse = {
  sections: Array<OpenApiSwellcastSectionModel>;
  swellId: Scalars['String']['output'];
};

export type OpenApiSwellcastSwellFacesResponse = {
  alias?: Maybe<Scalars['String']['output']>;
  url?: Maybe<Scalars['String']['output']>;
};

export type OpenApiSwellcastSwellResponse = {
  articles?: Maybe<Array<ArticleType>>;
  audio?: Maybe<OpenApiAudioModel>;
  author?: Maybe<OpenApiAuthorModel>;
  canonicalId?: Maybe<Scalars['String']['output']>;
  categories?: Maybe<Array<OpenApiCategoryModel>>;
  createdOn?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  faces?: Maybe<Array<OpenApiSwellcastSwellFacesResponse>>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  isQAndA?: Maybe<Scalars['Boolean']['output']>;
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  lastRepliedOn?: Maybe<Scalars['String']['output']>;
  pinState?: Maybe<PinStatus>;
  promptId?: Maybe<Scalars['String']['output']>;
  promptSlug?: Maybe<Scalars['String']['output']>;
  reactions?: Maybe<Array<OpenApiReactionResponse>>;
  repliesCount?: Maybe<Scalars['Float']['output']>;
  snippet?: Maybe<Scalars['String']['output']>;
  subscriptionOnly?: Maybe<Scalars['Boolean']['output']>;
  swellcast?: Maybe<OpenApiSwellcastInfoResponse>;
  swellcastOwner?: Maybe<OpenApiSwellcastOwnerModel>;
  title?: Maybe<Scalars['String']['output']>;
};

/** OpenAPISwellcastType */
export enum OpenApiSwellcastType {
  Category = 'CATEGORY',
  ConversationPublic = 'CONVERSATION_PUBLIC',
  Swellcast = 'SWELLCAST'
}

export type OpenApiSwellcastWithSwellResponse = {
  blockingState: BlockStatus;
  cohostState?: Maybe<CohostingStatus>;
  cohostingState?: Maybe<CohostingStatus>;
  cohostsCount?: Maybe<Scalars['Float']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  followersCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onlyAdminCanPost?: Maybe<Scalars['Boolean']['output']>;
  owner?: Maybe<OpenApiSwellcastOwnerModel>;
  subscribersCount?: Maybe<Scalars['Float']['output']>;
  subscriptionState?: Maybe<SubscriptionStatus>;
  swellCount: Scalars['Float']['output'];
  swells: Array<OpenApiSwellcastSwellResponse>;
  type: SwellcastType;
  unheardCount?: Maybe<Scalars['Float']['output']>;
};

/** OpenSwellcastType */
export enum OpenSwellcastType {
  Default = 'DEFAULT',
  Page = 'PAGE'
}

export type ParamModel = {
  key?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type PaymentGatewayResponse = {
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  isGlobalPG: Scalars['Boolean']['output'];
  isIndianPG: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
};

export type PaymentIntentResponse = {
  appId: Scalars['String']['output'];
  image: Scalars['String']['output'];
  intent: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type PgIntervalsRequest = {
  APPLE: RecurringInterval;
  GOOGLE: RecurringInterval;
  PHONEPE: RecurringInterval;
  STRIPE: RecurringInterval;
  SWELL: RecurringInterval;
};

export type PgIntervalsResponse = {
  APPLE: RecurringInterval;
  GOOGLE: RecurringInterval;
  PHONEPE: RecurringInterval;
  STRIPE: RecurringInterval;
  SWELL: RecurringInterval;
};

export type PgPeriodsRequest = {
  APPLE: Scalars['Float']['input'];
  GOOGLE: Scalars['Float']['input'];
  PHONEPE: Scalars['Float']['input'];
  STRIPE: Scalars['Float']['input'];
  SWELL: Scalars['Float']['input'];
};

export type PgPeriodsResponse = {
  APPLE: Scalars['Float']['output'];
  GOOGLE: Scalars['Float']['output'];
  PHONEPE: Scalars['Float']['output'];
  STRIPE: Scalars['Float']['output'];
  SWELL: Scalars['Float']['output'];
};

export type PgProductIdsRequest = {
  APPLE?: InputMaybe<Scalars['String']['input']>;
  GOOGLE?: InputMaybe<Scalars['String']['input']>;
  PHONEPE?: InputMaybe<Scalars['String']['input']>;
  STRIPE?: InputMaybe<Scalars['String']['input']>;
  SWELL?: InputMaybe<Scalars['String']['input']>;
};

export type PgProductIdsResponse = {
  APPLE?: Maybe<Scalars['String']['output']>;
  GOOGLE?: Maybe<Scalars['String']['output']>;
  PHONEPE?: Maybe<Scalars['String']['output']>;
  STRIPE?: Maybe<Scalars['String']['output']>;
  SWELL?: Maybe<Scalars['String']['output']>;
};

/** PinStatus */
export enum PinStatus {
  Pinned = 'PINNED',
  Unpinned = 'UNPINNED'
}

export type PlanInfoStatusResponse = {
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<PlanState>;
};

export type PlanMinimalResponse = {
  description: Scalars['String']['output'];
  frequency: RecurringInterval;
  hasActiveSubscription: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  isConsumable: Scalars['Boolean']['output'];
  pgId: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type PlanProductResponse = {
  currency: CurrencyResponse;
  description?: Maybe<Scalars['String']['output']>;
  freeTrialDays?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  price: PriceResponse;
  title?: Maybe<Scalars['String']['output']>;
};

export type PlanResponse = {
  active: Scalars['Boolean']['output'];
  capabilities: Array<Scalars['String']['output']>;
  createdOn: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  frequency: PgIntervalsResponse;
  gracePeriod: PgPeriodsResponse;
  groupId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  lastUpdatedOn: Scalars['DateTime']['output'];
  onholdPeriod: PgPeriodsResponse;
  pausePeriod: PgPeriodsResponse;
  supportedProductIds: PgProductIdsResponse;
  title: Scalars['String']['output'];
};

/** PlanState */
export enum PlanState {
  Active = 'ACTIVE',
  Canceled = 'CANCELED',
  Ended = 'ENDED',
  Ingraceperiod = 'INGRACEPERIOD',
  Onhold = 'ONHOLD',
  Paused = 'PAUSED',
  Pending = 'PENDING'
}

export type PodcastAppsResponse = {
  id: Scalars['String']['output'];
  imageUrl: Scalars['String']['output'];
  name: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type PodcastDataResponse = {
  apps?: Maybe<Array<PodcastAppsResponse>>;
  coverImage?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  filterByTag?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  includeReplies: Scalars['Boolean']['output'];
  includeSwellDescription: Scalars['Boolean']['output'];
  omitDefaultSwell: Scalars['Boolean']['output'];
  rssVersion?: Maybe<Scalars['String']['output']>;
};

/** PodcastSubType */
export enum PodcastSubType {
  Free = 'FREE',
  Pro = 'PRO'
}

/** PodcastType */
export enum PodcastType {
  Community = 'COMMUNITY',
  Personal = 'PERSONAL'
}

export type PopularQuote = {
  author?: Maybe<Scalars['String']['output']>;
  quote?: Maybe<Scalars['String']['output']>;
};

export type PriceResponse = {
  currency: CurrencyResponse;
  customInterval?: Maybe<Scalars['String']['output']>;
  fullPrice?: Maybe<FullPrice>;
  id: Scalars['String']['output'];
  interval?: Maybe<RecurringInterval>;
  monthlyValue?: Maybe<Scalars['String']['output']>;
  type: PriceType;
  value: Scalars['String']['output'];
};

/** PriceType */
export enum PriceType {
  OneTime = 'ONE_TIME',
  Recurring = 'RECURRING',
  Rent = 'RENT'
}

export type ProductMinimalResponse = {
  active: Scalars['Boolean']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
};

export type ProductResponse = {
  defaultPriceId?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  freeTrialDays?: Maybe<Scalars['Float']['output']>;
  hasPausedSubscription: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  prices?: Maybe<Array<PriceResponse>>;
  supportedPG?: Maybe<Array<PaymentGatewayResponse>>;
};

export type PromptBuildParametersInput = {
  allowedResponseTypes?: InputMaybe<Array<AllowedPromptResponseTypes>>;
  isCustomBackgroundImage?: InputMaybe<Scalars['Boolean']['input']>;
  promptBackgroundImage: Scalars['String']['input'];
  promptCreditSwellcastId?: InputMaybe<Scalars['String']['input']>;
  promptText: Scalars['String']['input'];
  promptTitle: Scalars['String']['input'];
  textFontSize?: InputMaybe<Scalars['Float']['input']>;
  thumbnail?: InputMaybe<Scalars['String']['input']>;
  titleFontSize?: InputMaybe<Scalars['Float']['input']>;
  utmCampaign?: InputMaybe<Scalars['String']['input']>;
  utmMedium?: InputMaybe<Scalars['String']['input']>;
  utmSource?: InputMaybe<Scalars['String']['input']>;
};

export type PromptBuildParametersOutput = {
  allowedResponseTypes?: Maybe<Array<AllowedPromptResponseTypes>>;
  isCustomBackgroundImage: Scalars['Boolean']['output'];
  promptBackgroundImage: Scalars['String']['output'];
  promptCreditSwellcastId?: Maybe<Scalars['String']['output']>;
  promptText: Scalars['String']['output'];
  promptTitle: Scalars['String']['output'];
  textFontSize?: Maybe<Scalars['Float']['output']>;
  thumbnail?: Maybe<Scalars['String']['output']>;
  titleFontSize?: Maybe<Scalars['Float']['output']>;
  utmCampaign?: Maybe<Scalars['String']['output']>;
  utmMedium?: Maybe<Scalars['String']['output']>;
  utmSource?: Maybe<Scalars['String']['output']>;
};

export type PromptRecordParametersInput = {
  categoryId?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  disableAutoPost?: InputMaybe<Scalars['Boolean']['input']>;
  isPanel?: InputMaybe<Scalars['Boolean']['input']>;
  isPremium?: InputMaybe<Scalars['Boolean']['input']>;
  isQAndA?: InputMaybe<Scalars['Boolean']['input']>;
  isReply?: InputMaybe<Scalars['Boolean']['input']>;
  languageCode?: InputMaybe<Scalars['String']['input']>;
  swellId?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type PromptRecordParametersOutput = {
  categoryId?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  disableAutoPost: Scalars['Boolean']['output'];
  isPanel: Scalars['Boolean']['output'];
  isPremium: Scalars['Boolean']['output'];
  isQAndA: Scalars['Boolean']['output'];
  isReply: Scalars['Boolean']['output'];
  languageCode?: Maybe<Scalars['String']['output']>;
  swellId?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type PromptResponse = {
  allowedResponseTypes?: Maybe<Array<AllowedPromptResponseTypes>>;
  altText: Scalars['String']['output'];
  createdOn: Scalars['DateTime']['output'];
  credit?: Maybe<AuthorModel>;
  editorsPickSetOn?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  isEditorsPick?: Maybe<Scalars['Boolean']['output']>;
  postCount: Scalars['Float']['output'];
  promptImageUrl: Scalars['String']['output'];
  promptLink: Scalars['String']['output'];
  promptText: Scalars['String']['output'];
  promptTitle: Scalars['String']['output'];
  slug?: Maybe<Scalars['String']['output']>;
  swellId?: Maybe<Scalars['String']['output']>;
  swellcast: SwellcastResponse;
};

export type PurchaseDetailsResponse = {
  active?: Maybe<Scalars['Boolean']['output']>;
  expiryDate?: Maybe<Scalars['DateTime']['output']>;
  purchaseCurrency?: Maybe<CurrencyResponse>;
  purchaseDate?: Maybe<Scalars['DateTime']['output']>;
  purchasePrice?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  fat: FetchAudioTextResponse;
  getBrowsePage: OpenApiPromoWithSectionsResponse;
  getCommunitiesPage: OpenApiPromoWithSectionsResponse;
  getFeaturedConversations?: Maybe<OpenApiFeaturedConvResponse>;
  getHomePage: OpenApiHomePageResponse;
  getHomePageV2: OpenApiPromoWithSectionsResponse;
  getLatestVideos: Array<OpenApiLatestVideoResponse>;
  getPopularInfo: OpenApiPopularInfoResponse;
  getProfilePage: OpenApiProfileWithSectionsResponse;
  getPromptPage: OpenApiPromptWithSectionsResponse;
  getSearchPage: OpenApiSearchPageResponse;
  getSearchResultsPage: OpenApiSearchWithSectionsResponse;
  getSeeAll: OpenApiSeeAllWithSectionResponse;
  getStation: OpenApiStationResponse;
  getStationPage: OpenApiStationWithSectionsResponse;
  getSwellcast?: Maybe<OpenApiSwellcastResponse>;
  listCategory?: Maybe<Array<OpenApiCategoryModel>>;
  listReplyBySwell?: Maybe<Array<OpenApiReplyResponse>>;
  listStationSwells: OpenApiStationSwellsResponse;
  listSwellSuggestions: OpenApiSwellcastSectionsResponse;
  loadSwellById?: Maybe<OpenApiSwellResponse>;
  ping: Scalars['String']['output'];
  search?: Maybe<OpenApiSwellcastResponse>;
};


export type QueryFatArgs = {
  url: Scalars['String']['input'];
};


export type QueryGetBrowsePageArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  sinceSectionId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetCommunitiesPageArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  sinceSectionId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetHomePageArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetHomePageV2Args = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  sinceSectionId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetLatestVideosArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  refreshCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetPopularInfoArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetProfilePageArgs = {
  alias?: InputMaybe<Scalars['String']['input']>;
  config?: InputMaybe<Scalars['String']['input']>;
  countryCode?: InputMaybe<Scalars['String']['input']>;
  hashtagFilter?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  sinceSectionId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetPromptPageArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  sinceSectionId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSearchPageArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  refreshCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetSearchResultsPageArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  searchTerm: Scalars['String']['input'];
  sinceSectionId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSeeAllArgs = {
  limit: Scalars['Float']['input'];
  params: Scalars['String']['input'];
  sectionType: SeeAllSectionType;
  sinceId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetStationArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  flags?: InputMaybe<Array<SwellFlags>>;
  id: Scalars['String']['input'];
  type: StationType;
};


export type QueryGetStationPageArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  sinceSectionId?: InputMaybe<Scalars['String']['input']>;
  type: StationType;
};


export type QueryGetSwellcastArgs = {
  alias?: InputMaybe<Scalars['String']['input']>;
  categoryId?: InputMaybe<Scalars['String']['input']>;
  flags?: InputMaybe<Array<SwellFlags>>;
  hashtagFilter?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Float']['input'];
  offset: Scalars['Float']['input'];
  sortOrder?: InputMaybe<SortOrder>;
  type?: InputMaybe<OpenApiSwellcastType>;
};


export type QueryListReplyBySwellArgs = {
  canonicalId?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Float']['input'];
  offset?: InputMaybe<Scalars['Float']['input']>;
  since?: InputMaybe<Scalars['String']['input']>;
};


export type QueryListStationSwellsArgs = {
  countryCode?: InputMaybe<Scalars['String']['input']>;
  flags?: InputMaybe<Array<SwellFlags>>;
  id: Scalars['String']['input'];
  limit: Scalars['Float']['input'];
  since?: InputMaybe<Scalars['String']['input']>;
  type: StationType;
};


export type QueryListSwellSuggestionsArgs = {
  id: Scalars['String']['input'];
};


export type QueryLoadSwellByIdArgs = {
  id: Scalars['String']['input'];
  refreshCache?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QuerySearchArgs = {
  flags?: InputMaybe<Array<SwellFlags>>;
  limit: Scalars['Float']['input'];
  offset: Scalars['Float']['input'];
  term?: InputMaybe<Scalars['String']['input']>;
};

/** ReactState */
export enum ReactState {
  Notpressed = 'NOTPRESSED',
  Pressed = 'PRESSED'
}

export type ReactionResponse = {
  count?: Maybe<Scalars['Float']['output']>;
  pressState?: Maybe<ReactState>;
  reaction?: Maybe<Scalars['String']['output']>;
};

/** RecurringInterval */
export enum RecurringInterval {
  Annual = 'ANNUAL',
  Custom = 'CUSTOM',
  Month = 'MONTH',
  Quarter = 'QUARTER'
}

export type ReplyModel = {
  audio: AudioType;
  author: AuthorModel;
  canonicalId?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  message?: Maybe<Scalars['String']['output']>;
  promptId?: Maybe<Scalars['String']['output']>;
  snippet?: Maybe<TranscribeSnippetResponse>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
};

export type ReplyResponse = {
  articles?: Maybe<Array<ArticleType>>;
  audio?: Maybe<AudioType>;
  author?: Maybe<AuthorModel>;
  canonicalId?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  isParent?: Maybe<Scalars['Boolean']['output']>;
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  message?: Maybe<Scalars['String']['output']>;
  parentId?: Maybe<Scalars['String']['output']>;
  promptId?: Maybe<Scalars['String']['output']>;
  reactions?: Maybe<Array<ReactionResponse>>;
  snippet?: Maybe<TranscribeSnippetResponse>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
};

export type SeeAllSectionResponse = {
  authors: Array<AuthorModel>;
  emptyMessage?: Maybe<Scalars['String']['output']>;
  hashtags: Array<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  images: Array<ImageResponse>;
  label?: Maybe<Scalars['String']['output']>;
  promos: Array<HomePageHeaderModel>;
  prompts: Array<PromptResponse>;
  sectionDataType?: Maybe<HomePageSectionDataType>;
  sectionParams?: Maybe<HomePageSectionParamsModel>;
  sectionType: Scalars['String']['output'];
  stations: Array<StationInfoResponse>;
  swellcasts: Array<SwellcastResponse>;
  swells: Array<SwellWithReplyResponse>;
  type: HomePageRenderType;
};

/** SeeAllSectionType */
export enum SeeAllSectionType {
  BrowseFeatured = 'BROWSE_FEATURED',
  BrowseLatestCommunityPrompts = 'BROWSE_LATEST_COMMUNITY_PROMPTS',
  BrowseSwells = 'BROWSE_SWELLS',
  BrowseTopSpeakers = 'BROWSE_TOP_SPEAKERS',
  CommunitiesIFollow = 'COMMUNITIES_I_FOLLOW',
  CommunityByStation = 'COMMUNITY_BY_STATION',
  FeaturedCommunity = 'FEATURED_COMMUNITY',
  FeaturedCommunityAuthors = 'FEATURED_COMMUNITY_AUTHORS',
  FeaturedPersonalAuthors = 'FEATURED_PERSONAL_AUTHORS',
  FeaturedPersonalSwellcasts = 'FEATURED_PERSONAL_SWELLCASTS',
  FeaturedPrompts = 'FEATURED_PROMPTS',
  Hashtagged = 'HASHTAGGED',
  PodcastSwells = 'PODCAST_SWELLS',
  ProfileAll = 'PROFILE_ALL',
  ProfileMyPosts = 'PROFILE_MY_POSTS',
  ProfilePosts = 'PROFILE_POSTS',
  ProfilePostsInOthers = 'PROFILE_POSTS_IN_OTHERS',
  ProfilePrompts = 'PROFILE_PROMPTS',
  ProfileRepliedIn = 'PROFILE_REPLIED_IN',
  Prompts = 'PROMPTS',
  PromptSwells = 'PROMPT_SWELLS',
  SearchCommunity = 'SEARCH_COMMUNITY',
  SearchPeople = 'SEARCH_PEOPLE',
  SearchPosts = 'SEARCH_POSTS',
  SearchPrompts = 'SEARCH_PROMPTS',
  StationCommunityPrompts = 'STATION_COMMUNITY_PROMPTS',
  StationFirstTimePosts = 'STATION_FIRST_TIME_POSTS',
  StationPosts = 'STATION_POSTS',
  StationPostsWorldover = 'STATION_POSTS_WORLDOVER',
  StationTopAuthors = 'STATION_TOP_AUTHORS',
  StationTrendingSwells = 'STATION_TRENDING_SWELLS'
}

/** SortOrder */
export enum SortOrder {
  CreatedOn = 'CREATED_ON',
  Default = 'DEFAULT',
  EditorsPickSetOn = 'EDITORS_PICK_SET_ON',
  InvitedOn = 'INVITED_ON',
  LastRepliedOn = 'LAST_REPLIED_ON',
  NoSort = 'NO_SORT',
  PremiumOn = 'PREMIUM_ON',
  RepliedOn = 'REPLIED_ON'
}

export type StationInfoResponse = {
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  ogImage?: Maybe<Scalars['String']['output']>;
  type: StationType;
};

/** StationType */
export enum StationType {
  Category = 'CATEGORY',
  Country = 'COUNTRY',
  Language = 'LANGUAGE'
}

/** Status */
export enum Status {
  Active = 'ACTIVE',
  DeleteInitiated = 'DELETE_INITIATED',
  Inactive = 'INACTIVE',
  MarkedForDeletion = 'MARKED_FOR_DELETION'
}

/** SubscriptionStatus */
export enum SubscriptionStatus {
  Disabled = 'DISABLED',
  Enabled = 'ENABLED',
  NotSubscribed = 'NOT_SUBSCRIBED',
  Subscribed = 'SUBSCRIBED'
}

/** SubscriptionType */
export enum SubscriptionType {
  CommunityPage = 'COMMUNITY_PAGE',
  Swell = 'SWELL',
  Swellcast = 'SWELLCAST'
}

export type SwellCardInfo = {
  cardStyle: SwellCardStyle;
  message?: Maybe<Scalars['String']['output']>;
};

/** SwellCardStyle */
export enum SwellCardStyle {
  Expanded = 'EXPANDED',
  Simple = 'SIMPLE'
}

/** SwellFlags */
export enum SwellFlags {
  ConvoPublicReplyOnly = 'CONVO_PUBLIC_REPLY_ONLY',
  ExcludeHeard = 'EXCLUDE_HEARD',
  ExcludeMasterHeard = 'EXCLUDE_MASTER_HEARD',
  ExcludeMyZone = 'EXCLUDE_MY_ZONE',
  FilterForHomeFeed = 'FILTER_FOR_HOME_FEED',
  HeardCalcByTime = 'HEARD_CALC_BY_TIME',
  IncludeNewbie = 'INCLUDE_NEWBIE',
  IsFirstPage = 'IS_FIRST_PAGE',
  LastHeardId = 'LAST_HEARD_ID',
  ListenSummary = 'LISTEN_SUMMARY',
  NextReplyToHear = 'NEXT_REPLY_TO_HEAR',
  OnlyEditorsPick = 'ONLY_EDITORS_PICK',
  OnlyGraph = 'ONLY_GRAPH',
  PremiumAudio = 'PREMIUM_AUDIO',
  PurchaseDetails = 'PURCHASE_DETAILS',
  RssFilter = 'RSS_FILTER',
  SkipGraph = 'SKIP_GRAPH',
  SkipLanguageFilter = 'SKIP_LANGUAGE_FILTER',
  SkipOwn = 'SKIP_OWN',
  SkipSwellcast = 'SKIP_SWELLCAST',
  SkipUnpaidPremium = 'SKIP_UNPAID_PREMIUM',
  SkipUntracked = 'SKIP_UNTRACKED',
  SkipZoneBoosting = 'SKIP_ZONE_BOOSTING',
  SkipZoneFilter = 'SKIP_ZONE_FILTER',
  Snippets = 'SNIPPETS',
  SwellcastInfo = 'SWELLCAST_INFO',
  SwellPrice = 'SWELL_PRICE',
  UnheardCount = 'UNHEARD_COUNT'
}

export type SwellItemCount = {
  count: Scalars['Float']['output'];
  percentage: Scalars['Float']['output'];
};

export type SwellItemCounts = {
  id: Scalars['String']['output'];
  listened: Array<SwellItemCount>;
  type: Scalars['String']['output'];
};

export type SwellPriceProduct = {
  id: Scalars['String']['output'];
  prices: Array<PriceResponse>;
};

export type SwellResponse = {
  adminSkipTags?: Maybe<Array<Scalars['String']['output']>>;
  adminTags?: Maybe<Array<Scalars['String']['output']>>;
  articles?: Maybe<Array<ArticleType>>;
  audio?: Maybe<AudioType>;
  audioSource?: Maybe<AudioSource>;
  author?: Maybe<AuthorModel>;
  boost?: Maybe<Scalars['Float']['output']>;
  canonicalId?: Maybe<Scalars['String']['output']>;
  categories?: Maybe<Array<CategoryResponse>>;
  configuredPrice?: Maybe<Array<PriceResponse>>;
  contributors?: Maybe<Array<Scalars['String']['output']>>;
  countryBasedPromotionScore?: Maybe<Scalars['Float']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  editorsPickSetOn?: Maybe<Scalars['DateTime']['output']>;
  featuredOn?: Maybe<Scalars['DateTime']['output']>;
  heardStatus?: Maybe<HeardStatus>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['String']['output'];
  invitees?: Maybe<Array<Scalars['String']['output']>>;
  isQAndA?: Maybe<Scalars['Boolean']['output']>;
  isRental?: Maybe<Scalars['Boolean']['output']>;
  joinCode?: Maybe<Scalars['String']['output']>;
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  languages?: Maybe<Array<Scalars['String']['output']>>;
  lastRepliedOn?: Maybe<Scalars['DateTime']['output']>;
  listenSummary?: Maybe<Array<SwellItemCount>>;
  newUserPromotionScore?: Maybe<Scalars['Float']['output']>;
  participated?: Maybe<Scalars['Boolean']['output']>;
  pinState?: Maybe<PinStatus>;
  premiumJoinCode?: Maybe<Scalars['String']['output']>;
  price?: Maybe<PriceResponse>;
  promotedCountries?: Maybe<Array<Scalars['String']['output']>>;
  promotionScore?: Maybe<Scalars['Float']['output']>;
  promptId?: Maybe<Scalars['String']['output']>;
  promptSlug?: Maybe<Scalars['String']['output']>;
  purchaseDetails?: Maybe<PurchaseDetailsResponse>;
  reactions?: Maybe<Array<ReactionResponse>>;
  recommendationId?: Maybe<Scalars['String']['output']>;
  replies?: Maybe<Array<ReplyModel>>;
  repliesCount?: Maybe<Scalars['Float']['output']>;
  replyDeeplinkId?: Maybe<Scalars['String']['output']>;
  rssGUID?: Maybe<Scalars['String']['output']>;
  shareURL?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  snippet?: Maybe<TranscribeSnippetResponse>;
  subscriptionOnly?: Maybe<Scalars['Boolean']['output']>;
  swellcastId?: Maybe<Scalars['String']['output']>;
  swellcastInfo?: Maybe<SwellcastInfoResponse>;
  swellcastOwner?: Maybe<SwellcastOwnerModelWithoutCounts>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  title?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
  unlocked?: Maybe<Scalars['Boolean']['output']>;
  visibility?: Maybe<VisibleType>;
};

export type SwellStatsCountResponse = {
  listenCount?: Maybe<Scalars['Float']['output']>;
  reactionCount?: Maybe<Scalars['Float']['output']>;
};

export type SwellStatsItemsResponse = {
  reply?: Maybe<SwellStatsCountResponse>;
  swell?: Maybe<SwellStatsCountResponse>;
};

/** SwellType */
export enum SwellType {
  Baditem = 'BADITEM',
  Reply = 'REPLY',
  Swell = 'SWELL'
}

export type SwellWithReplyResponse = {
  activities?: Maybe<Array<ActivityMinimalResponse>>;
  adminSkipTags?: Maybe<Array<Scalars['String']['output']>>;
  adminTags?: Maybe<Array<Scalars['String']['output']>>;
  articles?: Maybe<Array<ArticleType>>;
  audio?: Maybe<AudioType>;
  audioSource?: Maybe<AudioSource>;
  author?: Maybe<AuthorModel>;
  boost?: Maybe<Scalars['Float']['output']>;
  canonicalId?: Maybe<Scalars['String']['output']>;
  cardInfo?: Maybe<SwellCardInfo>;
  categories?: Maybe<Array<CategoryResponse>>;
  configuredPrice?: Maybe<Array<PriceResponse>>;
  contributors?: Maybe<Array<Scalars['String']['output']>>;
  countryBasedPromotionScore?: Maybe<Scalars['Float']['output']>;
  countryCode?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  editorsPickSetOn?: Maybe<Scalars['DateTime']['output']>;
  featuredOn?: Maybe<Scalars['DateTime']['output']>;
  heardStatus?: Maybe<HeardStatus>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  homePageSectionType?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  invitees?: Maybe<Array<Scalars['String']['output']>>;
  isQAndA?: Maybe<Scalars['Boolean']['output']>;
  isRental?: Maybe<Scalars['Boolean']['output']>;
  joinCode?: Maybe<Scalars['String']['output']>;
  keywords?: Maybe<Array<Scalars['String']['output']>>;
  languages?: Maybe<Array<Scalars['String']['output']>>;
  lastRepliedOn?: Maybe<Scalars['DateTime']['output']>;
  listenSummary?: Maybe<Array<SwellItemCount>>;
  newUserPromotionScore?: Maybe<Scalars['Float']['output']>;
  participated?: Maybe<Scalars['Boolean']['output']>;
  pinState?: Maybe<PinStatus>;
  premiumJoinCode?: Maybe<Scalars['String']['output']>;
  price?: Maybe<PriceResponse>;
  promotedCountries?: Maybe<Array<Scalars['String']['output']>>;
  promotionScore?: Maybe<Scalars['Float']['output']>;
  promptId?: Maybe<Scalars['String']['output']>;
  promptSlug?: Maybe<Scalars['String']['output']>;
  purchaseDetails?: Maybe<PurchaseDetailsResponse>;
  reactions?: Maybe<Array<ReactionResponse>>;
  recommendationId?: Maybe<Scalars['String']['output']>;
  replies?: Maybe<Array<ReplyModel>>;
  repliesCount?: Maybe<Scalars['Float']['output']>;
  reply?: Maybe<ReplyResponse>;
  replyDeeplinkId?: Maybe<Scalars['String']['output']>;
  rssGUID?: Maybe<Scalars['String']['output']>;
  shareURL?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  snippet?: Maybe<TranscribeSnippetResponse>;
  subscriptionOnly?: Maybe<Scalars['Boolean']['output']>;
  swellcastId?: Maybe<Scalars['String']['output']>;
  swellcastInfo?: Maybe<SwellcastInfoResponse>;
  swellcastOwner?: Maybe<SwellcastOwnerModelWithoutCounts>;
  tags?: Maybe<Array<Scalars['String']['output']>>;
  title?: Maybe<Scalars['String']['output']>;
  type: SwellType;
  unlocked?: Maybe<Scalars['Boolean']['output']>;
  visibility?: Maybe<VisibleType>;
};

export type SwellcastInfoResponse = {
  blockState?: Maybe<BlockStatus>;
  blockingState?: Maybe<BlockStatus>;
  capabilities?: Maybe<Array<Scalars['String']['output']>>;
  cohostingState?: Maybe<CohostingStatus>;
  description?: Maybe<Scalars['String']['output']>;
  followingState?: Maybe<FollowingStatus>;
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  subscriptionState?: Maybe<SubscriptionStatus>;
  type?: Maybe<SwellcastType>;
};

export type SwellcastMinimalResponse = {
  cohostState?: Maybe<CohostingStatus>;
  cohostingState?: Maybe<CohostingStatus>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  followingState: FollowingStatus;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ogImage?: Maybe<Scalars['String']['output']>;
  owner: SwellcastOwnerModelWithoutCounts;
  podcastData?: Maybe<Array<PodcastDataResponse>>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  subscriptionState?: Maybe<SubscriptionStatus>;
  type: SwellcastType;
};

export type SwellcastOwnerModel = {
  alias?: Maybe<Scalars['String']['output']>;
  audio?: Maybe<AudioType>;
  badge?: Maybe<Scalars['String']['output']>;
  cohostingCount?: Maybe<Scalars['Float']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  followingCount?: Maybe<Scalars['Float']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  languages?: Maybe<Array<Scalars['String']['output']>>;
  lastName?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  readCountryCode?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  swellcastDescription?: Maybe<Scalars['String']['output']>;
  swellcastId: Scalars['String']['output'];
  writeCountryCode?: Maybe<Scalars['String']['output']>;
};

export type SwellcastOwnerModelWithoutCounts = {
  alias?: Maybe<Scalars['String']['output']>;
  audio?: Maybe<AudioType>;
  badge?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  status?: Maybe<Scalars['String']['output']>;
  swellcastDescription?: Maybe<Scalars['String']['output']>;
  swellcastId: Scalars['String']['output'];
};

export type SwellcastResponse = {
  blockState?: Maybe<BlockStatus>;
  blockingState?: Maybe<BlockStatus>;
  capabilities?: Maybe<Array<Scalars['String']['output']>>;
  cohostState?: Maybe<CohostingStatus>;
  cohostingState?: Maybe<CohostingStatus>;
  cohostsCount?: Maybe<Scalars['Float']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  followersCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastPromptCreatedOn?: Maybe<Scalars['DateTime']['output']>;
  lastSwellCreatedOn?: Maybe<Scalars['DateTime']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  ogImage?: Maybe<Scalars['String']['output']>;
  onlyAdminCanPost?: Maybe<Scalars['Boolean']['output']>;
  openSwellcastType?: Maybe<OpenSwellcastType>;
  owner?: Maybe<SwellcastOwnerModel>;
  pageId?: Maybe<Scalars['String']['output']>;
  planIds?: Maybe<Array<Scalars['String']['output']>>;
  podcastData?: Maybe<Array<PodcastDataResponse>>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  preferredStations?: Maybe<Array<Scalars['String']['output']>>;
  product?: Maybe<ProductMinimalResponse>;
  promptCount?: Maybe<Scalars['Float']['output']>;
  subscribersCount?: Maybe<Scalars['Float']['output']>;
  subscriptionState?: Maybe<SubscriptionStatus>;
  swellCount?: Maybe<Scalars['Float']['output']>;
  type: SwellcastType;
  unheardCount?: Maybe<Scalars['Float']['output']>;
};

export type SwellcastSectionModel = {
  label?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  param?: Maybe<HomePageActionParamsModel>;
  sectionType: SwellcastSectionType;
  swellcast: SwellcastWithSwellResponse;
  type?: Maybe<Scalars['String']['output']>;
};

/** SwellcastSectionType */
export enum SwellcastSectionType {
  AuthorSwells = 'AUTHOR_SWELLS',
  CategorySwells = 'CATEGORY_SWELLS',
  CountrySwells = 'COUNTRY_SWELLS',
  HashtagSwells = 'HASHTAG_SWELLS',
  LanguageSwells = 'LANGUAGE_SWELLS',
  OwnerSwells = 'OWNER_SWELLS',
  PopularSwells = 'POPULAR_SWELLS',
  RecommendationSwells = 'RECOMMENDATION_SWELLS'
}

/** SwellcastType */
export enum SwellcastType {
  Baditem = 'BADITEM',
  Group = 'GROUP',
  Public = 'PUBLIC'
}

export type SwellcastWithSwellResponse = {
  blockingState: BlockStatus;
  cohostState?: Maybe<CohostingStatus>;
  cohostingState?: Maybe<CohostingStatus>;
  cohostsCount?: Maybe<Scalars['Float']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  followersCount?: Maybe<Scalars['Float']['output']>;
  followingState?: Maybe<FollowingStatus>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  onlyAdminCanPost?: Maybe<Scalars['Boolean']['output']>;
  owner?: Maybe<SwellcastOwnerModel>;
  podcastData?: Maybe<Array<PodcastDataResponse>>;
  podcastSubType: PodcastSubType;
  podcastType: PodcastType;
  subscribersCount?: Maybe<Scalars['Float']['output']>;
  subscriptionState?: Maybe<SubscriptionStatus>;
  swellCount: Scalars['Float']['output'];
  swells: Array<SwellResponse>;
  type: SwellcastType;
  unheardCount?: Maybe<Scalars['Float']['output']>;
};

export type TalkingCircle = {
  remainingTimeSec: Scalars['Float']['output'];
  totalExpiryTimeSec: Scalars['Float']['output'];
};

export type TextWithStartEnd = {
  end: Scalars['Float']['output'];
  start: Scalars['Float']['output'];
  text: Scalars['String']['output'];
};

export type TranscribeSnippetResponse = {
  text: Scalars['String']['output'];
  url?: Maybe<Scalars['String']['output']>;
};

export type UploadFileRequest = {
  fileName?: InputMaybe<Scalars['String']['input']>;
  filePath: Scalars['String']['input'];
  fileType: Scalars['String']['input'];
  public?: InputMaybe<Scalars['Boolean']['input']>;
  skipPublicRead?: InputMaybe<Scalars['Boolean']['input']>;
  swellcastId?: InputMaybe<Scalars['String']['input']>;
  temp?: InputMaybe<Scalars['Boolean']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type UploadFileResponse = {
  canonicalId?: Maybe<Scalars['String']['output']>;
  code: Scalars['Float']['output'];
  encryptedUrl?: Maybe<Scalars['String']['output']>;
  endpointUrl?: Maybe<Scalars['String']['output']>;
  message: Scalars['String']['output'];
  params?: Maybe<Array<ParamModel>>;
  url?: Maybe<Scalars['String']['output']>;
};

export type UserRegisterAt = {
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
};

export type UserResponse = {
  alias?: Maybe<Scalars['String']['output']>;
  allSinceId?: Maybe<Scalars['String']['output']>;
  audio?: Maybe<AudioType>;
  badge?: Maybe<Scalars['String']['output']>;
  categories?: Maybe<Array<CategoryResponse>>;
  city?: Maybe<Scalars['String']['output']>;
  communityFollowingCount: Scalars['Float']['output'];
  country?: Maybe<Scalars['String']['output']>;
  createdOn?: Maybe<Scalars['DateTime']['output']>;
  dailyDigestSentOn?: Maybe<Scalars['DateTime']['output']>;
  dob?: Maybe<Scalars['Float']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  followingCount: Scalars['Float']['output'];
  followingOnlyToInvite: Scalars['Boolean']['output'];
  gender?: Maybe<Gender>;
  groupSinceId?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  isRecommendedSwellcastsSet: Scalars['Boolean']['output'];
  isTOSAgreed: Scalars['Boolean']['output'];
  languages?: Maybe<Array<Scalars['String']['output']>>;
  lastActiveOn?: Maybe<Scalars['DateTime']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  lastSpokenOn?: Maybe<Scalars['DateTime']['output']>;
  notificationLevel?: Maybe<NotificationLevel>;
  paymentCurrency?: Maybe<Scalars['String']['output']>;
  personalFollowingCount: Scalars['Float']['output'];
  phoneNumber?: Maybe<Scalars['String']['output']>;
  previousActiveOn?: Maybe<Scalars['DateTime']['output']>;
  privateSinceId?: Maybe<Scalars['String']['output']>;
  publicSinceId?: Maybe<Scalars['String']['output']>;
  readCountryCode?: Maybe<Scalars['String']['output']>;
  registeredAt?: Maybe<UserRegisterAt>;
  roles?: Maybe<Array<Scalars['String']['output']>>;
  sids?: Maybe<Array<Scalars['String']['output']>>;
  signedUpOn?: Maybe<Scalars['DateTime']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  status: Status;
  useAudioEmojis: Scalars['Boolean']['output'];
  verified: VerificationModel;
  versionTOSAgreed?: Maybe<Scalars['String']['output']>;
  versionTOSLatest?: Maybe<Scalars['String']['output']>;
  writeCountryCode?: Maybe<Scalars['String']['output']>;
};

export type VerificationModel = {
  phoneVerified: Scalars['Boolean']['output'];
};

/** VisibleType */
export enum VisibleType {
  Invisible = 'INVISIBLE',
  Visible = 'VISIBLE'
}
