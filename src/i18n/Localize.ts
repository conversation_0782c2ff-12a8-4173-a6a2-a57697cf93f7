// import { LocalStore } from '../components/swellcast/utils/LocalStore';
import { inBrowser } from '../utils/Utils';

/*

What's happening:

- On load, the script checks for a localize key in localStorage and sets all the exported values accordingly
- To change locale, you have th set the localStorage val to new locale code and refresh


How to use:

import { Localize } from './Localize';

<h1>{Localize.SWELLCAST_EMPTY}</h1>

How to Update:

1) Add lang/location to LOCALE enum
3) Add key for copy to LocalCopy
2) Add Locale to LocalCopyLookup
4) Add values to LocalCopyLookup[LOCALE]

*/

enum LOCALE {
  EN_US = 'en-us',
  SP_US = 'sp-us',
}

const DEFAULT_LOCALE = LOCALE.EN_US;

enum LocalCopy {
  IMAGE_NOT_FOUND = 'IMAGE_NOT_FOUND',
  SWELLCAST_NO_EXIST = 'SWELLCAST_NO_EXIST',
  STATION_NO_EXIST = 'STATION_NO_EXIST',
  STATION_EMPTY = 'STATION_EMPTY',
  SWELL_NO_EXIST = 'SWELL_NO_EXIST',
  SWELLCAST_EMPTY = 'SWELLCAST_EMPTY',
  REPLIES_EMPTY = 'REPLIES_EMPTY',
  PLAYER_ERROR_ALERT = 'PLAYER_ERROR_ALERT',
  CLICK_TO_PLAY = 'CLICK_TO_PLAY',
  LOADING = 'LOADING',
  ERROR = 'ERROR',
  FINISHED_PLAYING = 'FINISHED_PLAYING',
  NOW_PLAYING = 'NOW_PLAYING',
  AUDIO_SRC_ERROR = 'AUDIO_SRC_ERROR',
  GO_TO_SWELLCAST = 'GO_TO_SWELLCAST',
  GENERAL_ERROR = 'GENERAL_ERROR',
  GENERAL_ERROR_DESCRIPTION = 'GENERAL_ERROR_DESCRIPTION',
  FACEBOOK_LINK = 'FACEBOOK_LINK',
  TWITTER_LINK = 'TWITTER_LINK',
  LINKEDIN_LINK = 'LINKEDIN_LINK',
  INSTAGRAM_LINK = 'INSTAGRAM_LINK',
}

type LocalizeMap = { [key in LocalCopy]: string };

type LocalizeLookup = { [key in LOCALE]?: { [key in LocalCopy]?: string } };

const LocalCopyLookup: LocalizeLookup = {
  [LOCALE.EN_US]: {
    [LocalCopy.IMAGE_NOT_FOUND]: 'Image Not Found',
    [LocalCopy.SWELLCAST_EMPTY]: 'This Swellcast has yet to begin!',
    [LocalCopy.STATION_EMPTY]: 'This Station has yet to begin!',
    [LocalCopy.REPLIES_EMPTY]: 'Join the conversation!',
    [LocalCopy.SWELLCAST_NO_EXIST]: 'This Swellcast does not exist',
    [LocalCopy.STATION_NO_EXIST]: 'This Station does not exist',
    [LocalCopy.SWELL_NO_EXIST]: 'This Swell does not exist',
    [LocalCopy.PLAYER_ERROR_ALERT]: 'There was a problem loading this audio. Please refresh and try again. Thanks',
    [LocalCopy.CLICK_TO_PLAY]: 'CLICK TO PLAY',
    [LocalCopy.LOADING]: 'LOADING...',
    [LocalCopy.ERROR]: 'ERROR!',
    [LocalCopy.FINISHED_PLAYING]: 'FINISHED PLAYING',
    [LocalCopy.NOW_PLAYING]: 'NOW PLAYING',
    [LocalCopy.AUDIO_SRC_ERROR]: 'Houston, we have a problem. For some reason, this audio file is not loading. Give it a minute then refresh.',
    [LocalCopy.GENERAL_ERROR]: 'Houston, we have a problem...',
    [LocalCopy.GENERAL_ERROR_DESCRIPTION]: 'Something went wrong :(',
    [LocalCopy.GO_TO_SWELLCAST]: 'Go to Swellcast of $name',
    [LocalCopy.FACEBOOK_LINK]: 'https://www.facebook.com/swelltalk',
    [LocalCopy.TWITTER_LINK]: 'https://twitter.com/intent/follow?source=followbutton&variant=1.0&screen_name=swell_talk',
    [LocalCopy.LINKEDIN_LINK]: 'https://www.linkedin.com/company/swellcast',
    [LocalCopy.INSTAGRAM_LINK]: 'https://www.instagram.com/swell_talk/',
  },
  [LOCALE.SP_US]: {
    [LocalCopy.SWELLCAST_NO_EXIST]: 'Este no existe',
    [LocalCopy.SWELL_NO_EXIST]: 'Este no existe',
  },
};

let locale = DEFAULT_LOCALE;

// check if user has local set - not implemented
if (inBrowser) {
  const key = LOCALE.EN_US;
  if (key) {
    const localeKey = Object.keys(LOCALE).find((k) => LOCALE[k as keyof typeof LOCALE] == key);
    if (localeKey) {
      locale = LOCALE[localeKey as keyof typeof LOCALE];
    }
  }
}

export const Localize: LocalizeMap = Object.keys(LocalCopy).reduce((c, key) => {
  const k = key as keyof typeof LocalCopy;
  c[k] = LocalCopyLookup?.[locale]?.[k] ?? LocalCopyLookup[DEFAULT_LOCALE]?.[k] ?? '';
  return c;
}, {} as LocalizeMap);