import { DehydratedState, QueryClient } from '@tanstack/react-query';
import { HelmetServerState } from 'react-helmet-async';
import { APIType } from '../api/gql.common';
import { OpenApiSwellcastResponseEnhanced } from '../api/gql.getSwellcast';
import { OpenApiSwellResponseEnhanced } from '../api/gql.loadSwellById';
import { CategoryResponse, OpenApiAuthorModel, OpenApiCategoryModel, OpenApiOwnerModel, OpenApiSwellcastOwnerModel, SeeAllSectionType, StationType } from '../generated/graphql';
import { UTMParams } from '../utils/UTM.class';

export enum ColorMode {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto',
}

export interface OpenAPIError {
  message?: string;
  description?: string;
}

export interface IPaginate {
  offset: number;
  limit: number;
  enabled: boolean;
}

// export type IPaginatedPageParams = Partial<IPaginate> & RouteParams;

export enum OS {
  IOS = 'iOS',
  ANDROID = 'Android',
  SERVER = 'server',
  WINDOWS_PHONE = 'Windows Phone',
  UNKNOWN = 'unknown',
}

export enum ILayoutMode {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape',
  SQUARE = 'square',
}

export enum SwellListType {
  SWELLCAST = 'swellcast',
  STATION = 'station',
  COUNTRY = 'country',
  LANGUAGE = 'language',
  WIDGET = 'WIDGET',
}

export const StationTypeMap = {
  [StationType.Category]: 'category',
  [StationType.Country]: 'country',
  [StationType.Language]: 'language',
};

export type RouteParams = {
  id?: string;
  listType?: SwellListType;
  listId?: string;
  canonicalId?: string;
  swellId?: string;
  slug?: string;
  stationId?: string;
  countryId?: string;
  countryCode?: string;
  listSlug?: string;
  langId?: string;
  hash?: string;
  replyId?: string;
  search?: string;
  branch?: string;
  sectionType?: SeeAllSectionType;
  sectionParams?: string;
  promptId?: string;
};

export type RouteQuery = {
  stage?: APIType;
  countryCode?: string;
  search?: string;
  prod?: string;
  offset?: string;
  limit?: string;
};

export enum PlayerStatus {
  NONE,
  PLAYING,
  PAUSED,
  SEEKING,
  LOADING,
  LOADED,
  STOPPED,
  ERROR,
  LOAD_ERROR,
  PLAY_ERROR,
  BLOCKED,
  COMPLETED,
  ENQUEUED,
  RECORDING,
  RECORDING_PAUSED, // not used - fun option
}

export interface OGModel {
  canonicalPath?: string;
  title?: string;
  description?: string;
  image?: string;
  keywords?: string;
}

export interface MasterRef {
  isMaster: boolean;
  params: RouteParams & Partial<IPaginate>;
  index?: number;
  trackId?: string;
  id?: string;
  title?: string;
  description?: string;
  articleImage?: string;
  swellcastImage?: string;
  userId?: string;
  categories?: (CategoryResponse | OpenApiCategoryModel)[];
  languages?: Array<string>;
  countryCode?: string;
  transcribeKeywords?: Array<string>;
  owner?: OpenApiAuthorModel | OpenApiOwnerModel | OpenApiSwellcastOwnerModel;
  author?: OpenApiAuthorModel;
  swellcast?: { id: string };
  subscriptionOnly?: boolean;
  canReply?: boolean;
  isPanel?: boolean;
}

export interface SwellReplyModel extends Omit<OpenApiSwellcastResponseEnhanced, 'swellCount' | 'message'>, OpenApiSwellResponseEnhanced {
  isEditorsPick?: boolean;
}

export type WaveModel = Array<number>;

export enum UploadStatus {
  NONE,
  UPLOADING,
  SERVER_ERR,
  WEB_ERR,
  TIMEOUT_ERR,
  OFFLINE_ERR,
  COMPLETED,
  EMPTY,
}

export enum AudioMode {
  NONE = 'none',
  PLAY = 'play',
  RECORD_REPLY = 'reply',
  RECORD_NEW = 'master',
  RECORD_PROFILE = 'profile',
  RECORD_PROMPT_RESPONSE = 'prompt',
}

export enum ShareType {
  STATION = 'station',
  SWELLCAST = 'swellcast',
  SWELLCAST_SWELL = 'swellcast_swell',
  SWELL = 'swell',
  REPLY = 'reply',
  COMMUNITY = 'community',
}

export enum ReactType {
  HEART = 'HEART',
} // purge-css: layout-mode-landscape layout-mode-portrait

// export enum SwellCardFlags {
//   HIDE_DESCRIPTION,
//   USE_SWELLCAST_IMAGE,
//   NO_BASIC_CARD,
// }

export enum MediaRecorderEvent {
  DATA_AVAILABLE = 'dataavailable',
  ERROR = 'error',
  PAUSE = 'pause',
  RESUME = 'resume',
  START = 'start',
  STOP = 'stop',
}

export interface IServerSettings {
  isLocal: boolean;
  isStage: boolean;
  isProd: boolean;
  //   env: ENVType;
  stage: APIType;
  serverCountryCode: string;
  apiUrl: string;
  countryCode: string;
  resolvedColorMode: ColorMode;
  colorMode: ColorMode;
  autoPlay: boolean;
  debug?: string;
  volume?: number;
  playbackRate: number;
  isWidget?: boolean;
  version: number;
  nonce?: number;
  allowPlayInCard: boolean;
  utmParams: UTMParams;
}

export type HelmetContext = { helmet: HelmetServerState };

export interface IAppProps {
  location?: string;
  helmetContext?: { helmet: HelmetServerState };
  queryClientServer?: QueryClient;
  dehydratedState?: DehydratedState;
  basename?: string;
  settings?: Partial<IServerSettings>;
}
