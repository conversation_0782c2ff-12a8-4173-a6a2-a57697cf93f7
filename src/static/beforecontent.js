/*
attempt to set the color mode immediately before the content loads
so there's no flash of light/dark mode before user setting is applied

Manually pulled so webpack doesn't bloat this file from:
src/utils/colorMode.tsx
*/

function isApp() {
  return Object.prototype.hasOwnProperty.call(window, 'SwellAppChannel');
}

function getCookies() {
  const cookies = document.cookie.split('; ');
  const cookieObject = {};
  cookies.forEach((cookie) => {
    const [name, value] = cookie.split('=');
    cookieObject[name] = decodeURIComponent(value);
  });
  return cookieObject;
}

function deleteCookie(cookieName) {
  document.cookie = `${cookieName}=; expires=${new Date(0).toUTCString()}; path=/;`;
  document.cookie = `${cookieName}=; expires=${new Date(0).toUTCString()}; path=/; domain=.swellcast.com;`;
}

// Special request...
if (!isApp()) {
  // check if is /do/record route
  if (window.location.pathname.indexOf('/do/record') === 0) {
    const params = new URLSearchParams(window.location.search);
    // continue if searchParams has utm_source
    if (params.has('utm_source')) {
      const cookies = getCookies();
      const utm_source = params.get('utm_source');
      // continue if searchParams utm_source is different that cookied value
      if (utm_source !== cookies['utm_source']) {
        // remove _ga_*
        Object.keys(cookies).forEach((cookieName) => {
          if (/^_ga/.test(cookieName)) {
            deleteCookie(cookieName);
          }
        });
        // update utm cookies
        document.cookie = `utm_source=${utm_source}; path=/;`;
        document.cookie = `utm_medium=${params.get('utm_medium') || ''}; path=/;`;
        document.cookie = `utm_campaign=${params.get('utm_campaign') || ''}; path=/;`;
      }
    }
  }
}

const ColorMode = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto',
};

function getSystemColorMode() {
  if (window.matchMedia) {
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return ColorMode.DARK;
    }
    if (window.matchMedia('(prefers-color-scheme: light)').matches) {
      return ColorMode.LIGHT;
    }
  }
  return ColorMode.AUTO;
}

const applyColorMode = (mode) => {
  const _mode = mode === ColorMode.AUTO ? getSystemColorMode() : mode;
  document.body.setAttribute('data-bs-theme', _mode);
};

const params = new URLSearchParams(window.location.search.toLowerCase());
const queryColorMode = params.has('colormode') ? params.get('colormode') : null;

let userColorMode = window.localStorage.getItem('colormode');
if (userColorMode !== 'light' && userColorMode !== 'dark') userColorMode = null;
const colorMode = queryColorMode ?? userColorMode ?? ColorMode.AUTO;
applyColorMode(colorMode);
