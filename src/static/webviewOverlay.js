/* eslint-disable no-unreachable */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-undef */

function SwFsoInit() {
  var styleEl = document.createElement('style');
  styleEl.innerHTML = `.sw-fso-full-screen-overlay {
             position: fixed;
             top: 0;
             left: 0;
             width: 100vw;
             height: 100vh;
             background-color: rgba(0, 0, 0, 0.85);
             z-index: 10000;
             display: flex;
             justify-content: center;
             /* Center horizontally */
             align-items: center;
             /* Center vertically */
             flex-direction: column;
             /* Stack children vertically */
         }


         .sw-fso-overlay-text {
             color: white;
             /* Choose a color that stands out */
             font-size: 24px;
             /* Adjust size as needed */
             z-index: 10001;
             /* Above the overlay */
             position: relative;
             font-family: Inter, Verdana, Geneva, Tahoma, sans-serif;


         }


         .sw-fso-overlay-arrow {
             position: fixed;
             right: 0px;
             /* Adjust to place near the top right corner */
             top: 10px;
             /* Adjust to place near the top right corner */
             width: 100px;
             /* Adjust size as needed */
             height: auto;
             z-index: 10001;
             /* Above the overlay */
         }


         .sw-fso-sectionDiv {
             max-width: 80%;
             margin-left: auto;
             margin-right: auto;
             pointer-events: none;
         }`;
  document.head.appendChild(styleEl);

  var elem = document.getElementById('SwFsoIdWebviewOverlay');

  if (!elem) {
    elem = document.createElement('div');
    elem.id = 'SwFsoIdWebviewOverlay';
    elem.classList.add('sw-fso-full-screen-overlay');
    elem.style.display = 'none';
    document.body.appendChild(elem);

    //   <div id="SwFsoIdWebviewOverlay" class='sw-fso-full-screen-overlay' style="display: none;"></div>

    //   var elem = document.getElementById('SwFsoIdWebviewOverlay');
    elem.innerHTML = `<span class='sw-fso-overlay-text'>
         <div class='sw-fso-sectionDiv'><h3 class="h3-normal">Open in Browser by using the menu option or button on top right of the screen. You can record and post directly from the browser.</h3></div>
     </span>
     <img src='https://assets.swellcast.com/marketing/promptImages/SWAssets_popoutarrow.webp' class='sw-fso-overlay-arrow' alt='Arrow pointing to the top right'>`;
  }

  if (elem) {
    if (SwFsoisNonSwellWebView()) {
      document.body.style.overflow = 'hidden';
      elem.style.display = 'flex';
    }
  }
}

function SwFsoisNonSwellWebView() {
  var isWebView = false;
  try {
    if (SwellAppChannel) isWebView = true;
  } catch (err) {
    //
  }

  var retVal = !isWebView; // Is it Swell webview, in which case cannot be non swell webview

  if (retVal) {
    var userAgent = navigator.userAgent || navigator.vendor || window.opera;
    // iOS WebView detection
    // var isiOSWebView = (userAgent.match(/(iPad|iPhone|iPod).*AppleWebKit(?!.*Safari)/i) ||
    //     (window.navigator.standalone === true)) && !window.MSStream;
    // Android WebView detection
    var isAndroidWebView = userAgent.includes('wv');
    // Instagram Webview detection
    var isInstagramWebView = userAgent.includes('Instagram');
    var isFacebookWebView = userAgent.includes('FB_IAB');
    // retVal = isiOSWebView || isAndroidWebView || isInstagramWebView;
    retVal = isAndroidWebView && (isInstagramWebView || isFacebookWebView);
  }

  const isProd = window.location.hostname === 'www.swellcast.com';

  if (!isProd) {
    const isStage = window.location.hostname === 'stage.swellcast.com';
    const params = new URLSearchParams(window.location.search);
    const debugRecord = isStage && params.has('debug') && params.get('debug').indexOf('record') > 0 - 1;
    if (debugRecord) return false;
  }
  return retVal;
}

window.addEventListener('DOMContentLoaded', () => SwFsoInit(), false);
