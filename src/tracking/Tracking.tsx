import { OpenApiSwellcastResponseEnhanced } from '../api/gql.getSwellcast';
import { OpenApiSwellResponseEnhanced } from '../api/gql.loadSwellById';
import { IAuthTrack } from '../components/login/AuthProvider';
import { widgetVersion } from '../framework/settings/settings';
import { OpenApiReplyResponse, OpenApiSwellResponse } from '../generated/graphql';
import { getSwellContentType } from '../utils/swell-utils';
import { getMobileOperatingSystem, inBrowser } from '../utils/Utils';

const getAudioPercent = (duration: number, position: number, maxPercent = 25): number => {
  const percentCompleted = Math.floor((100 * position) / duration);
  const slot = Math.floor(percentCompleted / maxPercent) * maxPercent;
  return slot;
};

export interface IPercentTrackingArgs {
  swell: OpenApiSwellResponse;
  reply?: OpenApiReplyResponse | null;
  duration: number;
  position: number;
  eventtype: 'start' | 'play' | 'stop' | 'complete';
  playbackRate: number;
  debug: boolean;
  loggedIn?: boolean;
}

const sw_ta_proxy = (info: IAuthTrack, _debug = false) => {
  //   if (_debug) console.log('sw_ta', info);
  window.sw_ta(info);
};

export const trackAudioLoaded = ({ swell, reply, loggedIn, playbackRate: playrate, debug }: IPercentTrackingArgs): void => {
  const isMaster = !reply;
  const item = isMaster ? swell : reply;
  const swellId = swell.id;
  const replyId = reply?.id ?? '';

  if (debug) console.log('trackAudioLoaded()', { swellId, replyId, loggedIn });

  if (loggedIn) {
    sw_ta_proxy({
      swellId,
      replyId,
      audioUrl: item?.audio?.url ?? '',
      totalDuration: item.audio?.duration ?? 0,
      currentDuration: 0,
      playrate,
      eventtype: 'start',
    });
  } else {
    track({
      av: widgetVersion,
      pt: 'widget',
      pi: 'swellweb',
      ec: 'audio',
      ea: 'loaded',
      el1: 'loaded',
      et: 'audio',
      ei: item?.id ?? 'unknown_id',
      cv2: swell.id,
      cv4: swell.title,
    });

    dataLayer({
      event: 'audio',
      audioPlayerAction: 'Loaded',
      audioTitle: swell.title ?? 'unknown_title',
      playingSwellId: item?.id ?? 'unknown_id',
      audioValue: 0,
    });
  }
};

export const trackTextSwellLoad = (trackObject: IPercentTrackingArgs) => {
  if (trackObject.debug) console.log('trackTextSwellLoad', { swellId: trackObject.swell.id, loggedIn: trackObject.loggedIn });

  if (trackObject.loggedIn) {
    sw_ta_proxy(
      {
        swellId: trackObject.swell.id,
        replyId: '',
        audioUrl: trackObject.swell.audio?.url ?? '',
        totalDuration: 1,
        currentDuration: 1,
        playrate: 1,
        eventtype: 'stop',
      },
      trackObject.debug,
    );
  } else {
    trackAudioLoaded({ ...trackObject, reply: null });
    trackAudioProgress({ ...trackObject, eventtype: 'play', reply: null, duration: 4, position: 0 });
    trackAudioProgress({ ...trackObject, eventtype: 'play', reply: null, duration: 4, position: 1 });
    trackAudioProgress({ ...trackObject, eventtype: 'play', reply: null, duration: 4, position: 2 });
    trackAudioProgress({ ...trackObject, eventtype: 'play', reply: null, duration: 4, position: 3 });
    trackAudioProgress({ ...trackObject, eventtype: 'play', reply: null, duration: 4, position: 4 });
  }
};

export const trackAudioPosition = ({ swell, reply, duration, position, eventtype, playbackRate: playrate, loggedIn, debug = false }: IPercentTrackingArgs) => {
  const isMaster = !reply;
  const item = isMaster ? swell : reply;
  const swellId = swell.id;
  const replyId = reply?.id ?? '';

  if (debug) console.log('trackAudioPosition()', { loggedIn, eventtype, swellId, replyId, duration, position: Math.round(position * 10) / 10 });

  sw_ta_proxy(
    {
      swellId,
      replyId,
      audioUrl: item?.audio?.url ?? '',
      totalDuration: item?.audio?.duration ?? 0,
      currentDuration: ~~position,
      playrate,
      eventtype,
    },
    debug,
  );
};

export const trackAudioProgress = ({ swell, reply, duration, position, eventtype, debug = false }: IPercentTrackingArgs) => {
  const isMaster = !reply;
  const item = isMaster ? swell : reply;
  const swellId = swell.id;
  const replyId = reply?.id ?? null;
  const contentType = getSwellContentType(swell); // SA-7836
  const quarter = getAudioPercent(duration, position, 25);

  if (debug) console.log('trackAudioProgress()', eventtype, { swellId, replyId, duration: ~~(duration * 10) / 10, position: Math.round(position * 10) / 10, quarter });

  track({
    av: widgetVersion,
    ec: 'audio',
    ea: 'play',
    el1: `audio_${isMaster ? '' : 'reply_'}${quarter}_percentage`,
    ev1: quarter,
    el3: 'visible', // visible need to be tracked for this audio event. Sice it is needed on computation.
    ev3: 1, // Web all the swell's/replies are public hence always set to be 1
    cl2: item.id,
    cv2: swell.id,
    et: isMaster ? 'audio' : 'audio_reply',
    ei: item?.id ?? 'unknown_id',
    eai: item.author?.id,
    eas: item.author?.swellcast?.id,
    el5: 'languages',
    ev5: swell.languages,
    el6: 'categories',
    ev6: swell?.categories?.map((item) => item.id) ?? [],
    el8: 'countries',
    ev8: swell?.countryCode ? [swell?.countryCode] : [],
    el9: 'transcribeKeywords',
    ev9: isMaster ? swell?.keywords : reply.keywords,
    pt: 'widget',
    pi: 'swellweb', // should be "id" of page check: https://anecure.atlassian.net/wiki/spaces/ENGINEERIN/pages/436961359/Front-End+-+BI+Event+Model
    pas: swell?.swellcast?.id,
    cv4: swell.title,
  });

  dataLayer({
    event: 'audio',
    swellId,
    replyId,
    audioPlayerAction: 'playing',
    audioTitle: swell.title,
    audioValue: quarter,
    promptId: swell?.promptId ?? null,
    type: contentType,
  });
};

export const trackAudioStart = (trackObject: IPercentTrackingArgs): void => {
  if (trackObject.debug) console.log('trackAudioStart()');
  if (trackObject.loggedIn) {
    trackAudioPosition({ ...trackObject, eventtype: 'start' });
  } else {
    trackAudioProgress({ ...trackObject, eventtype: 'play' });
  }
};

export const trackAudioPause = (trackObject: IPercentTrackingArgs): void => {
  // NOTE: Yes, "stop" means "pause" here - just accept it.
  if (trackObject.debug) console.log('trackAudioPause()');
  if (trackObject.loggedIn) {
    trackAudioPosition({ ...trackObject, eventtype: 'stop' });
  } else {
    // trackAudioPercent({ ...trackObject, eventtype: 'stop' });
  }
};

export const trackAudioResume = (trackObject: IPercentTrackingArgs): void => {
  if (trackObject.debug) console.log('trackAudioResume()');
  if (trackObject.loggedIn) {
    trackAudioPosition({ ...trackObject, eventtype: 'start' });
  } else {
    // NOTE: don't track resume for guest
    // trackAudioPercent({ ...trackObject, eventtype: 'play' });
  }
};

export const trackAudioComplete = (trackObject: IPercentTrackingArgs): void => {
  if (trackObject.debug) console.log('trackAudioComplete()');
  if (trackObject.loggedIn) {
    // JIRA: https://anecure.atlassian.net/browse/SA-7880
    // NOTE: Yes, "stop"means "complete" here - let it go.
    trackAudioPosition({ ...trackObject, eventtype: 'play' });
    trackAudioPosition({ ...trackObject, eventtype: 'stop' });
  } else {
    trackAudioProgress({ ...trackObject, eventtype: 'play' });
    // trackAudioPercent({ ...trackObject, eventtype: 'complete' });
  }
};

export const trackAudioError = ({ swell, reply, debug }: IPercentTrackingArgs): void => {
  const isMaster = !reply;
  const swellId = swell.id;
  const replyId = reply?.id ?? null;

  if (debug) console.log('trackAudioError()', { swellId, replyId });

  track({
    av: widgetVersion,
    pt: 'widget',
    pi: 'swellweb',
    ec: 'audio',
    ea: 'error',
    el1: 'Audio Player Error',
    et: isMaster ? 'Primary Swell' : 'Swell Reply',
    ei: isMaster ? swellId : replyId, // ?? 'unknown_id',
    cv2: swellId,
    cv4: swell.title,
  });
};

export const trackSwellcastPageView = (data: Pick<OpenApiSwellcastResponseEnhanced, 'id' | 'name'>): void => {
  if (!data) return;
  track({
    av: widgetVersion,
    pt: 'widget',
    pi: 'swellcastweb',
    ea: 'loaded',
    el1: 'loaded',
    cv2: data?.id,
    cv4: data?.name,
  });
};

export const trackSwellPageView = (data: OpenApiSwellResponseEnhanced): void => {
  track({
    av: widgetVersion,
    pt: 'widget',
    pi: 'swellweb',
    ea: 'loaded',
    el1: 'loaded',
    cv2: data?.id,
    cv4: data?.title,
  });
};

const getOSKey = () => {
  const os = getMobileOperatingSystem();
  return (os === 'unknown' ? 'desktop' : os).toLowerCase();
};

const trackPopDownloadApp = (context: string) => {
  dataLayer({ event: `download-app-${getOSKey()}`, context });
};

export function trackFollowSwellcastPopup(swellcastAlias = 'unknown') {
  dataLayer({ event: 'follow-popup', context: 'listen-time-popup', swellcastAlias });
}

export const trackClickDownloadApp = (ctaLink: string, context: string) => {
  trackPopDownloadApp(context);
  track({
    av: widgetVersion,
    pt: 'widget',
    pi: 'swellcastweb',
    ea: 'click',
    el1: 'listen on swell',
    cl1: 'url',
    cv1: ctaLink,
  });
};

function track(customTrackObj1: Record<string, unknown>, customTrackObj2: Record<string, unknown> = {}): void {
  try {
    const trackObj = new TrackerObject(Object.assign({}, customTrackObj2, customTrackObj1));
    const TRACKING_API = process.env.TRACKING_API;

    if (inBrowser && TRACKING_API && trackObj.cv2 && trackObj.cv4) {
      fetch(TRACKING_API, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          action: trackObj.ea,
          type: trackObj.el1,
        },
        body: JSON.stringify(trackObj),
      }).catch((_err) => {
        // console.log(_err);
      });
    }
    // if (debug) console.log('track', removeEmptyProps(trackObj));
  } catch (err) {
    console.error('track', err);
  }
}

export function dataLayer(payload: Record<string, unknown>): void {
  if (!inBrowser) return;
  if (!window.dataLayer) window.dataLayer = [];
  // JIRA: https://anecure.atlassian.net/browse/SA-7729
  window.dataLayer.push({ promptId: null, ...payload });
}

class TrackerObject {
  // caller:string = 'none';
  // Device Info
  gt?: number = Date.now(); // timestamp
  av?: string = ''; // version
  di?: string = ''; // device id
  dos?: string = ''; // window.navigator.userAgent; // Device OS
  dv?: string = ''; // device version
  dm?: string = ''; // device model

  // User Info
  ui?: string = 'guest'; // User Id
  ul?: string = ''; // User Login Type
  uf?: string = '0'; // User following count
  ufr?: string = '0'; // User folloer count
  us?: string = ''; // User SwellCastId
  un?: string = ''; // User New
  uc?: string = '__DYNAMICCOUNTRY__'; // User Country

  // Page Info
  pt?: string = ''; // Page Type
  pi?: string = ''; // ID of the page. For eg: SwellCastId, SwellId, and so on, where relevant
  pai?: string = ''; // Page Author ID, if relevant
  pas?: string = ''; // Page Author SwellCastId, if relevant

  // Event Info
  ec?: string = 'audio'; // Event Category. For eg: Video
  ea = ''; // Event Action. For eg: Play  // required in header as "action"
  el1 = ''; // Event Label. For eg: Health Food's // required in header as "type"
  ev1?: number = 0; // Event Value. For eg: 10%, 25%, 50%, 100%
  el2?: string = ''; // Event Label. For eg: Health Food's
  ev2?: number = 0; // Event Value. For eg: 10%, 25%, 50%, 100%
  el3?: string = ''; // Event Label. For eg: Health Food's
  ev3?: number = 0; // Event Value. For eg: 10%, 25%, 50%, 100%
  el4?: string = ''; // Event Label. For eg: Health Food's
  ev4?: Array<string> = []; // Event Value. For eg: 10%, 25%, 50%, 100%
  el5?: string = ''; // Event Label. For eg: Health Food's
  ev5?: Array<string> = []; // Event Value. For eg: 10%, 25%, 50%, 100%
  el6?: string = ''; // Event Label. For eg: Health Food's
  ev6?: Array<string> = []; // Event Value. For eg: 10%, 25%, 50%, 100%
  el7?: string = ''; // Event Label. For eg: Health Food's
  ev7?: Array<string> = []; // Event Value. For eg: 10%, 25%, 50%, 100%
  el8?: string = ''; // Event Label. For eg: Health Food's
  ev8?: Array<string> = []; // Event Value. For eg: 10%, 25%, 50%, 100%
  el9?: string = ''; // Event Label. For eg: Health Food's
  ev9?: Array<string> = []; // Event Value. For eg: 10%, 25%, 50%, 100%
  cl1?: string = ''; // Custom Label. For eg: Health Food's
  cv1?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  cl2?: string = ''; // Custom Label. For eg: Health Food's
  cv2?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  cl3?: string = ''; // Custom Label. For eg: Health Food's
  cv3?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  cl4?: string = ''; // Custom Label. For eg: Health Food's
  cv4?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  cl5?: string = ''; // Custom Label. For eg: Health Food's
  cv5?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  cl6?: string = ''; // Custom Label. For eg: Health Food's
  cv6?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  cl7?: string = ''; // Custom Label. For eg: Health Food's
  cv7?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  cl8?: string = ''; // Custom Label. For eg: Health Food's
  cv8?: string = ''; // Custom Value. For eg: 10%, 25%, 50%, 100%
  et?: string = ''; // Entity Type. For eg: Reply, SwellCast
  ei?: string = ''; // Entity ID. For eg: Reply ID, Swell ID, etc
  eai?: string = ''; // Entity Author Id
  eac?: string = ''; // Antity Author SwelCastId

  constructor(delta: Record<string, unknown> = {}) {
    Object.assign(this, delta);
  }
}
