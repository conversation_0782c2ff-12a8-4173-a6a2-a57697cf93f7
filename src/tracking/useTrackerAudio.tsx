import { useEffect, useMemo, useRef, useState } from 'react';

import { useCurrentSwell } from '../api/gql.loadSwellById';
import { useDebug } from '../components/debug/useDebug';
import { useAuth } from '../components/login/useAuth';
import { useAudioData, useAudioReply } from '../components/player/AudioPlayer/useAudioData';
import { useAudioTrack } from '../components/player/AudioPlayer/useAudioTrack';
import { useSettings } from '../components/settings/useSettings';
import { useOrchestration } from '../framework/useOrchestration';
import { OpenApiSwellResponse } from '../generated/graphql';
import { SwellReplyModel } from '../models/models';
import { isTextSwell } from '../utils/swell-utils';
import { IPercentTrackingArgs, trackAudioComplete, trackAudioError, trackAudioLoaded, trackAudioPause, trackAudioPosition, trackAudioProgress, trackAudioResume, trackAudioStart, trackTextSwellLoad } from './Tracking';

export const useAudioTracking = () => {
  const { debug } = useDebug('track');
  const auth = useAuth();
  const track = useAudioTrack();
  const swell = useCurrentSwell();
  const data = useAudioData() as SwellReplyModel;
  const reply = useAudioReply();
  const { playbackRate } = useSettings();
  const orch = useOrchestration();
  //   const { isSeeking } = useMediaSeek(track.$audio!);
  const subscriptionOnly = useMemo(() => swell.data?.subscriptionOnly ?? false, [swell.data]);
  const shouldTrack = useMemo(() => data && (auth.loggedIn || !subscriptionOnly), [data, auth.loggedIn, subscriptionOnly]);
  const [percentBlock, setPercentBlock] = useState(-1);
  //   const percentBlock = useMemo(() => (track.canPlay && orch.isOpen ? ~~(track.progress / 0.25) : -1), [orch.isOpen, track.isPlaying, track.progress]);

  const trackObject = useMemo<IPercentTrackingArgs>(
    () => ({
      swell: swell.data as OpenApiSwellResponse, //
      duration: track.duration,
      position: track.position,
      playbackRate: playbackRate,
      debug,
      reply,
      loggedIn: auth.loggedIn,
      eventtype: 'start',
    }),
    [swell.data, track.duration, track.position, playbackRate, debug, reply, auth.loggedIn],
  );

  // Thanks AI, but why does this even work?
  const trackObjectRef = useRef(trackObject);
  trackObjectRef.current = trackObject;

  useEffect(() => {
    // if (!auth.isReady) return;
    // if (!shouldTrack) return;
    if (track.hasMetaData && track.duration) {
      setPercentBlock(~~(track.progress / 0.25));
    } else {
      setPercentBlock(-1);
    }
  }, [track.progress, track.hasMetaData]);

  // fire the text swell event everytime a swell loads with a text master.
  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    if (swell.isSuccess && isTextSwell(swell.data!)) {
      trackTextSwellLoad(trackObject);
    }
  }, [auth.isReady, swell.isSuccess]);

  // TODO: The issue I’m fixing now is that when autoplay=0, is doesn’t release to play through the replies after the user clicks play. I’m uploading the the “loaded” fix.

  // audio is loaded
  //   useEffect(() => {
  //     if (!auth.isReady) return;
  //     if (!shouldTrack) return;
  //     console.log({ readyState: AudioReadyState[track.readyState], isPlaying: track.isPlaying, canPlay: track.canPlay });
  //     if (track.readyState === AudioReadyState.HAVE_METADATA && track.isPlaying === false && track.canPlay === false) {
  //     //   resetPercentTracking();
  //     //   trackAudioLoaded(trackObject);
  //     }
  //   }, [auth.isReady, track.readyState]);

  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    // console.log({ hasMetaData: track.hasMetaData });
    if (track.hasMetaData) {
      //   resetPercentTracking();
      trackAudioLoaded(trackObject);
    }
  }, [auth.isReady, track.hasMetaData]);

  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    if (track.isPlaying) {
      if (track.position == 0) {
        trackAudioStart(trackObject);
      } else {
        trackAudioResume(trackObject);
      }
    }
  }, [track.isPlaying]);

  // play pause triggered - only for logged in user - don't ask, just do it
  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    // if (!auth.loggedIn) return; // loggedIn only
    if (track.isPlaying) {
      //   trackAudioResume(trackObject);
    }
  }, [auth.isReady, track.isPlaying]);

  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    if (!auth.loggedIn) return; // loggedIn only
    if (track.isPaused && !track.isComplete) {
      trackAudioPause(trackObject);
    }
  }, [auth.isReady, track.isPaused]);

  // watch for each 25% progress for guest user - this tracks from 0-75%
  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    if (auth.loggedIn) return; // guest only
    if (percentBlock > -1 && track.duration && track.isPlaying) {
      // && track.isPlaying
      // console.log({p:track.progress, d:track.duration})
      trackAudioProgress({ ...trackObject, eventtype: 'play' });
    }
  }, [percentBlock]);

  // track 2 second intervals for auth user only
  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    if (auth.loggedIn && track.isPlaying) {
      const trackPosition = () => {
        trackAudioPosition({ ...trackObjectRef.current, eventtype: 'play' });
      };
      const timer = setInterval(trackPosition, 2000);
      trackPosition();

      return () => {
        clearInterval(timer);
      };
    }
  }, [auth.isReady, shouldTrack, track.isPlaying]);

  // audio is completed
  useEffect(() => {
    if (!auth.isReady) return;
    if (!shouldTrack) return;
    // if (!auth.loggedIn) return; // loggedIn only
    if (track.isComplete) {
      trackAudioComplete(trackObject);
    }
  }, [auth.isReady, track.isComplete]);

  //   reset on close player
  useEffect(() => {
    if (!orch.isOpen) {
      // resetPercentTracking();
      setPercentBlock(-1);
    }
  }, [orch.isOpen]);

  // audio error
  useEffect(() => {
    if (!auth.isReady) return;
    if (track.isError) {
      trackAudioError(trackObject);
    }
  }, [auth.isReady, track.isError]);
};
