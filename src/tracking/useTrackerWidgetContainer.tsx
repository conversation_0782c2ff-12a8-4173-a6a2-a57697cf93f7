import { useEffect } from 'react';
import { useDebug } from '../components/debug/useDebug';
import { inBrowser } from '../utils/Utils';
import { dataLayer } from './Tracking';

function getParentOrigin() {
  const locationAreDisctint = window.location !== window.parent.location;
  const parentOrigin = ((locationAreDisctint ? document.referrer : document.location) || '').toString();

  if (parentOrigin) {
    return new URL(parentOrigin).origin;
  }

  const currentLocation = document.location;

  if (currentLocation.ancestorOrigins && currentLocation.ancestorOrigins.length) {
    return currentLocation.ancestorOrigins[0];
  }

  return '';
}

// attempt to get host domain for widget tracking
export const useTrackerWidgetContainer = () => {
  const { debug } = useDebug('track');
  // for widget in iframe, host is not passed
  useEffect(() => {
    if (inBrowser) {
      if (debug) console.log('add window.onmessage');
      window.onmessage = function (event: MessageEvent<{ iframehost: string }>) {
        if (event.data instanceof Object && Object.prototype.hasOwnProperty.call(event.data, 'iframehost')) {
          if (debug) console.log('widgetContainerDomain', event.data.iframehost);
          dataLayer({ widgetContainerDomain: event.data.iframehost });
        }
      };
    }
    return () => {
      if (debug) console.log('remove window.onmessage');
      window.onmessage = null;
    };
  }, []);

  useEffect(() => {
    if (inBrowser) {
      const inIframe = window.location !== window.parent.location;
      if (!inIframe) {
        if (debug) console.log('inIframe', inIframe, 'widgetContainerDomain', window.location.host);
        dataLayer({ widgetContainerDomain: window.location.host });
      } else {
        if (debug) console.log('inIframe', inIframe, 'widgetContainerDomain', getParentOrigin());
        dataLayer({ widgetContainerDomain: getParentOrigin() });
      }
    }
  }, []);
};
