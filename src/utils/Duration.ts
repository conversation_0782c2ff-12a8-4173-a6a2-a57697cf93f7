type DurationInput = {
  days?: number;
  hours?: number;
  minutes?: number;
  seconds?: number;
  milliseconds?: number;
};

export class Duration {
  readonly milliseconds: number;

  constructor({ days = 0, hours = 0, minutes = 0, seconds = 0, milliseconds = 0 }: DurationInput) {
    this.milliseconds = (((days * 24 + hours) * 60 + minutes) * 60 + seconds) * 1000 + milliseconds;
  }

  static fromMilliseconds(ms: number): Duration {
    return new Duration({ milliseconds: ms });
  }

  static since(ms: number): Duration {
    return new Duration({ milliseconds: Date.now() - ms });
  }

  static zero = new Duration({ milliseconds: 0 });

  get inMilliseconds(): number {
    return this.milliseconds;
  }

  get inSeconds(): number {
    return this.milliseconds / 1000;
  }

  get inMinutes(): number {
    return this.inSeconds / 60;
  }

  get inHours(): number {
    return this.inMinutes / 60;
  }

  get inDays(): number {
    return this.inHours / 24;
  }

  add(other: Duration): Duration {
    return Duration.fromMilliseconds(this.milliseconds + other.milliseconds);
  }

  subtract(other: Duration): Duration {
    return Duration.fromMilliseconds(this.milliseconds - other.milliseconds);
  }

  isLongerThan(other: Duration): boolean {
    return this.milliseconds > other.milliseconds;
  }

  isShorterThan(other: Duration): boolean {
    return this.milliseconds < other.milliseconds;
  }

  equals(other: Duration): boolean {
    return this.milliseconds === other.milliseconds;
  }

  toString(): string {
    const totalSeconds = Math.floor(this.milliseconds / 1000);
    const ms = this.milliseconds % 1000;
    const s = totalSeconds % 60;
    const m = Math.floor(totalSeconds / 60) % 60;
    const h = Math.floor(totalSeconds / 3600);
    return `${h}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
  }
}
