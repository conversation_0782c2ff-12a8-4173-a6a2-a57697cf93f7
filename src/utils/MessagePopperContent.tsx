import React from 'react';
import { PopupContainer } from '../components/popup/PopupContainer';
import { IMessagePopper } from './useMessagePopper';

export const MessagePopperContent = ({ data }: { data: IMessagePopper }) => {
  return (
    <PopupContainer>
      <div className='d-flex flex-column gap-4' style={{ maxWidth: 420 }}>
        <h3 className='m-0 text-center' dangerouslySetInnerHTML={{ __html: data?.title ?? '' }} />
        <p className='m-0' dangerouslySetInnerHTML={{ __html: data?.description ?? '' }} />
        <div className='d-flex flex-column gap-3 w-100 flex-center'>
          {data?.actions.map((action, i) => {
            const style: React.CSSProperties = {
              color: '#000',
            };
            if (action?.color) {
              style.backgroundColor = `#${action.color}`;
            }
            if (action?.style) {
              Object.assign(style, action.style);
            }
            return (
              <a className='btn btn-primary w-100 text-center' key={`mpa${i}`} href={action.onClickNavigate} style={style}>
                {action.label}
              </a>
            );
          })}
        </div>
      </div>
    </PopupContainer>
  );
};
