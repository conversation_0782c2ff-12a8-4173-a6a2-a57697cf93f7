import SwellLogo from '@images/swell-app-icon-square.svg';

export const VisitSwellLifeScreen = () => (
  <div data-component='VisitSwellLifeScreen' className='bg-black' style={{ position: 'fixed', zIndex: 9999, inset: 0 }}>
    <VisitSwellLife />
  </div>
);

export const VisitSwellLife = (): JSX.Element => (
  <a className='fix w-100' href='https://swell.life' target='_blank' rel='noreferrer'>
    <div data-component='VisiteSwellLife' className='fade-in d-flex flex-column h-100 bg-dark' style={{ minHeight: '100vh' }}>
      <div className='p-3 flex-grow-1 d-flex align-items-center justify-content-center'>
        <div style={{ width: 'min(80%,300px,60vh)', maxHeight: '90%' }}>
          <div style={{ width: '100%', paddingBottom: '100%', position: 'relative', borderRadius: '10%', transform: 'rotate(-3deg)' }}>
            <div
              className='borealis-effect-1'
              style={{
                position: 'absolute',
                transform: 'rotate(45deg)',
                width: '70vw',
                height: 0,
                boxShadow: '#007aff33 0px 0px 80px 16px',
                borderRadius: '25%',
              }}
            />
            <div
              className='borealis-effect-2'
              style={{
                position: 'absolute',
                transform: 'rotate(-25deg) translate(-50%, -35%)',
                width: '70vw',
                height: 0,
                boxShadow: '#757e62 0px 0px 120px 16px',
                borderRadius: '25%',
              }}
            />
            <div
              className='borealis-effect-3'
              style={{
                position: 'absolute',
                transform: 'rotate(-90deg) translate(-50%, -35%)',
                width: '80vw',
                height: 0,
                boxShadow: '#ca403f33 0px 0px 120px 120px',
                borderRadius: '25%',
              }}
            />
            <div
              className='borealis-effect-4'
              style={{
                position: 'absolute',
                transform: 'rotate(75deg) translate(0, 27vw)',
                width: '50vw',
                height: 0,
                boxShadow: '#DF9BEF22 0px 0px 120px 120px',
                borderRadius: '50%',
              }}
            />
            <SwellLogo id='VisitSwellLife' style={{ borderRadius: '10%', position: 'absolute', width: '86%', height: '90%', top: '50%', left: '50%', transform: 'translate(-50%,-50%)' }} />
          </div>
        </div>
      </div>
      <div className='p-4 text-center shadow flex-shrink-1 d-flex align-items-center justify-content-center position-relative'>
        <h2 className='m-0 hover'>
          Visit us at <u>Swell.life</u>
        </h2>
      </div>
    </div>
  </a>
);
