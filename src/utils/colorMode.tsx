import { useEffect, useState } from 'react';
import { ColorMode } from '../models/models';
import { inBrowser } from './Utils';

function getSystemColorMode() {
  if (inBrowser && window.matchMedia) {
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return ColorMode.DARK;
    }
    if (window.matchMedia('(prefers-color-scheme: light)').matches) {
      return ColorMode.LIGHT;
    }
  }
  return ColorMode.LIGHT; // DEFAULT_COLOR_MODE; // ColorMode.AUTO;
}

export const useSystemColorMode = () => {
  const [colorMode, setColorMode] = useState(getSystemColorMode());

  useEffect(() => {
    const onChangeModeEvent = (e: MediaQueryListEvent) => {
      setColorMode(e.matches ? ColorMode.DARK : ColorMode.LIGHT);
    };
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', onChangeModeEvent);
    return () => {
      window.matchMedia('(prefers-color-scheme: dark)').removeEventListener('change', onChangeModeEvent);
    };
  }, []);

  return colorMode;
};

export const applyColorMode = (mode: ColorMode) => {
  const _mode = mode === ColorMode.AUTO ? getSystemColorMode() : mode;
  document.body.setAttribute('data-bs-theme', _mode);
};
