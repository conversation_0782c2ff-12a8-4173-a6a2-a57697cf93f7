import { buildUrl } from '../framework/buildUrl';
import { OpenApiHomePageSwellResponse, OpenApiSwellResponse } from '../generated/graphql';
import { getSwellLink2, isTextSwell } from './swell-utils';

// experiment to get swell meta data rather than compile it on load with "enhanced" types
// export const useSwellMeta = (canonicalId: string) => {
//   const swell = useSwell({ canonicalId });
//   return useMemo(() => (swell.isSuccess ? extractSwellMeta(swell.data) : { isText: false, link: '', path: '' }), [swell.isSuccess]);
// };

export const extractSwellMeta = (swell: OpenApiSwellResponse | OpenApiHomePageSwellResponse | null | undefined) => {
  const isText = swell ? isTextSwell(swell) : false;
  const path = swell ? getSwellLink2(swell) : '';
  const link = swell && path ? buildUrl({ pathname: path }) : '';
  return { isText, link, path };
};
