import { DehydratedState } from '@tanstack/react-query';
import { inBrowser } from './Utils';

export function getStaticQueryData<T>(queryKey: unknown[], defaultValue: T = null as T) {
  if (inBrowser) {
    const __REACT_QUERY_STATE__: DehydratedState = window?.__REACT_QUERY_STATE__ ?? {};
    const i = __REACT_QUERY_STATE__?.queries?.findIndex((q) => queryKey.reduce((s: number, w, i) => s + (w === q.queryKey[i] ? 1 : 0), 0) === queryKey.length) ?? -1;
    if (i > -1) {
      return { ...(__REACT_QUERY_STATE__.queries[i].state.data as Record<string, unknown>) } as T;
    }
  }
  return defaultValue;
}
