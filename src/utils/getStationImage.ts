import defaultStationBackground from '@images/default-station-bg.png';
import { DEFAULT_OG } from '../framework/settings/DEFAULT_OG';
import { CategoryResponse, OpenApiCategoryModel, StationInfoResponse } from '../generated/graphql';

export function getStationBackgroundImage(station: Pick<CategoryResponse, 'image'>) {
  return station?.image ?? defaultStationBackground;
}

export function getStationSquareImage(station: CategoryResponse | OpenApiCategoryModel | StationInfoResponse) {
  return station?.image?.replace('.jpg', '_LOW.jpg') ?? '';
}

export function getStationOGImage(station: Pick<CategoryResponse, 'image' | 'ogImage'>) {
  return station?.ogImage ?? station?.image ?? DEFAULT_OG.image;
}
