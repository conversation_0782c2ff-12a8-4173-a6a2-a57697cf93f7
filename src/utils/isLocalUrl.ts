export const isLocalPath = (url: string) => {
  let urlObject: URL;

  try {
    urlObject = new URL(url);
  } catch {
    return false;
  }

  if (urlObject.pathname.indexOf('/t/') === 0) {
    // Special Case!! these redirects should always reload the page
    return false;
  }
  switch (urlObject.hostname) {
    case 'localhost':
    case 'stage.swellcast.com':
    case 'www.swellcast.com':
    case 'swellcast.com':
      return true;
    default:
      return false;
  }
};

export const getLocalPath = (url: string) => {
  const urlObject = new URL(url);
  return urlObject.pathname;
};
