import { OpenApiSwellResponse } from '../generated/graphql';

// type SwellWithOwner = {
//   author?: { id?: string };
//   swellcastOwner?: { id?: string };
//   swellcast?: { owner?: { id?: string } };
// };

/*

loadSwellById

loadSwellById.author.swellcast
OpenAPIAuthorModel > OpenAPIAuthorSwellcastModel

loadSwellById.swellcast.owner
OpenAPISwellSwellcastModel > OpenAPISwellcastOwnerModel


*/
/*
type SwellWithOwner = Partial<Pick<OpenApiHomePageSwellResponse, 'author' | 'swellcastOwner' | 'swellcast'> & Pick<OpenApiSwellResponse, 'swellcast' | 'author'> & Pick<OpenApiHomePageSwellResponse, 'author' | 'swellcast' | 'swellcastOwner'>>;

export const isSwellOpen = (swell: SwellWithOwner): boolean => {
  const ownerId = swell.swellcastOwner?.id || swell.swellcast?.owner?.id;
  return swell.author.id !== ownerId;
};
*/

// export const isHomepageSwellOpen = (swell: OpenApiHomePageSwellResponse) => {
//   const authorId = swell?.author?.id;
//   const ownerId = swell?.swellcastOwner?.id;
//   return authorId && ownerId && authorId !== ownerId;
// };

export const isSwellResponseOpen = (swell: OpenApiSwellResponse) => {
  const authorId = swell?.author?.id;
  const ownerId = swell?.swellcast?.owner?.id;
  return authorId && ownerId && authorId !== ownerId;
};
