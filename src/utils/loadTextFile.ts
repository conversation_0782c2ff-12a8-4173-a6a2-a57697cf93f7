import { useQuery } from '@tanstack/react-query';
import { slugify } from './Utils';

/**
 * Load a proxied text file and decode it as UTF-8, stripping a leading BOM if present.
 * @param audioUrl  The original URL of the audio/text resource
 * @returns An object with:
 *   - promise: resolves to the loaded text string (or rejects on HTTP errors or abort),
 *   - cancel(): aborts the fetch if it’s still in flight.
 */

export const useTextFile = (url?: string | null) => {
  return useQuery({
    queryKey: ['text-file', url],
    enabled: !!url,
    queryFn: async () => {
      if (!url) throw new Error('No URL provided');
      const loader = loadTextFile(url);
      const text = await loader.promise;
      return text;
    },
    staleTime: Infinity,
    retry: false,
  });
};

function loadTextFile(audioUrl: string): {
  promise: Promise<string>;
  cancel: () => void;
} {
  const controller = new AbortController();
  const { signal } = controller;

  const proxiedUrl = `/proxy/${encodeURIComponent(slugify(audioUrl))}` + `?url=${encodeURIComponent(audioUrl)}`;

  const promise = fetch(proxiedUrl, { signal })
    .then((res) => {
      if (!res.ok) throw new Error(`HTTP ${res.status}`);
      return res.arrayBuffer();
    })
    .then((buffer) => {
      // If the fetch was aborted, this will never run.
      const decoder = new TextDecoder('utf-8');
      const text = decoder.decode(buffer);
      return text.replace(/^\uFEFF/, '');
    })
    .catch((err) => {
      console.warn(err);
      return '';
    });

  return {
    promise,
    cancel: () => controller.abort('client'),
  };
}
