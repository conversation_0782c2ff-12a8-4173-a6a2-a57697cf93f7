// Yes, this is very specific and solves a very minor text layout issue
// If text is 3 words with an article between, it attaches the article to the smaller word
export function prettifyTitle(text: string) {
  const words = (text ?? '').split(/\s+/g);
  if (words.length === 3 && (words[1] === '&' || words[1] === 'a')) {
    if (words[0].length > words[2].length) {
      return (
        <>
          {words[0]} <>{words[1]}</>&nbsp;{words[2]}
        </>
      );
    } else {
      return (
        <>
          {words[0]}&nbsp;<>{words[1]}</> {words[2]}
        </>
      );
    }
  }
  return words.join(' ');
}
