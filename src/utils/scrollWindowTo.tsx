import { inBrowser } from './Utils';

let scrollRequest = -1;

export function getElementY(scrollToObj: HTMLElement | Element): number {
  if (scrollToObj && typeof scrollToObj.getBoundingClientRect === 'function') {
    return window.pageYOffset + scrollToObj.getBoundingClientRect().top;
  } else {
    return 0;
  }
}

// cancel scroll on user interaction
if (inBrowser) {
  ['mousedown', 'wheel', 'DOMMouseScroll', 'mousewheel', 'keyup', 'touchmove'].map((t) => {
    window.addEventListener(t, () => cancelAnimationFrame(scrollRequest), { passive: true });
  });
}

export function scrollWindowTo(scrollTo: number, scrollDuration = 300) {
  // kill last scroll
  cancelAnimationFrame(scrollRequest);
  // set top down a hair so it's not pressed againt the browser top edge
  scrollTo -= window.innerHeight * 0.1 + 60; // acount for header height

  // Use native smooth scrolling if supported
  if ('scrollBehavior' in document.documentElement.style) {
    window.scrollTo({
      top: scrollTo,
      behavior: 'smooth'
    });
    return;
  }

  // Fallback to animation frame implementation
  const startPosition = window.pageYOffset;
  const distance = scrollTo - startPosition;
  let startTime: number | null = null;

  function step(timestamp: number) {
    if (!startTime) startTime = timestamp;
    const elapsed = timestamp - startTime;

    // Apply easing function (easeInOutQuad)
    const progress = Math.min(elapsed / scrollDuration, 1);
    const easing = progress < 0.5
      ? 2 * progress * progress
      : 1 - Math.pow(-2 * progress + 2, 2) / 2;

    window.scrollTo(0, startPosition + distance * easing);

    if (elapsed < scrollDuration) {
      scrollRequest = window.requestAnimationFrame(step);
    }
  }

  scrollRequest = window.requestAnimationFrame(step);
}
