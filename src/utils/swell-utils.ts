import defaultSwellcastBackground from '@images/bruno-wave-card.svg?url';
import { generatePath } from 'react-router-dom';

import { OpenApiSwellcastResponseEnhanced, OpenApiSwellcastSwellResponseEnhanced } from '../api/gql.getSwellcast';
import { OpenApiSwellResponseEnhanced } from '../api/gql.loadSwellById';
import { DEFAULT_OG } from '../framework/settings/DEFAULT_OG';
import { INTERSTITIAL_REPLY } from '../framework/settings/INTERSTITIAL_REPLY';
import { POSTROLL_REPLY } from '../framework/settings/POSTROLL_REPLY';
import { ArticleType, AudioType, OpenApiAuthorModel, OpenApiOwnerModel, OpenApiProfileWithSectionsResponse, OpenApiPromptResponse, OpenApiPromptWithSectionsResponse, OpenApiReplyResponse, OpenApiStationWithSectionsResponse, OpenApiSwellResponse, OpenApiSwellSwellcastModel, OpenApiSwellcastOwnerModel, OpenApiSwellcastSwellResponse, StationType, SwellcastOwnerModel } from '../generated/graphql';
import { OGModel, RouteParams, StationTypeMap, SwellListType } from '../models/models';
import { isVideoFile } from '../view/sound/media/hooks/isVideoFile';
import { removeEmptyProps, slugify, stripFirstUrl } from './Utils';
import { getStationOGImage } from './getStationImage';

export const DEFAULT_LIST_SLUG = 'swells';
const DEFAULT_SLUG = 'swell';

// build a more unique id so if the audio file changes, the ui can update
export function getAudioId(e: { id?: string | null; audio?: AudioType | null }) {
  const url = e?.audio?.url ?? 'audio';
  const filename = url.split('/')?.pop()?.split('.')[0];
  const id = e?.id ?? 'id';
  return `${id}_${filename}`;
}

export const getPostRoll = (params: RouteParams, swell: OpenApiSwellResponseEnhanced) => ({
  ...POSTROLL_REPLY,
  id: 'swell-postroll',
  canonicalId: params.canonicalId,
  masterRef: { ...POSTROLL_REPLY.masterRef, params: { ...params, replyId: 'swell-postroll' }, title: swell.title },
});

export const getInterstitial = (params: RouteParams, swell: OpenApiSwellResponseEnhanced) => ({
  ...INTERSTITIAL_REPLY,
  id: 'swell-interstitial',
  canonicalId: params.canonicalId,
  masterRef: { ...INTERSTITIAL_REPLY.masterRef, params: { ...params, replyId: 'swell-interstitial' }, title: swell.title },
});

const getSwellcastLinkPattern = (params: RouteParams) => {
  let base = '';
  switch (params.listType) {
    case SwellListType.STATION:
    case SwellListType.COUNTRY:
    case SwellListType.LANGUAGE:
      base = '/:listType/:listId/:listSlug';
      break;
    default:
      if (params?.hash) {
        base = '/:listId/hashtag/:hash';
      } else {
        base = '/:listId';
      }
  }
  return base;
};

const getSwellLinkPattern = (params: RouteParams) => {
  let base = '';
  switch (params.listType) {
    case SwellListType.STATION:
    case SwellListType.COUNTRY:
    case SwellListType.LANGUAGE:
      base = `/:listType/:listId/:listSlug/:canonicalId/:slug`;
      break;
    default:
      // add hashtag series support in widget
      if (params?.hash) {
        base = `/:listId/hashtag/:hash/:canonicalId/:slug`;
      } else {
        base = `/:listId/:canonicalId/:slug`;
      }
  }
  return base;
};

const getReplyLinkPattern = (params: RouteParams) => {
  let base = '';
  switch (params.listType) {
    case SwellListType.STATION:
    case SwellListType.COUNTRY:
    case SwellListType.LANGUAGE:
      base = `/:listType/:listId/:listSlug/:canonicalId/:slug/:replyId`;
      break;
    default:
      base = `/:listId/:canonicalId/:slug/:replyId`;
  }
  return base;
};

export const getPromptLink = (props: { alias: string; id: string; slug: string }) => {
  const alias = (props?.alias ? props.alias : 'swell').toLowerCase();
  const slug = props?.slug ? props.slug : 'swell';
  return `/${alias}/prompt/${props.id}/${slug}`;
};

export const getPromptLink2 = (item: OpenApiPromptResponse) => {
  const slug = item?.slug ? item.slug : `${slugify(item.promptTitle) + '-' + slugify(item.promptText)}`;
  return `/${item.swellcast.owner?.alias}/prompt/${item.id}/${slug}`;
};

export const getAuthorLink = (author: OpenApiAuthorModel | OpenApiOwnerModel | undefined | null) => {
  if (!author) return '';
  const params = { listId: author?.alias?.toLowerCase() ?? 'listId' };
  return generatePath(`/:listId`, params);
};

export const getOwnerLink = (swell: OpenApiSwellSwellcastModel | { owner?: { alias?: string } }) => {
  const params = { listId: swell?.owner?.alias?.toLowerCase() ?? 'listId' };
  if (!swell?.owner?.alias) console.log(swell);
  return generatePath(`/:listId`, params);
};

export const getSwellcastLink = (params: RouteParams) => {
  params = removeEmptyProps(params);
  // alias should be lowercase, station IDs are case sensitive
  if (params.listType === SwellListType.SWELLCAST) params.listId = params?.listId?.toLowerCase();
  const swellcastPattern = getSwellcastLinkPattern(params);
  return generatePath(swellcastPattern, { listSlug: DEFAULT_LIST_SLUG, listType: SwellListType.SWELLCAST, ...params, listId: params.listId as string });
};

export const getSwellLink2 = (swell: OpenApiSwellcastSwellResponse | OpenApiSwellResponse) => {
  return generatePath('/:listId/:canonicalId/:slug', {
    listId: (swell as OpenApiSwellcastSwellResponse).swellcastOwner?.alias?.toLowerCase() ?? (swell as OpenApiSwellResponse).swellcast?.owner?.alias?.toLowerCase() ?? (swell as OpenApiSwellResponse).swellcast?.name?.toLowerCase().replace(/^@/, '') ?? 'user', //
    canonicalId: swell.canonicalId!,
    slug: slugify(swell?.title ?? 'swell'),
  });
};

export const getSwellLink = (params: RouteParams) => {
  const cleanedParams = removeEmptyProps(params);
  const swellPattern = getSwellLinkPattern(cleanedParams);
  return generatePath(swellPattern, { listSlug: DEFAULT_LIST_SLUG, slug: DEFAULT_SLUG, ...params });
};

const getReplyLink = (params: Pick<RouteParams, 'listId' | 'canonicalId' | 'replyId' | 'slug'>) => {
  const swellPattern = params?.replyId ? getReplyLinkPattern(params) : getSwellLinkPattern(params);
  return generatePath(swellPattern, { listSlug: DEFAULT_LIST_SLUG, slug: DEFAULT_SLUG, ...params });
};

export const getAuthorFullname = (author: Partial<SwellcastOwnerModel> | Partial<OpenApiAuthorModel> | Partial<OpenApiOwnerModel> | Partial<OpenApiSwellcastOwnerModel> | null = {}) => {
  const parts = [];
  if (author?.firstName) parts.push(author.firstName);
  if (author?.lastName) parts.push(author.lastName);
  return parts.join(' ');
};

const getParamsFromSwell = (swell: OpenApiSwellcastSwellResponse): RouteParams => {
  return {
    listId: swell.author?.alias ?? '', //
    canonicalId: swell?.canonicalId ?? '',
    slug: slugify(swell?.title ?? ''),
  };
};

type NormalSwell = OpenApiSwellResponseEnhanced | ((OpenApiSwellcastSwellResponse | OpenApiSwellcastSwellResponseEnhanced) & { ogImage?: string });

export const getOGFromSwell = (swell: NormalSwell): OGModel => {
  const params = getParamsFromSwell(swell as OpenApiSwellcastSwellResponse);
  const isText = isTextSwell(swell);
  return {
    title: swell?.title ?? '',
    description: isText ? swell?.snippet ?? '' : stripFirstUrl(swell?.description ?? '') || (swell?.description ?? ''), // SA-7800
    image: swell?.ogImage ?? swell?.articles?.[0]?.image ?? swell?.swellcast?.image ?? DEFAULT_OG.image,
    // ignore the hash routes so that canonical paths are consistent
    canonicalPath: getSwellLink({ ...params, hash: undefined }),
  };
};

// REF: https://anecure.atlassian.net/wiki/spaces/PROD/pages/1064304684/Share+Copy+and+OG+on+different+Share+Scenario

export const getOGFromReply = (swell: OpenApiSwellResponse, reply: OpenApiReplyResponse): OGModel => {
  return {
    title: `@${reply.author?.alias} ${getAuthorFullname(reply.author)}`, // swell.masterRef.title,
    description: `Replying in "${swell.title}"`, // stripFirstUrl(swell?.message ?? swell.masterRef?.description ?? ''),
    image: reply?.articles?.[0]?.image ?? swell?.ogImage ?? swell?.articles?.[0]?.image ?? swell?.swellcast?.image ?? undefined, //swell?.masterRef?.swellcastImage ?? DEFAULT_OG.image,
    canonicalPath: getReplyLink({ listId: swell.swellcast?.owner?.alias ?? '', canonicalId: swell?.canonicalId ?? '', replyId: reply?.id ?? '', slug: slugify(swell?.title ?? '') }),
  };
};

export const getStationLink = (station: { type?: StationType; id: string; name?: string }) => {
  return generatePath('/:listType/:listId/:listSlug', {
    listType: StationTypeMap[station?.type ?? StationType.Category],
    listSlug: slugify(station?.name ?? DEFAULT_LIST_SLUG),
    listId: station.id,
  });
};

export const getOGFromCommunity = (station: OpenApiStationWithSectionsResponse): OGModel => ({
  title: station?.name ?? '',
  description: stripFirstUrl(station?.description ?? ''),
  image: getStationOGImage(station),
  canonicalPath: getStationLink(station),
});

export const getOGFromProfile = (swellcast: OpenApiProfileWithSectionsResponse, params: RouteParams): OGModel => ({
  title: params?.hash ? `Swells by ${swellcast.name} tagged #${params.hash}` : swellcast.name ?? '',
  description: stripFirstUrl(swellcast?.description ?? ''),
  image: swellcast?.ogImage ?? swellcast?.image ?? '',
  canonicalPath: getSwellcastLink(params),
});

export const getOGFromPrompt = (prompt: OpenApiPromptWithSectionsResponse): OGModel => ({
  title: `${prompt.promptTitle.replace(/\n/g, ' ')} | ${stripFirstUrl(prompt?.promptText ?? '').replace(/\n/g, ' ')}`, // SA-7597
  description: 'Respond to this prompt and create your own prompts on Swell.', // product-call-channel/canvas /  formerly SA-7597
  image: prompt?.ogImage ?? prompt?.swellcast?.ogImage ?? '',
  canonicalPath: getPromptLink2(prompt),
});

export function hasArticleImage(swell: { articles?: ArticleType[] | null }) {
  return !!(swell?.articles?.[0]?.image ?? false);
}

export function getSwellcastImage(swellcast: Pick<OpenApiSwellcastResponseEnhanced, 'image'>) {
  return swellcast?.image ?? defaultSwellcastBackground;
}

export const isTextSwell = (swell: { audio?: OpenApiSwellResponse['audio'] }) => isTextFile(swell?.audio?.url ?? '');

export const isTextFile = (src: string) => /.txt$/.test(src ?? '');

const isVideoSwell = (swell: { audio?: OpenApiSwellResponse['audio'] }) => isVideoFile(swell?.audio?.url ?? '');

export enum SwellContentType {
  AUDIO = 'audio',
  VIDEO = 'video',
  TEXT = 'text',
}

export const getSwellContentType = (swell: OpenApiSwellResponse | OpenApiReplyResponse | OpenApiSwellResponseEnhanced, _context = '') => {
  //   console.log('getSwellContentType', swell?.audio?.url, { context });
  if (isVideoSwell(swell)) {
    return SwellContentType.VIDEO;
  }
  if (isTextSwell(swell)) {
    return SwellContentType.TEXT;
  }
  return SwellContentType.AUDIO;
};
