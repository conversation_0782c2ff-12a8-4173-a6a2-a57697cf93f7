import { useEffect } from 'react';
import { isValidCanonicalId } from '../api/gql.loadSwellById';
import { useDebug } from '../components/debug/useDebug';
import { useSettings } from '../components/settings/useSettings';
import { useAudioMode } from '../framework/useAudioMode';
import { useOrchestration } from '../framework/useOrchestration';
import { useRouteParams } from './useRouteParams';

export const useAutoPlay = () => {
  const { debug } = useDebug('autoplay');
  const params = useRouteParams();
  const settings = useSettings();
  const orch = useOrchestration();
  const aux = useAudioMode();
  const canonicalId = params?.canonicalId;

  useEffect(() => {
    if (isValidCanonicalId(canonicalId) && settings.autoPlay && (!orch.isOpen || canonicalId !== aux.audioQuery.playlistId)) {
      if (debug) console.log('useAutoPlay::useEffect');
      orch.togglePlay({ playlistId: canonicalId, trackId: params?.replyId }, false);
    }
  }, [canonicalId]);
};
