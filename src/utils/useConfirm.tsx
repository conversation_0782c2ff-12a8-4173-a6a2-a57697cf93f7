import React from 'react';
import { PopupContainer } from '../components/popup/PopupContainer';
import { usePopup } from '../components/popup/usePopup';
import { SwellButton } from '../components/swellbutton/SwellButton';

export const useConfirm = () => {
  const popup = usePopup();

  const showConfirm = async (message: string | React.ReactNode, { cancelLabel = 'Cancel', confirmLabel = 'Confirm' }: { cancelLabel?: string; confirmLabel?: string } = {}) => {
    return new Promise<boolean>((resolve, _reject) => {
      popup.showPopup(
        <PopupContainer>
          <div className='d-flex flex-column gap-3 align-items-center'>
            <div>{message}</div>
            <div className='d-flex gap-2'>
              <SwellButton.Cancel
                onClick={() => {
                  popup.close();
                  resolve(false);
                }}
                label={cancelLabel}
              />
              <SwellButton.Confirm
                onClick={() => {
                  popup.close();
                  resolve(true);
                }}
                label={confirmLabel}
              />
            </div>
          </div>
        </PopupContainer>,
        false,
      );
    });
  };

  return { showConfirm };
};
