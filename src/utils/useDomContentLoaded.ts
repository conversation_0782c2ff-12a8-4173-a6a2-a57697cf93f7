import { useLayoutEffect, useRef } from 'react';
import { inBrowser } from './Utils';

export function useDOMContentLoaded(callback: () => void) {
  const fired = useRef(false);

  if (inBrowser) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useLayoutEffect(() => {
      const handleDOMContentLoaded = () => {
        // avoid duplicate call
        if (fired.current === false) {
          fired.current = true;
          callback();
        }
      };

      if (document.readyState === 'complete') {
        handleDOMContentLoaded();
      } else {
        document.addEventListener('DOMContentLoaded', handleDOMContentLoaded);
        return () => {
          document.removeEventListener('DOMContentLoaded', handleDOMContentLoaded);
        };
      }
    }, [callback]);
  }
}
