import { useCallback, useEffect, useReducer, useRef } from 'react';

export enum PointerAction {
  NONE,
  SWIPE_DOWN,
  SWIPE_UP,
  SWIPE_LEFT,
  SWIPE_RIGHT,
  CLICK,
}

interface DragInfo {
  isActive: boolean;
  state: string;
  x: number;
  y: number;
  offsetX: number;
  offsetY: number;
  maxOffsetX: number;
  maxOffsetY: number;
  downX: number;
  downY: number;
  downTime: number;
  upTime: number;
  directionX: number;
  directionY: number;
  action: PointerAction;
}

interface DragAction {
  type: string;
  x?: number;
  y?: number;
}

const eventOptions = { passive: false };
const CLICK_THRESHOLD = 3;
const SWIPE_THRESHOLD = 50;

export const useDragger = () => {
  const $handle = useRef<HTMLDivElement>(null);

  const [info, dispatch] = useReducer(
    (state: DragInfo, action: DragAction) => {
      const newState = { ...state, state: action.type };

      switch (action.type) {
        case 'start':
          newState.isActive = true;
          newState.downTime = Date.now();
          newState.downX = action.x ?? 0;
          newState.downY = action.y ?? 0;
          newState.offsetX = 0;
          newState.offsetY = 0;
          newState.maxOffsetX = 0;
          newState.maxOffsetY = 0;
          newState.directionX = 0;
          newState.directionY = 0;
          newState.action = PointerAction.NONE;
          break;
        case 'move':
          if (action.x !== state.x || action.y !== state.y) {
            newState.x = action.x ?? 0;
            newState.y = action.y ?? 0;
            newState.offsetX = newState.x - newState.downX;
            newState.offsetY = newState.y - newState.downY;
            if (Math.abs(newState.offsetX) > Math.abs(newState.maxOffsetX)) newState.maxOffsetX = newState.offsetX;
            if (Math.abs(newState.offsetY) > Math.abs(newState.maxOffsetY)) newState.maxOffsetY = newState.offsetY;
            newState.directionX = state.x < newState.x ? 1 : -1;
            newState.directionY = state.y < newState.y ? 1 : -1;
          }
          break;
        case 'end':
          {
            newState.isActive = false;
            newState.upTime = Date.now();

            const actionTime = newState.upTime - newState.downTime;
            if (actionTime < 1250) {
              const xClicked = Math.abs(newState.maxOffsetX) < CLICK_THRESHOLD;
              const yClicked = Math.abs(newState.maxOffsetY) < CLICK_THRESHOLD;
              const xSwiped = Math.abs(newState.maxOffsetX) > SWIPE_THRESHOLD;
              const ySwiped = Math.abs(newState.maxOffsetY) > SWIPE_THRESHOLD;

              if (newState.directionY > 0 && !yClicked && !xSwiped) {
                newState.action = PointerAction.SWIPE_DOWN;
              } else if (newState.directionY < 0 && !yClicked && !xSwiped) {
                newState.action = PointerAction.SWIPE_UP;
              } else if (newState.directionX > 0 && !xClicked && !ySwiped) {
                newState.action = PointerAction.SWIPE_RIGHT;
              } else if (newState.directionX < 0 && !xClicked && !ySwiped) {
                newState.action = PointerAction.SWIPE_LEFT;
              } else if (xClicked && yClicked) {
                newState.action = PointerAction.CLICK;
              }
            }
          }
          break;
      }
      return newState;
    },
    {
      isActive: false, //
      state: 'none',
      x: 0,
      y: 0,
      offsetX: 0,
      offsetY: 0,
      maxOffsetX: 0,
      maxOffsetY: 0,
      downX: 0,
      downY: 0,
      downTime: 0,
      upTime: 0,
      directionX: 0,
      directionY: 0,
      action: PointerAction.NONE,
    },
  );

  const handleDownPointer = useCallback((e: PointerEvent) => {
    e.stopPropagation();
    e.preventDefault();
    switch (e.type) {
      case 'pointerdown':
        dispatch({ type: 'start', x: e.clientX, y: e.clientY });
        document.addEventListener('pointermove', handleDocPointer, eventOptions);
        document.addEventListener('pointerup', handleDocPointer, eventOptions);
        document.addEventListener('pointercancel', handleDocPointer, eventOptions);
        break;
    }
  }, []);

  const handleDocPointer = useCallback((e: PointerEvent) => {
    e.stopPropagation();
    e.preventDefault();
    switch (e.type) {
      case 'pointermove':
        dispatch({ type: 'move', x: e.clientX, y: e.clientY });
        break;
      case 'pointercancel':
      case 'pointerup':
        dispatch({ type: 'end' });
        removeDocumentEventsPointer();
        break;
    }
  }, []);

  const removeDocumentEventsPointer = () => {
    document.removeEventListener('pointermove', handleDocPointer);
    document.removeEventListener('pointerup', handleDocPointer);
    document.removeEventListener('pointercancel', handleDocPointer);
  };

  const handleDownTouch = useCallback((e: TouchEvent) => {
    e.stopPropagation();
    e.preventDefault();
    switch (e.type) {
      case 'touchstart':
        dispatch({ type: 'start', x: e.touches[0].clientX, y: e.touches[0].clientY });
        document.addEventListener('touchmove', handleDocTouch, eventOptions);
        document.addEventListener('touchend', handleDocTouch, eventOptions);
        document.addEventListener('touchcancel', handleDocTouch, eventOptions);
        break;
    }
  }, []);

  const handleDocTouch = useCallback((e: TouchEvent) => {
    e.stopPropagation();
    e.preventDefault();
    switch (e.type) {
      case 'touchmove':
        dispatch({ type: 'move', x: e.touches[0].clientX, y: e.touches[0].clientY });
        break;
      case 'touchcancel':
      case 'touchend':
        dispatch({ type: 'end' });
        removeDocumentEventsTouch();
        break;
    }
  }, []);

  const removeDocumentEventsTouch = () => {
    document.removeEventListener('touchmove', handleDocTouch);
    document.removeEventListener('touchend', handleDocTouch);
    document.removeEventListener('touchcancel', handleDocTouch);
  };

  const handleMouseDown = useCallback((e: MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    switch (e.type) {
      case 'mousedown':
        dispatch({ type: 'start', x: e.clientX, y: e.clientY });
        document.addEventListener('mousemove', handleDocMouse, eventOptions);
        document.addEventListener('mouseup', handleDocMouse, eventOptions);
        break;
    }
  }, []);

  const handleDocMouse = useCallback((e: MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    switch (e.type) {
      case 'mousemove':
        dispatch({ type: 'move', x: e.clientX, y: e.clientY });
        break;
      case 'mouseup':
        dispatch({ type: 'end', x: e.clientX, y: e.clientY });
        removeDocumentEventsMouse();
        break;
    }
  }, []);

  const removeDocumentEventsMouse = () => {
    document.removeEventListener('touchmove', handleDocTouch);
    document.removeEventListener('touchend', handleDocTouch);
    document.removeEventListener('touchcancel', handleDocTouch);
  };

  useEffect(() => {
    $handle.current?.addEventListener('pointerdown', handleDownPointer);
    $handle.current?.addEventListener('touchstart', handleDownTouch, { passive: true });
    $handle.current?.addEventListener('mousedown', handleMouseDown);
    return () => {
      $handle.current?.removeEventListener('pointerdown', handleDownPointer);
      $handle.current?.removeEventListener('touchstart', handleDownTouch);
      $handle.current?.removeEventListener('mousedown', handleMouseDown);
    };
  }, [$handle.current]);

  return { ...info, $handle };
};
