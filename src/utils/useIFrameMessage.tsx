import { useEffect, useState } from 'react';
import { useDebug } from '../components/debug/useDebug';

interface IMessage {
  action: string;
  payload: string;
  uid?: string;
}

// communicate between iframes
// this is used to control the global play state of multiple widgets

export const useIFrameMessage = () => {
  const { debug } = useDebug('message');
  const [linked, setLinked] = useState(false);
  const [data, setData] = useState<IMessage>({ action: '', payload: '' });
  const [uid, setUid] = useState<string>('');
  //   const [status, setStatus] = useState<'playing' | 'paused'>('paused');
  const [parentOrigin, setParentOrigin] = useState<string>('');

  //   console.log('message', { debug });

  function send(action: string, payload = '') {
    if (linked) {
      try {
        if (debug) console.log('postMessage', { action, payload, uid });
        window.parent.postMessage({ action, payload, uid }, parentOrigin);
      } catch {
        setLinked(false);
      }
    }
  }

  useEffect(() => {
    function handleMessage(event: MessageEvent<IMessage>) {
      if (event.data?.action && event.data?.payload) {
        switch (event.data?.action) {
          case 'link':
            setParentOrigin(event.data.payload);
            setLinked(true);
            break;
          case 'uid':
            setUid(event.data.payload);
            break;
        }
        setData(event.data);
      }
    }
    window.addEventListener('message', handleMessage);

    return () => window.removeEventListener('message', handleMessage);
  }, []);

  useEffect(() => {
    if (linked) send('handshake');
  }, [linked]);

  return { send, linked, data };
};
