import { useCallback, useEffect, useRef, useState } from 'react';
import { isValidImageSrc } from './isValidImageSrc';

export const useImage = (props: { src: string; alt?: string; hide?: boolean; onError?: (img: HTMLImageElement) => void; onSuccess?: (img: HTMLImageElement) => void }) => {
  const [isError, setIsError] = useState(!isValidImageSrc(props.src));
  const resolvedSrc = isError ? props.alt : props.src;
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const $img = useRef<HTMLImageElement>(null);

  const handleEvent = useCallback(
    (e: Event) => {
      setIsLoading(false);
      if (!$img.current) return;
      if (props?.hide === true) {
        $img.current.style.visibility = '';
      }
      switch (e.type) {
        case 'load':
          setIsSuccess(true);
          props?.onSuccess?.($img.current);
          break;

        case 'error':
          {
            const _alt = props.alt?.replace(/^\/\//, `${window.location.protocol}://`) ?? '';
            if ($img.current.src === _alt) {
              $img.current.style.visibility = 'hidden';
            } else {
              $img.current.src = props.alt ?? '';
              $img.current.addEventListener('error', handleEvent, { once: true });
              setIsError(true);
              props?.onError?.($img.current);
            }
          }
          break;
      }
    },
    [props],
  );

  const onChange: MutationCallback = useCallback(
    (changes) => {
      changes.forEach((change) => {
        if (change.attributeName?.includes('src')) {
          if ($img.current && isValidImageSrc($img.current.src)) {
            //
          } else {
            setIsLoading(false);
            handleEvent({ type: 'error' } as Event);
          }
        }
      });
    },
    [handleEvent],
  );

  useEffect(() => {
    if ($img.current) {
      const imgElement = $img.current;
      const observer = new MutationObserver(onChange);
      observer.observe(imgElement, { attributes: true });
      if (isValidImageSrc(props.src)) {
        if (imgElement.complete) {
          if (imgElement.naturalWidth <= 0) {
            handleEvent({ type: 'error' } as Event);
          } else {
            handleEvent({ type: 'load' } as Event);
          }
        } else {
          setIsLoading(true);
          imgElement.addEventListener('error', handleEvent);
          imgElement.addEventListener('load', handleEvent);
        }

        return () => {
          setIsLoading(false);
          imgElement.removeEventListener('error', handleEvent);
          imgElement.removeEventListener('load', handleEvent);
          observer.disconnect();
        };
      } else {
        setIsLoading(false);
        handleEvent({ type: 'error' } as Event);
      }
    }
  }, [handleEvent, onChange, props.src]);

  return { src: resolvedSrc, $img, isLoading, isSuccess, isError };
};
