import { useEffect, useState } from 'react';
import { inBrowser } from './Utils';

function getTopHref() {
  if (inBrowser) {
    let topHref = 'unknown';
    try {
      topHref = window?.top?.location?.href ?? 'null';
    } catch (err) {
      console.log(err);
    }
    return topHref;
  }
  return '';
}

export function useIsIframed() {
  const [isFramed, setIsFramed] = useState(false);

  useEffect(() => {
    setIsFramed(getTopHref() !== self.location.href);
  }, []);

  return isFramed;
}
