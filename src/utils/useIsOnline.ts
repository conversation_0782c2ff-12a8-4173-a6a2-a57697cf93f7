import { useEffect, useState } from 'react';

export const useIsOnline = () => {
  const [isOnline, setIsOnline] = useState(true);

  function handleNetwork(e: Event) {
    switch (e.type) {
      case 'offline':
        setIsOnline(false);
        break;
      case 'online':
        setIsOnline(true);
        break;
    }
  }

  //   const [networkState, setNetworkState] = useState({
  //     isOnline: navigator.onLine,
  //     effectiveType: '',
  //     downlink: 0,
  //     rtt: 0,
  //   });

  useEffect(() => {
    // const updateNetState = () => {
    //   const connection = navigator.connection;
    //   if (connection) {
    //     setNetworkState({
    //       isOnline: navigator.onLine,
    //       effectiveType: connection.effectiveType,
    //       downlink: connection.downlink,
    //       rtt: connection.rtt,
    //     });
    //   }
    // };
    // window.addEventListener('load', updateNetState);
    window.addEventListener('online', handleNetwork);
    window.addEventListener('offline', handleNetwork);

    return () => {
      //   window.removeEventListener('load', updateNetState);
      window.removeEventListener('online', handleNetwork);
      window.removeEventListener('offline', handleNetwork);
    };
  }, []);

  return isOnline;
};
