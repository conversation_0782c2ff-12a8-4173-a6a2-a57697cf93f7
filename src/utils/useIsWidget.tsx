import { useLocation } from 'react-router-dom';
import { useSettings } from '../components/settings/useSettings';
import { buildUrl } from '../framework/buildUrl';
import { useEnv } from '../framework/useEnv';

export function isWidgetPath(path: string) {
  return /^\/widget(-swell)?\//.test(path);
}

export function useIsWidget() {
  return useSettings().isWidget;
}

export function useIsWidgetPath() {
  const loc = useLocation();
  return isWidgetPath(loc.pathname);
}

export function useActionBaseUrl() {
  const env = useEnv();
  const { isWidget } = useSettings();
  const url = buildUrl({ host: env.CANONICAL_DOMAIN, protocol: 'https' });
  return isWidget ? url : '';
}
