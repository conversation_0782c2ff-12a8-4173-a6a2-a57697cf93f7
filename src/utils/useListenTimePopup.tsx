import { useCallback, useEffect, useRef } from 'react';
import { useCurrentSwell } from '../api/gql.loadSwellById';
import { FollowPopupInner } from '../components/common/followpopup/FollowPopupInner';
import { useAuth } from '../components/login/useAuth';
import { useAudioStatus } from '../components/player/AudioPlayer/useAudioStatus';
import { usePopup } from '../components/popup/usePopup';
import { useEnv } from '../framework/useEnv';
import { useOrchestration } from '../framework/useOrchestration';
import { PlayerStatus } from '../models/models';
import { trackFollowSwellcastPopup } from '../tracking/Tracking';

// Define types for clarity
// interface SwellData {
//   swellcast?: {
//     owner?: {
//       alias?: string;
//     };
//     masterRef?: {
//       params?: Record<string, unknown>;
//     };
//   };
// }

export const useListenTimePopup = () => {
  const { isRecording } = useOrchestration();
  const { loggedIn } = useAuth();
  const env = useEnv();
  const TIME_BEFORE_FOLLOW_POPUP = parseInt(env?.TIME_BEFORE_FOLLOW_POPUP ?? '10000', 10);
  const status = useAudioStatus();
  const { data: swell, isSuccess } = useCurrentSwell();
  const popup = usePopup();

  // Use refs to track state without causing re-renders
  const totalTimeRef = useRef(0);
  const poppedRef = useRef(false);
  const aliasRef = useRef('');
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startedPlaying = useRef(0);

  // Debounced popup rendering
  const showPopupDebounced = useCallback(() => {
    if (loggedIn || isRecording || poppedRef.current) return;

    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    timerRef.current = setTimeout(() => {
      requestAnimationFrame(() => {
        popup.showPopup(<FollowPopupInner params={swell?.masterRef?.params ?? {}} />);
        trackFollowSwellcastPopup(aliasRef.current);
        poppedRef.current = true;
        timerRef.current = null;
      });
    }, 50);
  }, [loggedIn, isRecording, popup, swell?.masterRef?.params]);

  // Update alias and reset state on swell change
  useEffect(() => {
    if (isSuccess) {
      const newAlias = swell?.swellcast?.owner?.alias ?? '';
      if (newAlias !== aliasRef.current) {
        aliasRef.current = newAlias;
        totalTimeRef.current = 0;
        poppedRef.current = false;
      }
    }
  }, [isSuccess, swell]);

  // Track listening time with interval
  useEffect(() => {
    if (poppedRef.current || loggedIn || !isSuccess) return;

    const loop = () => {
      const now = Date.now();
      const delta = now - startedPlaying.current;
      totalTimeRef.current += delta;
      startedPlaying.current = now;

      if (totalTimeRef.current >= TIME_BEFORE_FOLLOW_POPUP) {
        showPopupDebounced();
      } else {
        timerRef.current = setTimeout(loop, 1000); // re-schedule
      }
    };

    if (status === PlayerStatus.PLAYING) {
      startedPlaying.current = Date.now();
      timerRef.current = setTimeout(loop, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      if (status === PlayerStatus.PLAYING && startedPlaying.current) {
        totalTimeRef.current += Date.now() - startedPlaying.current;
      }
    };
  }, [status, loggedIn, isSuccess, TIME_BEFORE_FOLLOW_POPUP, showPopupDebounced]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  return null;
};
