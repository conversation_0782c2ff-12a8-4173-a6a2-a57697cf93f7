import isEqual from 'lodash/isEqual';
import { useState } from 'react';
import { isBrowser } from './Utils';

function getLocalStoreObject<T = null>(id: string, defaultValue?: T): T {
  let val: T = defaultValue ?? null as T;
  if (isBrowser()) {
    const raw = window.localStorage.getItem(id);
    if (raw) {
      try {
        val = JSON.parse(raw);
      } catch (err) {
        console.log(err);
        //
      }
    }
  }
  return val;
}

export function useLocalStorage<T>(id: string, initialValue: T) {
  const [data, setData] = useState<T>(getLocalStoreObject(id, initialValue));

  function merge(delta: T) {
    const res = { ...data, ...delta };
    if (!isEqual(data, res)) {
      setData(res);
    }
  }

  const update = (delta: Partial<T>) => {
    const newData = { ...data, ...delta };
    if (!isEqual(newData, data)) {
      window.localStorage.setItem(id, JSON.stringify(newData));
      setData(newData);
    }
  };

  return {
    data,
    setData: update,
    update,
    merge,
  };
}
