import React, { useCallback, useEffect } from 'react';

import { useDebug } from '../components/debug/useDebug';
import { usePopup } from '../components/popup/usePopup';
import { MessagePopperContent } from './MessagePopperContent';

/*

In the case where a swell is posted and user is navigated from /me/ back to this site,
There may be a query parameter "msg={localStorageItem}".
In that storage object there is a JSON object containing the content for a popup.
After the popup shows, replace the url so no one shares this endpoint
and erase the localStorage object - one and done.

*/
export interface IMessagePopper {
  title?: string;
  description?: string;
  delay?: number;
  actions: {
    color?: string;
    label: string;
    style?: React.CSSProperties;
    onClickDismiss: boolean;
    onClickNavigate: string;
  }[];
}

export const useMessagePopper = () => {
  const { debug } = useDebug('msg');
  const pop = usePopup();

  // Pre-create the popup component rendering function
  const showMessagePopup = useCallback(
    (data: IMessagePopper, storageKey?: string) => {
      // Use requestAnimationFrame to ensure this runs during an idle frame
      requestAnimationFrame(() => {
        pop.showPopup(<MessagePopperContent data={data} />);
        if (storageKey) {
          window.localStorage.removeItem(storageKey);
        }
      });
    },
    [pop],
  );

  useEffect(() => {
    if (debug) {
      // add to url to test: ?debug=msg&msg=me_message
      window.localStorage.setItem('me_message', '{"delay":2000, "title":"Congratulations!","description":"Your Swell has been posted. You can edit your post and moderate it in the Swell app. Dive into the Swell app to unleash your creativity and engage with your audience.<br><br>Share your Swell with your friends, family, and colleagues. Let them in on the action and have them join the conversation.","actions":[{"label":"Download App","color":"fcb950","onClickDismiss":false,"onClickNavigate":"https://app.swell.life?utm_source=webPostAudio"},{"label":"Cancel","color":"cccccc","onClickDismiss":true,"onClickNavigate":null}]}');
    }

    const params = new URLSearchParams(window.location.search);
    if (params.has('msg') && !!params.get('msg')) {
      const msgKey = params.get('msg') ?? '';
      const msg = window.localStorage.getItem(msgKey);

      if (!debug) {
        window.history.replaceState({}, '', window.location.pathname);
      }

      try {
        // Parse JSON outside of setTimeout to catch errors early
        const data = JSON.parse(msg ?? '') as IMessagePopper;
        const delay = data?.delay ?? 0;

        // For long delays, use requestIdleCallback if available
        if (delay > 1000 && 'requestIdleCallback' in window) {
          window.requestIdleCallback(() => {
            setTimeout(() => showMessagePopup(data, msgKey), delay);
          });
        } else {
          setTimeout(() => showMessagePopup(data, msgKey), delay);
        }
      } catch (e) {
        console.error('Failed to parse message data:', e);
      }
    }
  }, [debug, showMessagePopup]);
};
