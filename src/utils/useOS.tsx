import debounce from 'lodash/debounce';
import { useEffect, useState } from 'react';
import { OS } from '../models/models';
import { getMobileOperatingSystem } from './Utils';

// capture OS and update on resize

export const useOS = () => {
  const [os, setOS] = useState(OS.SERVER);

  // change OS on screen size to accomodate the browser inspector mobile emulators - for dev
  useEffect(() => {
    setOS(getMobileOperatingSystem());
    const handleResize = debounce(() => {
      setOS(getMobileOperatingSystem());
    }, 500);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return os;
};

// export const useIsMobile = () => {
//   const os = useOS();
//   return os === OS.IOS || os === OS.ANDROID || os === OS.WINDOWS_PHONE;
// };
