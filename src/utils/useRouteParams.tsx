import { useParams } from 'react-router-dom';
import { useSettings } from '../components/settings/useSettings';
import { RouteParams, SwellListType } from '../models/models';

export function safeDecodeURIComponent(param: string, defaultValue = null) {
  let value = defaultValue ?? param;
  try {
    value = decodeURIComponent(param);
  } catch {
    // console.log(value, err);
  }
  return value;
}

export const useRouteParams = () => {
  const { countryCode } = useSettings();
  const params = useParams<RouteParams>();
  return { listType: SwellListType.SWELLCAST, countryCode, ...params, search: safeDecodeURIComponent(params?.search ?? '') };
};
