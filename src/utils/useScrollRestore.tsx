import { useEffect } from 'react';
import { NavigationType, useLocation, useNavigationType } from 'react-router-dom';
import { ScrollRestoreCache } from './ScrollRestoreCache';
import { inBrowser } from './Utils';

if (inBrowser && 'scrollRestoration' in window.history) {
  window.history.scrollRestoration = 'manual';
}

export const useScrollRestore = (isReady: boolean = true) => {
  const loc = useLocation();
  const navType = useNavigationType();
  const key = `key_${loc.pathname}`;

  useEffect(() => {
    ScrollRestoreCache[key] = ScrollRestoreCache?.[key] ?? { path: [0, 0], top: 0 };
    function handleScroll() {
      ScrollRestoreCache[key].top = window.scrollY;
      ScrollRestoreCache[key].path.push(window.scrollY);
    }
    window.addEventListener('scroll', handleScroll);
    return () => {
      // for some inexplicable reason, the next-to-last scroll position is the actual last position. So I have to record a path and then rewind it one step when the page pushes
      ScrollRestoreCache[key].top = ScrollRestoreCache[key].path[ScrollRestoreCache[key].path.length - 2];
      ScrollRestoreCache[key].path = [ScrollRestoreCache[key].top, ScrollRestoreCache[key].top];
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    switch (navType) {
      case NavigationType.Pop:
        if (isReady) {
          window.scrollTo({ top: ScrollRestoreCache?.[key]?.top ?? 0, behavior: 'auto' });
        }
        break;
      case NavigationType.Push:
        window.scrollTo({ top: 0, behavior: 'auto' });
        break;
    }
  }, [navType, isReady]);

  return {};
};
