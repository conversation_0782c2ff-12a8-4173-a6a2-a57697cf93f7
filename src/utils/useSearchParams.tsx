import { isEqual } from 'lodash';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

function getSearchParams<T>(search: string, defaultParams: Partial<T> = {}) {
  const searchParams = new URLSearchParams(search);
  const params: Partial<T> = {};

  for (const key of searchParams.keys()) {
    const value = searchParams.get(key);
    if (value !== null) {
      // If the value can be parsed as JSON, do so
      try {
        params[key as keyof T] = JSON.parse(value);
      } catch {
        params[key as keyof T] = value as unknown as T[keyof T];
      }
    }
  }
  return { ...defaultParams, ...params };
}

export function useSearchParams<T extends Record<string, string | number | boolean>>(defaultParams: T = {} as T) {
  const nav = useNavigate();
  const { search, pathname } = useLocation();
  const [params, setParams] = useState({ ...defaultParams, ...getSearchParams<T>(search) });

  useEffect(() => {
    const newParams = { ...defaultParams, ...params, ...getSearchParams<T>(search) };
    if (!isEqual(newParams, params)) {
      setParams(newParams);
    }
  }, [search]);

  const update = (delta: T) => {
    const newParams = { ...params };

    (Object.keys(delta) as Array<keyof typeof delta>).forEach((key) => {
      if (delta[key] === null || delta[key] === undefined) {
        delete newParams[key];
      } else {
        newParams[key] = delta[key];
      }
    });

    if (!isEqual(newParams, params)) {
      const searchParams = new URLSearchParams(search);

      // Update URL parameters
      for (const [key, value] of Object.entries(delta)) {
        if (value === null || value === undefined) {
          searchParams.delete(key);
        } else {
          searchParams.set(key, String(value));
        }
      }
      setParams(newParams);
      // Replace URL parameters without reloading the page
      nav(
        {
          pathname,
          search: searchParams.toString(),
        },
        { replace: true },
      );
    }
  };

  return { ...params, update };
}

const useActiveSearchParams = (): URLSearchParams => {
  const loc = useLocation();
  const entries = Object.fromEntries(new URLSearchParams(loc.search));
  const searchParams = new URLSearchParams();
  // trying to make these case insensitive
  Object.keys(entries).forEach((k) => {
    searchParams.set(k, entries[k]);
    if (k.toLowerCase() !== k) {
      searchParams.set(k.toLowerCase(), entries[k]);
    }
  });
  return searchParams;
};

export function useSearchParam<T = string>(param: string, defaultValue: T = null as T) {
  const searchParams = useActiveSearchParams();
  const value = searchParams.get(param.toLowerCase());
  return value !== null ? (value as T) : defaultValue;
}

// export const useSearchParamJson = <T,>(param: string, defaultValue: T = null) => {
//   const value = useSearchParam<string>(param);
//   let data: T;
//   try {
//     data = JSON.parse(value);
//   } catch (err) {
//     data = null;
//   }
//   return (data !== null ? data : defaultValue) as T;
// };
