import { useQuery } from '@tanstack/react-query';
import { DEFAULT_SERVER_SETTINGS } from '../framework/settings/DEFAULT_SERVER_SETTINGS';
import { serverSettingsKey } from '../framework/settings/serverSettingsKey';
import { IServerSettings } from '../models/models';

export function useSettingsData() {
  const query = useQuery<IServerSettings>({
    enabled: false,
    queryFn: async () => DEFAULT_SERVER_SETTINGS,
    queryKey: serverSettingsKey,
    gcTime: Infinity,
  });

  return { ...DEFAULT_SERVER_SETTINGS, ...query.data };
}
