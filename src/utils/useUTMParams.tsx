import { useMemo } from 'react';
import { useSettings } from '../components/settings/useSettings';
import { UTMParams } from './UTM.class';

/**
 * Grab the UTM parameters for the current visitor.
 *
 * * On the server and during hydration it returns defaults + query string
 *   (so markup is deterministic and cache-friendly).
 * * After React mounts it merges in any cookie-persisted values exactly once.
 */

export function useUTMParams(): UTMParams {
  const { utmParams } = useSettings();
  return useMemo(() => utmParams, [utmParams]);
}
