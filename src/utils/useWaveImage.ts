import { useColorMode } from '../components/common/colormode/useColorMode';
import { Bruno<PERSON>ave<PERSON>ard, BrunoWaveCardDark } from '../framework/settings/settings';
import { ColorMode } from '../models/models';

export const useWaveImage = () => {
  const { mode } = useColorMode();
  const dynamicWave = mode === ColorMode.LIGHT ? BrunoWaveCardDark : BrunoWaveCard;
  return { lightModeWave: BrunoWaveCardDark, darkModeWave: BrunoWaveCard, dynamicWave };
};
