import { Footer } from '../../components/footer/footer';
import { WebsiteHeader } from '../../components/header/header';
import { SwellLink } from '../../components/swellbutton/SwellButton';
import { DenyIframe } from '../../utils/DenyIframe';

export const Page404 = (): JSX.Element => (
  <DenyIframe>
    <div className='shadow position-relative' style={{ zIndex: 10 }}>
      <WebsiteHeader />
    </div>
    <Page404Content />
    <Footer />
  </DenyIframe>
);

export const Page404Content = (): JSX.Element => (
  <div className='flex-center p-5' style={{ minHeight: '70vh' }}>
    <div className='d-flex flex-column gap-5'>
      <h1>404: Page not found</h1>

      <div className='d-flex flex-column gap-3'>
        <h3>The page you are looking for is either...</h3>
        <div className='d-flex flex-column gap-2'>
          <h4>A) Late for work</h4>
          <h4>B) On vacation</h4>
          <h4>C) &quot;Working&quot; from home</h4>
        </div>
      </div>

      <p>All we know for sure is that it&apos;s not here.</p>

      <div className='d-flex gap-2'>
        <SwellLink.Primary className='btn-lg' to='/'>
          Go to the home page
        </SwellLink.Primary>
        <SwellLink.Secondary className='btn-lg' to='https://www.swell.life/contact' reloadDocument>
          Speak with the manager
        </SwellLink.Secondary>
      </div>
    </div>
  </div>
);
