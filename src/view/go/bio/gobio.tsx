// import './gobio.scss';

import { useQuery } from '@tanstack/react-query';

import { CircleLoader } from '../../../components/common/circleloader/CircleLoader';
import { useIsSwellApp, useWebviewAction } from '../../../finder/FinderUtils';
import { CENTER_COLUMN_MAX_WIDTH } from '../../../framework/settings/CENTER_COLUMN_MAX_WIDTH';
import { DEFAULT_COUNTRY_CODE, isLocal } from '../../../framework/settings/settings';
import { Localize } from '../../../i18n/Localize';
import { dataLayer } from '../../../tracking/Tracking';
import { getProxyUrl } from '../../../utils/getProxyUrl';
import { useRouteParams } from '../../../utils/useRouteParams';

interface GoBioDataItem {
  id: string;
  image: string;
  swellId: string;
  link: string;
}

interface GoBioData {
  items: GoBioDataItem[];
}

const urls: Record<string, string> = {
  IN: 'https://assets.swell.life/marketing/bio-in/data.json',
  US: 'https://assets.swell.life/marketing/bio/data.json',
};

const useBio = ({ countryCode }: { countryCode: string }) => {
  return useQuery({
    queryKey: ['bio', countryCode],
    queryFn: async () => {
      let apiUrl = urls?.[countryCode.toUpperCase()] ?? urls['US'];
      if (isLocal) {
        apiUrl = getProxyUrl(apiUrl);
      }
      const res = await fetch(apiUrl);
      if (!res.ok) {
        return Promise.reject({ message: 'ERROR', description: Localize.GENERAL_ERROR });
      }
      const data = await res.json();
      return data;
    },
  });
};

export const GoBio = () => {
  return (
    <>
      {/* <WebsiteHeader /> */}
      {/* <WebsiteHeaderSpacer /> */}
      <div style={{ display: 'flex', minHeight: 'calc(100vh - 132px)', width: '100%' }}>
        <GoBioContent />
      </div>
      {/* <Footer /> */}
    </>
  );
};

const GoBioContent = () => {
  const params = useRouteParams();
  const data = useBio({ countryCode: params?.countryCode || DEFAULT_COUNTRY_CODE });

  if (data.isSuccess) {
    return <GoBioSuccess data={data.data} />;
  }

  if (data.isError) {
    return <GoBioError />;
  }

  return <GoBioLoading />;
};

const GoBioError = () => (
  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '75vh', width: '100%' }}>
    <h2>{Localize.GENERAL_ERROR}</h2>
  </div>
);

const GoBioLoading = () => (
  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '75vh', width: '100%' }}>
    <CircleLoader />
  </div>
);

const GoBioSuccess = ({ data }: { data: GoBioData }) => {
  const isSwellApp = useIsSwellApp();
  const action = useWebviewAction({ id: 'bio' });

  return (
    <div style={{ margin: '0 auto', display: 'flex', flexWrap: 'wrap', gap: 10, padding: 30, justifyContent: 'center', maxWidth: CENTER_COLUMN_MAX_WIDTH }}>
      {data.items.map((item, i) => (
        <a
          key={`gobio${i}`}
          className='gobio-item'
          href={item.link}
          onClick={(e) => {
            dataLayer({
              event: 'click-bio',
              name: item.id,
            });
            if (isSwellApp) {
              e.preventDefault();
              e.stopPropagation();
              action.swell(item.swellId);
            }
          }}
        >
          <img style={{ objectFit: 'contain', width: '100%' }} src={item.image} />
        </a>
      ))}
      <div className='gobio-item' />
      <div className='gobio-item' />
      <div className='gobio-item' />
      <div className='gobio-item' />
      <div className='gobio-item' />
      <div className='gobio-item' />
      <div className='gobio-item' />
      <div className='gobio-item' />
    </div>
  );
};
