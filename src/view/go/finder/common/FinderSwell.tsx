import { focusManager } from '@tanstack/react-query';
import classNames from 'classnames';
import { CSSProperties, useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { Mugshot } from '../../../../components/common/Mugshot';
import { useIsSwellApp, useWebviewAction } from '../../../../finder/FinderUtils';
import { BrunoWaveCard, isLocal } from '../../../../framework/settings/settings';
import { OpenApiSwellcastSwellResponse } from '../../../../generated/graphql';
import { dataLayer } from '../../../../tracking/Tracking';
import { deorphan } from '../../../../utils/Utils';
import { isValidImageSrc } from '../../../../utils/isValidImageSrc';
import { getAuthorFullname, getSwellLink2 } from '../../../../utils/swell-utils';
import { timeago } from '../../../../utils/timeago';
import { useIsIframed } from '../../../../utils/useIsIframed';

export const FinderSwell = ({ swell, actionId }: { swell: OpenApiSwellcastSwellResponse; actionId: string }) => {
  const isSwellApp = useIsSwellApp();
  const action = useWebviewAction({ id: `finder/${actionId}` });
  const isFramed = useIsIframed();
  const img = swell?.articles?.[0]?.image ?? ''; //getArticleImage(swell);
  const snippet = swell.snippet;
  const useCombined = true;

  return (
    <Link
      key={swell.id}
      className='fix p-2 d-grid gap-2'
      style={{ gridTemplateColumns: img ? 'min-content auto min(160px, 30%)' : 'min-content auto 0' }}
      to={getSwellLink2(swell)}
      target={isFramed || isLocal ? '_blank' : '_self'}
      onClick={(e) => {
        dataLayer({
          event: `click-${actionId}`,
          name: swell.id,
        });
        if (isSwellApp) {
          e.preventDefault();
          e.stopPropagation();
          action.swell(swell.id);
        }
      }}
    >
      <div>{useCombined ? null : <Mug swell={swell} />}</div>
      <div className='d-flex flex-column gap-3 overflow-hidden'>
        {useCombined ? <CombinedTitle swell={swell} /> : <TitleUser swell={swell} />}
        {snippet ? (
          <div className='line-clamp-3 h5-normal ps-2' style={{ fontStyle: 'italic' }}>
            &hellip;{snippet}
          </div>
        ) : null}
      </div>
      <div className='position-relative landscape'>
        {img ? (
          <>
            <div className='rounded-2 border-grey-30 d-sm-block d-none position-relative landscape'>
              <BasicCardImage className='cover' src={img} />
            </div>
            <div className='rounded-2 border-grey-30 d-sm-none d-block position-relative square'>
              <BasicCardImage className='cover' src={img} />
            </div>
          </>
        ) : null}
      </div>
    </Link>
  );
};

const BasicCardImage = (props: React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>) => {
  const hasSrc = isValidImageSrc(props?.src ?? '');
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(!hasSrc);
  const $img = useRef<HTMLImageElement>(null);
  const $canvas = useRef<HTMLCanvasElement>(null);
  const isGif = hasSrc && /^(?!data:).*\.gif/i.test(props?.src ?? '');
  const [imgStyle, setImgStyle] = useState<CSSProperties>(props?.style ?? { display: 'block' });
  const [imgClass, setImgClass] = useState<string>('absolute-fill cover');
  const [cvsStyle, setCVSStyle] = useState<CSSProperties>({ ...(props?.style ?? {}), objectFit: 'cover', display: 'block' });
  const [isWindowActive, setIsWindowActive] = useState(true);
  const onError = () => setIsError(true);
  const onLoad = () => setIsSuccess(true);
  const imgSrc = isError ? BrunoWaveCard : hasSrc ? props.src : BrunoWaveCard;

  const onVisibilityChange = () => setIsWindowActive(!document.hidden);

  const draw = () => {
    // kill animated gif
    if ($img.current && $canvas.current) {
      const cvs = $canvas.current;
      const w = (cvs.width = $img.current.naturalWidth);
      const h = (cvs.height = $img.current.naturalHeight);
      const ctx = cvs.getContext('2d');
      if (ctx) {
        ctx.drawImage($img.current, 0, 0, w, h);
      }
    }
  };

  useEffect(() => {
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('visibilitychange', onVisibilityChange, false);
      window.addEventListener('focus', onVisibilityChange, false);
    }

    return () => {
      window.removeEventListener('visibilitychange', onVisibilityChange);
      window.removeEventListener('focus', onVisibilityChange);
    };
  }, []);

  useEffect(() => {
    if (isWindowActive) {
      draw();
    }
  }, [isWindowActive]);

  useEffect(() => {
    if (isError) {
      setCVSStyle((s) => ({ ...s, display: 'none' }));
      setImgStyle((s) => ({ ...s, display: 'block' }));
      setImgClass('absolute-fill contain');
    } else if (isSuccess) {
      if (isGif) {
        setCVSStyle((s) => ({ ...s, display: 'block' }));
        setImgStyle((s) => ({ ...s, display: 'none' }));
        setImgClass('absolute-fill cover');
        draw();
      } else {
        setCVSStyle((s) => ({ ...s, display: 'none' }));
        setImgStyle((s) => ({ ...s, display: 'block' }));
        setImgClass('absolute-fill cover');
      }
    }
  }, [isSuccess, isError, isGif, focusManager]);

  return (
    <>
      <canvas ref={$canvas} className={classNames('absolute-fill cover', props.className)} style={cvsStyle} />
      <img ref={$img} {...props} src={imgSrc} loading='lazy' className={imgClass} style={imgStyle} onError={onError} onLoad={onLoad} />
    </>
  );
};

const Mug = ({ swell }: { swell: OpenApiSwellcastSwellResponse }) => {
  return (
    <div style={{ alignSelf: 'flex-start' }}>
      <Mugshot size={48} image={swell.author?.image ?? ''} alt={`@${swell.author?.alias ?? ''}`} />
    </div>
  );
};

const CombinedTitle = ({ swell }: { swell: OpenApiSwellcastSwellResponse }) => {
  return (
    <div className='d-flex gap-2 w-100'>
      <Mug swell={swell} />
      <TitleUser swell={swell} />
    </div>
  );
};

const TitleUser = ({ swell }: { swell: OpenApiSwellcastSwellResponse }) => {
  return (
    <div className='overflow-hidden d-flex flex-column justify-content-center'>
      <h5 className='mb-0 mt-2 line-clamp-3 lh-1'>{deorphan(swell?.title ?? '')}</h5>
      <div className='text-break text-truncate overflow-hidden text-nowrap'>
        <span>{getAuthorFullname(swell.author)}</span>
        <DotSpace />
        <span>@{swell.author?.alias}</span>
        <DotSpace />
        <span>{timeago(new Date(Date.parse(swell?.createdOn ?? Date.now().toString())))}</span>
        {(swell?.repliesCount ?? 0) > 0 ? (
          <>
            <DotSpace />
            <span>
              {swell.repliesCount} {swell.repliesCount === 1 ? 'reply' : 'replies'}
            </span>
          </>
        ) : null}
      </div>
    </div>
  );
};

const DotSpace = () => <span>&nbsp;&middot;&nbsp;</span>;
