import { OpenApiSwellcastSwellResponse } from '../../../../generated/graphql';
import { FinderSwell } from './FinderSwell';

export const FinderSwells = ({ title, swells }: { title: string; swells: OpenApiSwellcastSwellResponse[] }) => {
  const dateStr = new Date().toLocaleDateString('en-us', { weekday: 'long', month: 'short', day: 'numeric' });
  return (
    <div className='finder-swell p-2'>
      {title ? (
        <div className='p-3'>
          <h2 className='mb-0'>{title}</h2>
          <p className='fs-4'>{dateStr}</p>
        </div>
      ) : null}

      <hr className='my-2' />

      <div className='d-flex flex-column gap-1'>
        {swells.map((swell, i) => (
          <FinderSwell key={`${swell.id}_${i}`} swell={swell} actionId='featured' />
        ))}
      </div>
    </div>
  );
};
