import { BounceLoader } from '../../../../components/common/bounceloader/BounceLoader';

export const FinderSwellsLoading = ({ title }: { title: string }) => {
  const dateStr = new Date().toLocaleDateString('en-us', { weekday: 'long', month: 'short', day: 'numeric' });

  return (
    <div className='finder-swell p-2' style={{ maxWidth: 700, margin: '0 auto' }}>
      <div className='p-3 border-bottom mb-3'>
        <div className='p-2'>
          <h2 className='mb-0'>{title}</h2>
          <p>{dateStr}</p>
        </div>
      </div>
      <div className='p-2 flex-center' style={{ minHeight: '50vh' }}>
        <BounceLoader />
      </div>
    </div>
  );

  //   return (
  //     <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
  //       <BounceLoader />
  //     </div>
  //   );
};
