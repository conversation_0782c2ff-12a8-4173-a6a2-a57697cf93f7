import { useMemo } from 'react';
import { useSearchPage } from '../../../../api/gql.getSearchPage';
import { FinderSwells } from '../common/FinderSwells';
import { FinderSwellsError } from '../common/FinderSwellsError';
import { FinderSwellsLoading } from '../common/FinderSwellsLoading';

export const FinderFeatured = () => {
  const searchPage = useSearchPage();
  const swells = useMemo(() => {
    if (searchPage.isSuccess) {
      const arr = [...searchPage.data?.featured ?? [], ...searchPage.data?.trendingSwells ?? []];
      const ids = arr.map((o) => o.id);
      const filtered = arr.filter(({ id }, index) => !ids.includes(id, index + 1));
      return filtered.sort((a, b) => ((a?.createdOn ?? '') < (b?.createdOn ?? '') ? 1 : -1));
    }
    return [];
  }, [searchPage]);

  if (searchPage.isLoading) {
    return <FinderSwellsLoading title='Featured Conversations' />;
  }

  if (searchPage.isSuccess) {
    return <FinderSwells title='Featured Conversations' swells={swells} />;
  }

  if (searchPage.isError) {
    return <FinderSwellsError />;
  }

  return <div />;
};
