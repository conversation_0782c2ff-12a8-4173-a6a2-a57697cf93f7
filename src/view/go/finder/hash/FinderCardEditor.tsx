import { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSettings } from '../../../../components/settings/useSettings';
import { ColorMode, ILayoutMode } from '../../../../models/models';
import { useSearchParams } from '../../../../utils/useSearchParams';

export const FinderCardEditor = () => {
  const { edit } = useSearchParams({ edit: 0 });
  if (edit === 1) {
    return <FinderCardEditorInner />;
  }
  return <div />;
};

const DEFAULT_PARAMS = {
  background: 'inherit',
  colormode: ColorMode.AUTO, //
  editorsPicks: 0,
  showtitle: 1,
  mugshots: 0,
  mugshotw: 50,
  showdesc: 1,
  showplay: 1,
  allowplay: 0,
  edit: 1,
  search: '',
  ui: '',
  title: '',
  cardwidth: 250,
  mugshotwidth: 250,
  layoutmode: ILayoutMode.LANDSCAPE,
  seeAllUrl: '',
};

const FinderCardEditorInner = () => {
  //   const color = useColorMode();
  const searchParams = useSearchParams<typeof DEFAULT_PARAMS>(DEFAULT_PARAMS);
  const settings = useSettings();
  // const _title = title || search;
  //   const layoutmode: ILayoutMode = Object.values(ILayoutMode).includes(searchParams.layoutmode) ? (searchParams.layoutmode as ILayoutMode) : ILayoutMode.LANDSCAPE;
  const history = useNavigate();
  const location = useLocation();

  const {
    background,
    colormode, //
    editorsPicks,
    showtitle,
    showdesc,
    search,
    title,
    cardwidth,
    mugshotwidth,
    edit,
    layoutmode,
    allowplay,
    ui,
    showplay,
    mugshots,
    mugshotw,
    seeAllUrl,
  } = useMemo(() => {
    const layoutmode = Object.values(ILayoutMode).includes(searchParams.layoutmode) ? (searchParams.layoutmode as ILayoutMode) : ILayoutMode.LANDSCAPE;
    return { ...DEFAULT_PARAMS, ...searchParams, layoutmode };
  }, [searchParams]);

  // Function to update a single search parameter
  const updateQueryParam = (paramName: string, paramValue: string) => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set(paramName, paramValue);
    history({ search: searchParams.toString() }, { replace: true });
  };

  const [searchTerm, setSearchTerm] = useState(search);

  useEffect(() => {
    if (searchTerm.trim()) {
      const t = setTimeout(() => {
        const term = searchTerm.trim().replace(/\s+/g, ' ');
        updateQueryParam('search', term);
        setSearchTerm(term);
      }, 2000);
      return () => {
        clearTimeout(t);
      };
    }
  }, [searchTerm]);

  useEffect(() => {
    updateQueryParam('colormode', colormode);
  }, []);

  if (settings.isProd || edit !== 1) {
    return null;
  }

  return (
    <div className='p-4 bg-grey-lighter d-flex flex-wrap gap-3 align-items-center w-100 text-black'>
      <div>
        <label className='mb-2' style={{ display: 'block' }}>
          UI
        </label>
        <select
          name='ui'
          value={ui}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value=''>default</option>
          <option value='dynamic'>dynamic</option>
        </select>
      </div>

      <div className={mugshots === 1 ? 'd-none' : ''}>
        <label className='mb-2' style={{ display: 'block' }}>
          width (card)
        </label>
        <input
          type='range'
          min={100}
          max={500}
          value={cardwidth}
          name='cardwidth'
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        />
      </div>

      <div className={mugshots === 0 ? 'd-none' : ''}>
        <label className='mb-2' style={{ display: 'block' }}>
          width (mugshot)
        </label>
        <input
          type='range'
          min={100}
          max={500}
          value={mugshotwidth}
          name='mugshotwidth'
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        />
      </div>

      <div className='d-none'>
        <label className='mb-2' style={{ display: 'block' }}>
          shape
        </label>
        <select
          name='layoutmode'
          value={layoutmode}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='square'>square</option>
          <option value='portrait'>portrait</option>
          <option value='landscape'>landscape</option>
        </select>
      </div>

      <div style={{ flex: '1 1 auto' }}>
        <label className='mb-2' style={{ display: 'block' }}>
          title
        </label>
        <input
          style={{ width: '100%', display: 'block' }}
          type='text'
          value={title}
          name='title'
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        />
      </div>

      <div style={{ flex: '1 1 auto' }}>
        <label className='mb-2' style={{ display: 'block' }}>
          search
        </label>
        <input
          style={{ width: '100%', display: 'block' }}
          type='text'
          value={searchTerm}
          name='search'
          onChange={(e) => {
            setSearchTerm(e.currentTarget.value);
          }}
        />
      </div>

      <div style={{ flex: '1 1 auto' }}>
        <label className='mb-2' style={{ display: 'block' }}>
          See All URL
        </label>
        <input
          style={{ width: '100%', display: 'block' }}
          type='text'
          value={seeAllUrl}
          name='seeAllUrl'
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        />
      </div>

      <div className='d-none'>
        <label className='mb-2' style={{ display: 'block' }}>
          play button
        </label>
        <select
          name='showplay'
          value={showplay}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='1'>show</option>
          <option value='0'>hide</option>
        </select>
      </div>

      <div>
        <label className='mb-2' style={{ display: 'block' }}>
          title
        </label>
        <select
          name='showtitle'
          value={showtitle}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='1'>show</option>
          <option value='0'>hide</option>
        </select>
      </div>

      <div className='d-none'>
        <label className='mb-2' style={{ display: 'block' }}>
          description
        </label>
        <select
          name='showdesc'
          value={showdesc}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='1'>show</option>
          <option value='0'>hide</option>
        </select>
      </div>

      <div>
        <label className='mb-2' style={{ display: 'block' }}>
          Editors Picks
        </label>
        <select
          name='editorsPicks'
          value={editorsPicks}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='1'>only</option>
          <option value='0'>default</option>
        </select>
      </div>

      <div>
        <label className='mb-2' style={{ display: 'block' }}>
          mugshots
        </label>
        <select
          name='mugshots'
          value={mugshots}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='0'>off</option>
          <option value='1'>on</option>
        </select>
      </div>

      <div className='d-none'>
        <label className='mb-2' style={{ display: 'block' }}>
          mugshot size
        </label>
        <input
          type='range'
          min={50}
          max={500}
          value={mugshotw}
          name='mugshotw'
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        />
      </div>

      <div>
        <label className='mb-2' style={{ display: 'block' }}>
          color
        </label>
        <select
          name='colormode'
          value={colormode}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='auto'>auto</option>
          <option value='dark'>dark</option>
          <option value='light'>light</option>
        </select>
      </div>

      <div>
        <label className='mb-2' style={{ display: 'block' }}>
          background
        </label>
        <select
          name='background'
          value={background}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='inherit'>inherit</option>
          <option value='transparent'>transparent</option>
        </select>
      </div>

      <div>
        <label className='mb-2' style={{ display: 'block' }}>
          Allow Play
        </label>
        <select
          name='allowplay'
          value={allowplay}
          onChange={(e) => {
            updateQueryParam(e.currentTarget.name, e.currentTarget.value);
          }}
        >
          <option value='0'>off</option>
          <option value='1'>on</option>
        </select>
      </div>
    </div>
  );
};
