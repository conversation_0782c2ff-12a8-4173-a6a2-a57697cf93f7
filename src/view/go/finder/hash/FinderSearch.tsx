import { CSSProperties, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSearch } from '../../../../api/gql.search';
import { BounceLoaderPage } from '../../../../components/common/bounceloader/BounceLoaderPage';
import { ContentSection } from '../../../../components/sections/ContentSection';
import { NormalSection } from '../../../../components/sections/InfiniteSectionsView';
import { SectionAuthor } from '../../../../components/sections/blocks/SectionAuthor';
import { SectionSwell } from '../../../../components/sections/blocks/SectionSwell';
import { HomePageRenderType, HomePageSectionDataType, SwellFlags } from '../../../../generated/graphql';
import { ILayoutMode } from '../../../../models/models';
import { useSearchParams } from '../../../../utils/useSearchParams';
import { FinderSwells } from '../common/FinderSwells';
import { FinderSwellsError } from '../common/FinderSwellsError';
import './finder-search.scss';

const defaultParams = {
  background: 'inherit',
  showtitle: 1,
  mugshots: 0, //
  mugshotw: 50,
  showdesc: 1,
  showplay: 0,
  allowplay: 1,
  edit: 0,
  search: '',
  ui: '',
  title: '',
  editorsPicks: 0,
  cardwidth: 250,
  mugshotwidth: 250,
  layoutmode: ILayoutMode.LANDSCAPE,
  seeAllUrl: '',
};

export const FinderSearch = ({ forceParams = {} }: { forceParams?: Partial<typeof defaultParams> }) => {
  const params = useSearchParams(defaultParams);
  const showtitle = params.showtitle;
  const search = params.search;
  const ui = forceParams?.ui ?? params.ui;
  const title = params.title;
  const cardwidth = params.cardwidth;
  const mugshotwidth = params.mugshotwidth;
  const mugshots = params.mugshots;
  const editorsPicks = params.editorsPicks;
  const [enabled, setEnabled] = useState(false);
  const _title = showtitle === 1 ? title || search : null;
  const flags = [SwellFlags.Snippets, ...(editorsPicks === 1 ? [SwellFlags.OnlyEditorsPick] : [])];
  const response = useSearch({ search, flags, enabled });

  // SWEB-242: api called twice - this "fixes" it
  useEffect(() => {
    const t = setTimeout(() => setEnabled(true), 200);
    return () => clearTimeout(t);
  }, []);

  useEffect(() => {
    if (params.background === 'transparent') {
      document.body.classList.add('bg-transparent');
    }

    if (params.background === 'inherit') {
      document.body.classList.remove('bg-transparent');
    }
  }, [params.background]);

  const authors = response.isSuccess && response.data.swells ? Object.values(response.data.swells.reduce((acc, obj) => ({ ...acc, [obj.author?.alias ?? '']: obj.author }), {})) : [];
  const sectionType = mugshots !== 0 ? HomePageSectionDataType.Authors : HomePageSectionDataType.Swells;
  const Renderer = mugshots !== 0 ? SectionAuthor : SectionSwell;

  const section = {
    id: '15',
    label: _title,
    type: HomePageRenderType.Horizontal,
    sectionDataType: sectionType,
    sectionType,
    swells: response.data?.swells ?? [],
    stations: [],
    emptyMessage: null,
    swellcasts: [],
    prompts: [],
    hashtags: [],
    images: [],
    promos: [],
    sectionParams: {
      seeAllUrl: params.seeAllUrl,
    },
    authors: authors,
  };

  const loc = useLocation();

  if (params.search == '') {
    return <Navigate to={loc.pathname + '?search=all'} />;
  }

  if (response.isSuccess) {
    if ((response.data.swells?.length ?? 0) === 0) {
      return (
        <div className='p-4'>
          <h2 className='text-shadow-mode'>{title}</h2>
          <p className='h4-normal'>No Swells posted yet</p>
        </div>
      );
    } else {
      if (ui === 'dynamic') {
        return (
          <div className='sections-view'>
            <div className='section-swells' style={{ '--grid-col-width': cardwidth } as CSSProperties}>
              <ContentSection
                section={section as NormalSection} //
                Renderer={Renderer}
                style={{ '--grid-col-width': mugshots === 1 ? mugshotwidth : cardwidth } as CSSProperties}
                className='go-finder-search'
              />
            </div>
          </div>
        );
      }
      return (
        <FinderSwells
          title={_title ?? ''} //
          swells={response.data?.swells ?? []}
        />
      );
    }
  }

  if (response.isError) {
    return <FinderSwellsError />;
  }

  return <BounceLoaderPage />;
};
