import { useMemo } from 'react';
import { useSearchPage } from '../../../../api/gql.getSearchPage';
import { FinderSwells } from '../common/FinderSwells';
import { FinderSwellsError } from '../common/FinderSwellsError';
import { FinderSwellsLoading } from '../common/FinderSwellsLoading';

export const FinderLatest = () => {
  const searchPage = useSearchPage();
  const swells = useMemo(() => {
    if (searchPage.isSuccess) {
      return searchPage.data?.swells?.sort((a, b) => (a.createdOn ?? '' < (b.createdOn ?? '') ? 1 : -1)) ?? [];
    }
    return [];
  }, [searchPage]);

  if (searchPage.isLoading) {
    return <FinderSwellsLoading title='Latest Conversations' />;
  }

  if (searchPage.isError) {
    return <FinderSwellsError />;
  }

  if (searchPage.isSuccess) {
    return <FinderSwells title='Latest Conversations' swells={swells} />;
  }
};
