import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { useAuth } from '../../../components/login/useAuth';
import { usePopup } from '../../../components/popup/usePopup';
import { useIsSwellApp } from '../../../finder/FinderUtils';
import { BecomePremiumPopupInner, SignInPopupInner } from './PremiumWall';

export const AccessWall = ({ children, premium = false, ignore = false }: { children: React.ReactNode; premium?: boolean; ignore?: boolean }) => {
  const isApp = useIsSwellApp();
  const auth = useAuth();
  const [eject, setEject] = useState(false);
  const [popupShown, setPopupShown] = useState(false);
  const popup = usePopup();
  const [isReady, setIsReady] = useState(false);

  // ask use to login to view the content
  useEffect(() => {
    if (!isApp) {
      if (auth.isReady) {
        if (!auth.loggedIn) {
          setPopupShown(true);
          popup.showPopup(<SignInPopupInner close={() => setEject(true)} />);
        } else if (premium && !auth.isPremium) {
          setPopupShown(true);
          popup.showPopup(<BecomePremiumPopupInner close={() => setEject(true)} />);
        }
      }
    }
  }, [auth.isReady, auth.loggedIn, auth.isPremium]);

  useEffect(() => {
    if (auth.isReady) {
      setIsReady(true);
    }
  }, [auth.isReady]);

  // if popup is dismissed, eject
  useEffect(() => {
    if (!popup.opened && popupShown) {
      setEject(true);
    }
  }, [popup.opened]);

  // circumvent for some reason
  if (ignore) {
    return children;
  }
  // if we're in the app, just show the content
  if (isApp) {
    return children;
  }

  if (!isReady) {
    return <BounceLoaderPage />;
  }

  if (!auth.isReady) {
    return <BounceLoaderPage />;
  }

  // leave the page
  if (eject) {
    return <Navigate to='/' />;
  }

  if (auth.isReady) {
    if (auth.loggedIn) {
      if (premium) {
        if (auth.isPremium) {
          return children;
        } else {
          return null;
        }
      } else {
        return children;
      }
    } else {
      // this is where the popup should show
      return null;
    }
    // return <Redirect to='/' push={true} />;
  }

  // waiting for auth
  return <BounceLoaderPage />;
};

// const BecomePremiumPopupInner = ({ close }: { close?(): void }) => {
//   return (
//     <PopupContainer close={close}>
//       <div style={{ width: 250 }} className='d-flex flex-column gap-3 align-items-center'>
//         <div>
//           <SwellLogo id='BecomePremiumPopupInner' style={{ height: 30 }} className='mx-auto' />
//           <div className='p-1'></div>
//         </div>
//         <div className='text-center fs-3 m-0' style={{ lineHeight: '1.4' }}>
//           Become a Swell Audio Creator to&nbsp;see this content!
//         </div>
//         <div>
//           <div className='p-1'></div>
//           <a href='https://www.swell.life/audio-creator-program' target='_blank' className='btn btn-primary px-3' onClick={(e) => e.stopPropagation()} rel='noreferrer'>
//             Learn More
//           </a>
//         </div>
//       </div>
//     </PopupContainer>
//   );
// };
