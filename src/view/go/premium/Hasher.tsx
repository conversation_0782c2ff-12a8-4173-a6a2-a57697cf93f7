import '../../../assets/css/main.scss';

import React, { useEffect, useRef, useState } from 'react';
import { inBrowser } from '../../../utils/Utils';
import { safeDecodeURIComponent } from '../../../utils/useRouteParams';
import { HashContext } from './HashContext';

const getHash = () => (inBrowser ? safeDecodeURIComponent(window.location.hash.replace('#', '')) || '___' : '');

export const HashProvider = ({ children }: { children: React.ReactNode }) => {
  const [hash, setHash] = useState(getHash());
  const ScrollRestoreCache = useRef<Record<string, { top: number; path: number[] }>>({});
  const $container = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleHashChange = () => {
      setHash(getHash());
    };
    window.addEventListener('hashchange', handleHashChange);
    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  useEffect(() => {
    // initialize scroll history
    ScrollRestoreCache.current[hash] = ScrollRestoreCache.current?.[hash] ?? { path: [0, 0], top: 0 };
  }, [hash]);

  useEffect(() => {
    // when html is ready, auto scroll
    if ($container.current) {
      let mounted = true;

      const handleScroll = () => {
        ScrollRestoreCache.current[hash].top = window.scrollY;
      };

      const scrollToElement = () => {
        if ($container.current) {
          const $target = document.getElementById(hash);
          if ($target) {
            document.getElementById(hash)?.scrollIntoView({ behavior: 'instant' });
          } else {
            window.scrollTo({ top: ScrollRestoreCache.current?.[hash]?.top ?? 0, behavior: 'auto' });
          }
        }
      };

      scrollToElement();

      setTimeout(() => {
        if (mounted) {
          scrollToElement();
          window.addEventListener('scroll', handleScroll);
        }
      }, 300);

      return () => {
        mounted = false;
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [hash, $container.current]);

  return (
    <HashContext.Provider value={{ hash }}>
      <div ref={$container}>{children}</div>
    </HashContext.Provider>
  );
};

export const HasherLink = (props: { to: string } & React.DetailedHTMLProps<React.AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>) => {
  return (
    <a {...{ ...props, to: undefined }} href={`#${props.to}`}>
      {props.children}
    </a>
  );
};
