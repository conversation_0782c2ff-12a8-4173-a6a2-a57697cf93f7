import OGImage from '@images/PremiumOG.jpg';
import SwellColors from '@images/swell-colors.png';
import { PlayArrow } from '@mui/icons-material';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { useWebviewAction } from '../../../finder/FinderUtils';
import { HelmetBuilder } from '../../../framework/HelmetBuilder';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { OGModel } from '../../../models/models';
import { dataLayer } from '../../../tracking/Tracking';
import { slugify, toBgUrl } from '../../../utils/Utils';
import useIntersectionObserver from '../../../utils/useIntersectionObserver';
import { Page404 } from '../../error/Page404';
import { VideoContext } from '../videos/VideoContext';
import { VideoContextProvider } from '../videos/VideoContextProvider';
import { HashProvider, HasherLink } from './Hasher';
import { PremiumWall } from './PremiumWall';
import { useHasher } from './useHasher';
import { useNoAppRedirect } from './useNoAppRedirect';
import { usePremiumHub } from './usePremiumHub';

interface PremiumItem {
  id: string;
  image: string;
  title: string;
  description: string;
  video: string;
  link: string;
  swellId: string;
  isNew: boolean;
}

export interface PremiumSection {
  slug: string;
  title: string;
  items: PremiumItem[];
  listimage?: string;
  pageimage?: string;
  description?: string;
}

export interface PremiumHubData {
  __lookup: Record<string, PremiumSection>;
  version: number;
  heading: string;
  description: string;
  image: string;
  'last updated': string;
  sections: PremiumSection[];
}

const premiumHubOG: OGModel = {
  title: 'Swell Premium Hub',
  description: 'Welcome to the Premium Hub of Swell, an exclusive space dedicated to empowering premium Swellcasters on their path to success and growth.',
  image: OGImage,
};

const iconShadow = 'drop-shadow(1px 1px 2px rgb(0 0 0 / 0.7))';

export const PremiumHubPage = () => {
  return (
    <PremiumWall>
      <PremiumHubContent />
    </PremiumWall>
  );
};

const PremiumHubContent = () => {
  const data = usePremiumHub();
  useNoAppRedirect();
  const $container = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  // this garbage is to make react SSR happy to hydrate the client - not sure why this mucking fudgery exists but...
  useEffect(() => {
    setIsLoading(data.isLoading);
  }, [data]);

  if (isLoading) {
    return <BounceLoaderPage />;
  }

  if (data.isSuccess) {
    return (
      <VideoContextProvider>
        <HashProvider>
          <HelmetBuilder {...premiumHubOG} />
          <div ref={$container} style={{ maxWidth: BreakPoints.M, margin: '0 auto', paddingBottom: 200 }}>
            <PremiumHubRouter />
          </div>
        </HashProvider>
      </VideoContextProvider>
    );
  }

  if (data.isError) {
    return <Page404 />;
  }

  return null;
};

// export const PremiumHubContent = () => {
//   //   const hasher = useHasher();
//   //   const data = usePremiumHub();
//   //   const $container = useRef<HTMLDivElement>();
//   //   useNoAppRedirect();

//   if (data.isSuccess) {
//     return <PremiumHubRouter />;
//     // return (
//     //   <div ref={$container} style={{ maxWidth: BreakPoints.M, margin: '0 auto', paddingBottom: 200 }}>
//     //     {/* <pre>{JSON.stringify({ hash: hasher.hash }, null, 2)}</pre> */}
//     //     <PremiumHubRouter />
//     //   </div>
//     // );
//   }

//   return <BounceLoaderPage />;
// };

const PremiumHubRouter = () => {
  const hasher = useHasher();
  const data = usePremiumHub();

  if (data.isSuccess) {
    if (data.data.version === 2) {
      if (data.data.__lookup?.[hasher.hash]) {
        return <Section section={data.data.__lookup[hasher.hash]} version={data.data.version} />;
      } else {
        return <PremiumHubHome data={data.data} />;
      }
    } else {
      return <PremiumHubLegacyLayout data={data.data} />;
    }
  }

  return <BounceLoaderPage />;
};

const PremiumHubLegacyLayout = ({ data }: { data: PremiumHubData }) => {
  return (
    <>
      <div className='text-white text-shadow position-relative'>
        <img src={data.image} style={{ width: '100%' }} />
        <div className='p-4' style={{ position: 'absolute', bottom: 0, width: '100%' }}>
          <h1>{data.heading}</h1>
          <p>Updated {data['last updated']}</p>
        </div>
      </div>
      <div className='d-flex flex-column gap-2 p-4'>
        <h4 data-component='formatcopy' className='h4-normal' dangerouslySetInnerHTML={{ __html: data.description }}></h4>
      </div>
      {data.sections.map((section, i) => (
        <Section key={slugify(section.title, i.toString())} section={section} version={data.version} />
      ))}
    </>
  );
};

const PremiumHubHome = ({ data }: { data: PremiumHubData }) => {
  return (
    <>
      <PremiumHero data={data} />
      <div className='p-3'>
        <div className='gap-3' style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(min(45%, 240px), 1fr))' }}>
          {data.sections.map((section) => (
            <SectionHero key={`hero_${section.slug}`} section={section} />
          ))}
        </div>
      </div>
    </>
  );
};

const PremiumHubImageWithText = ({ children, image }: { children: React.ReactNode; image: string }) => {
  // Component puts white text (with shadow) on an image in the lower left corner.
  // Currently used for title images on premium hub subpages
  if (image) {
    return (
      <div className='text-white text-shadow position-relative' data-component='premium-image'>
        <img src={image} style={{ width: '100%', aspectRatio: '1920/1500', objectFit: 'cover', objectPosition: 'center' }} />
        <div className='p-4' style={{ position: 'absolute', bottom: 0, width: '100%' }}>
          {children}
        </div>
      </div>
    );
  } else {
    // account for old version with no image
    return (
      <div className='text-mode' data-component='premium-image-noimage'>
        <div className=''>{children}</div>
      </div>
    );
  }
};

const PremiumHero = ({ data }: { data: PremiumHubData }) => {
  return (
    <div data-component='premium-hero'>
      <PremiumHubImageWithText image={data.image}>
        <h1>{data.heading}</h1>
        <p>Updated {data['last updated']}</p>
      </PremiumHubImageWithText>
      <div className='d-flex flex-column gap-2 p-4'>
        <h4 data-component='formatcopy' className='h4-normal' dangerouslySetInnerHTML={{ __html: data.description }}></h4>
      </div>
    </div>
  );
};

const SectionHero = ({ section }: { section: PremiumSection }) => {
  const image = section?.listimage ?? SwellColors;
  const newCount = section.items.filter((e) => e.isNew).length;
  return (
    <HasherLink to={`${section.slug}`} className='fix' data-component='section-hero'>
      <div className='text-white text-shadow position-relative w-100' style={{ aspectRatio: '1080/1920' }}>
        <img src={image} style={{ width: '100%', height: '100%', objectFit: 'cover', objectPosition: 'center' }} />
        <div className='d-flex flex-column gap-2 p-3 fade-to-black' style={{ position: 'absolute', bottom: 0, width: '100%' }}>
          <div style={{ height: 20 }} />
          {newCount > 0 ? (
            <p className='bg-blue text-white px-4 fit-content' style={{ display: 'inline-block' }}>
              {newCount} NEW!
            </p>
          ) : null}
          <h3>{section.title}</h3>
          {section?.description ? <p className='line-clamp-4' dangerouslySetInnerHTML={{ __html: section.description }}></p> : null}
        </div>
      </div>
    </HasherLink>
  );
};

const Section = ({ section, version }: { section: PremiumSection; version: number }) => {
  const nonheader = (
    <>
      {section?.description ? <h4 data-component='formatcopy' className='h4-normal' dangerouslySetInnerHTML={{ __html: section.description }}></h4> : null}
      <hr className='thin' />
      {section.items.map((item, i) => (
        <SectionItem key={slugify(item.id, i)} item={item} />
      ))}
    </>
  );
  if (version === 2.0) {
    return (
      <>
        <PremiumHubImageWithText image={section.pageimage ?? ''}>
          <h2>{section.title}</h2>
        </PremiumHubImageWithText>
        <div className='d-flex flex-column gap-4 p-4' data-component='section'>
          {nonheader}
        </div>
      </>
    );
  } else {
    return (
      <div className='d-flex flex-column gap-4 p-4' data-component='section'>
        <PremiumHubImageWithText image={section.pageimage ?? ''}>
          <h2>{section.title}</h2>
        </PremiumHubImageWithText>
        {nonheader}
      </div>
    );
  }
};

const SectionItem = ({ item }: { item: PremiumItem }) => {
  return (
    <div className='d-flex flex-column gap-3 mt-3' data-component='section-item'>
      <a id={item.id} style={{ scrollMarginTop: 120 }} />
      <div className='d-flex flex-column gap-1'>
        {item.isNew ? (
          <p className='bg-blue text-white px-4 fit-content' style={{ display: 'inline-block' }}>
            NEW!
          </p>
        ) : null}
        <h3>{item.title}</h3>
        <p data-component='formatcopy' dangerouslySetInnerHTML={{ __html: item.description }}></p>
      </div>
      {item.video ? <SectionVideo item={item} /> : <SectionItemImage item={item} />}
    </div>
  );
};

const SectionItemImage = ({ item }: { item: PremiumItem }) => {
  const action = useWebviewAction({ id: 'premium' });
  return (
    <a
      href={item.link}
      onClick={(e) => {
        dataLayer({
          event: 'click-premium-item',
          name: item.id,
        });
        if (item.swellId) {
          action.swell(item.swellId);
          e.preventDefault();
        }
      }}
    >
      <img className='rounded' src={item.image} style={{ width: '100%' }} />
    </a>
  );
};

const SectionVideo = ({ item }: { item: PremiumItem }) => {
  const poster = item.image;
  const src = item.video;
  const controller = useContext(VideoContext);
  const $container = useRef<HTMLDivElement>(null);
  const $video = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const io = useIntersectionObserver($container, { rootMargin: '1px' });

  useEffect(() => {
    if (io?.isIntersecting === false && !isPlaying) {
      setIsActive(false);
    }
  }, [io]);

  useEffect(() => {
    if (isActive) {
      dataLayer({
        event: 'play-premium-video',
        name: item.id,
      });
    }
  }, [isActive]);

  return (
    <>
      <div ref={$container} className='position-relative bg-black cover landscape rounded' style={{ backgroundImage: toBgUrl(poster), minHeight: 100 }}>
        {isActive ? (
          <video
            className='square contain'
            style={{ height: '100%' }}
            ref={$video}
            onPlay={() => {
              controller.setVideo($video);
              setIsActive(true);
              setIsPlaying(true);
            }}
            onPause={() => {
              setIsPlaying(false);
            }}
            controls={isActive}
            controlsList=''
            autoPlay={true}
          >
            <source src={src} type='video/mp4' />
          </video>
        ) : null}
        {isActive ? null : (
          <div
            onClick={() => {
              setIsActive(true);
            }}
            className='p-2 fit-content absolute-center'
          >
            <PlayArrow style={{ color: '#fff', fontSize: 65, filter: iconShadow, WebkitBoxShadow: iconShadow }} />
          </div>
        )}
      </div>
    </>
  );
};
