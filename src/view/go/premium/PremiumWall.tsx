import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { useAuth } from '../../../components/login/useAuth';
import { PopupContainer, PopupMessage } from '../../../components/popup/PopupContainer';
import { usePopup } from '../../../components/popup/usePopup';
import { useIsSwellApp } from '../../../finder/FinderUtils';
import { dataLayer } from '../../../tracking/Tracking';
import { stopPropagation } from '../../../utils/stopPropagation';

export const PremiumWall = ({ children }: { children: React.ReactNode }) => {
  const isApp = useIsSwellApp();
  const auth = useAuth();
  const [eject, setEject] = useState(false);
  const popup = usePopup();
  const [showContent, setShowContent] = useState(isApp);

  const closePopup = () => {
    popup.close();
    setEject(true);
  };

  // ask user to login to view the content
  useEffect(() => {
    if (!isApp) {
      if (auth.isReady) {
        if (!auth.loggedIn) {
          popup.showPopup(<SignInPopupInner close={closePopup} />, false);
        } else if (!auth.isPremium) {
          popup.showPopup(<BecomePremiumPopupInner close={closePopup} />, false);
        } else {
          setShowContent(true);
          popup.close();
        }
      }
    }
  }, [auth.isReady, auth.loggedIn, auth.isPremium]);

  // waiting for auth to validate/reject
  if (!auth.isReady) {
    return <BounceLoaderPage />;
  }

  // if we're in the app, just show the content
  if (isApp) {
    return children;
  }

  // leave the page
  if (eject) {
    return <Navigate to='/' />;
  }

  if (showContent) {
    return children;
  }
  return null;
};

export const SignInPopupInner = ({ close }: { close?(): void }) => {
  const onClick: React.MouseEventHandler<HTMLAnchorElement> = (e) => {
    e.stopPropagation();
    dataLayer({ event: 'signin', context: 'popup' });
  };

  return (
    <PopupContainer close={close}>
      <PopupMessage
        showLogo={true} //
        description='Sign in to view this content.'
      >
        <a href='/me/' className='btn btn-primary px-3' onClick={onClick}>
          Sign In
        </a>
      </PopupMessage>
    </PopupContainer>
  );
};

export const BecomePremiumPopupInner = ({ close }: { close?(): void }) => {
  return (
    <PopupContainer close={close}>
      <PopupMessage
        showLogo={true} //
        description='Become a Swell Audio Creator to&nbsp;see this content!'
      >
        <a href='https://www.swell.life/audio-creator-program' target='_blank' className='btn btn-primary px-3' onClick={stopPropagation} rel='noreferrer'>
          Learn More
        </a>
      </PopupMessage>
    </PopupContainer>
  );
};
