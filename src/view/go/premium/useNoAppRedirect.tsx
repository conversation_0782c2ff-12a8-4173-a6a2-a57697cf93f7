import { useEffect } from 'react';
import { useAuth } from '../../../components/login/useAuth';
import { useIsSwellApp } from '../../../finder/FinderUtils';

export const useNoAppRedirect = () => {
  const auth = useAuth();
  const isApp = useIsSwellApp();

  useEffect(() => {
    if (!isApp) {
      if (auth.isReady && !auth.loggedIn) {
        window.location.replace('/');
      }
    }
  }, [auth.loggedIn, auth.isReady, isApp]);
};
