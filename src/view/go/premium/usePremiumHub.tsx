import { useQuery } from '@tanstack/react-query';
import { useSettings } from '../../../components/settings/useSettings';
import { isLocal } from '../../../framework/settings/settings';
import { inBrowser, slugify } from '../../../utils/Utils';
import { getProxyUrl } from '../../../utils/getProxyUrl';
import { PremiumHubData, PremiumSection } from './PremiumHubPage';

const PREMIUM_STAGE = 'https://stageassets.swell.life/marketing/bio/premium.json';
const PREMIUM_PROD = 'https://assets.swell.life/marketing/bio/premium.json';

export const usePremiumHub = () => {
  const settings = useSettings();
  return useQuery<PremiumHubData>({
    enabled: inBrowser,
    queryKey: ['premiumhub', settings.stage],
    queryFn: async () => {
      let url = settings.stage === 'prod' ? PREMIUM_PROD : PREMIUM_STAGE;
      if (isLocal) url = getProxyUrl(url);

      const r = await fetch(url, {
        method: 'GET',
        headers: {
          'content-type': 'application/json',
        },
      });
      const data = (await r.json()) as PremiumHubData;
      // add unique slugs to sections for routes/anchors
      data.sections.forEach((section, i) => {
        section.slug = slugify(section.title, i);
      });

      data.__lookup = data.sections.reduce((sum, section) => {
        sum[section.slug] = section;
        section.items.forEach((item) => (sum[item.id] = section));
        return sum;
      }, {} as Record<string, PremiumSection>);

      return data;
    },
  });
};
