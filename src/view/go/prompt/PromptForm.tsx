import { useMemo, useRef, useState } from 'react';
import PromptHeaderImage from '../../../assets/images/prompt-form-header-2.jpg';
import { CircleLoader } from '../../../components/common/circleloader/CircleLoader';
import { Spacer } from '../../../components/common/Spacer';
import { useDebug } from '../../../components/debug/useDebug';
import { useAuth } from '../../../components/login/useAuth';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { getReCapchaKey } from '../../../framework/settings/reCaptchaKey';
import { dataLayer } from '../../../tracking/Tracking';
import { useLocalStorage } from '../../../utils/useLocalStorage';
import { useSearchParams } from '../../../utils/useSearchParams';
import { useUTMParams } from '../../../utils/useUTMParams';
import { toBgUrl } from '../../../utils/Utils';
import { AccessWall } from '../premium/AccessWall';
import { postSwellForm } from './submitForm';
import { useReCapcha } from './useReCapcha';

interface IPromptFormData {
  prompt: string;
  notes: string;
}

export const PromptFormPage = () => {
  const { debug } = useDebug('form');
  return (
    <AccessWall premium={false} ignore={debug}>
      <div className='position-relative flex-center overflow-hidden'>
        <div className='header-background' style={{ backgroundImage: toBgUrl(PromptHeaderImage) }} />
        <img src={PromptHeaderImage} style={{ maxWidth: BreakPoints.XL }} className='position-relative w-100' />
      </div>

      <div className='prompt-form-page d-flex flex-column align-items-center mx-auto gap-3' style={{ maxWidth: BreakPoints.S }}>
        <div className='rounded-sm border-grey-trans shadow-sm-md w-100 d-flex flex-column align-items-center'>
          <PromptSuggestionForm context='website' />
          <Spacer style={{ height: 50 }} />
        </div>
        <Spacer />
      </div>
    </AccessWall>
  );
};

const ErrorBlock = ({ errorMessage }: { errorMessage: string }) => {
  return (
    <h4 className='text-center rounded-1 p-4 bg-grey-dark bg-red text-white' role='alert'>
      ERROR: {errorMessage}
    </h4>
  );
};

const PromptSuggestionForm = ({ context }: { context: string }) => {
  const UTMParams = useUTMParams();
  const $form = useRef<HTMLFormElement>(null);
  const [status, setStatus] = useState<'loading' | 'error' | 'success' | 'none'>('none');
  const [errorMessage, setErrorMessage] = useState('');
  const store = useLocalStorage<IPromptFormData>('prompt-form', { prompt: '', notes: '' });
  const auth = useAuth();
  const searchParams = useSearchParams<{ alias: string; userid: string; thankyou: number; debug: string }>({ alias: '', userid: '', thankyou: 0, debug: '' });
  const alias = useMemo(() => (auth.loggedIn ? auth.user?.alias ?? '' : searchParams.alias), [auth.isReady, auth.loggedIn]);
  const userid = searchParams.userid;
  const disabled = status === 'loading';
  const debug = searchParams.debug === '1';
  const [reCaptchaToken, setReCaptchaToken] = useState('');
  const recap = useReCapcha({
    sitekey: getReCapchaKey(),
    onSuccess: (t) => {
      setReCaptchaToken(t);
      setStatus('none');
    },
  });

  const onSubmit: React.FormEventHandler<HTMLFormElement> = async (e) => {
    e.preventDefault();
    setStatus('loading');

    if (!reCaptchaToken) {
      setStatus('error');
      setErrorMessage('Please complete the ReCaptcha test. Thanks.');
      return;
    }

    const form = new FormData(e.currentTarget);
    const params = new URLSearchParams(form as unknown as string);
    const body = params.toString();
    const result = await postSwellForm(body);

    if (result.isSuccess) {
      setStatus('success');
      store.merge({ prompt: '', notes: '' });
      dataLayer({ event: 'prompt-suggestion-submit', context, swellcastAlias: alias });
    } else {
      const retry = await postSwellForm(body);
      if (retry.isSuccess) {
        setStatus('success');
        store.merge({ prompt: '', notes: '' });
        dataLayer({ event: 'prompt-suggestion-submit', context, swellcastAlias: alias });
      } else {
        setStatus('error');
        setErrorMessage(retry.message);
        dataLayer({ event: 'prompt-suggestion-error', context, swellcastAlias: alias });
      }
    }
    recap.reset();
  };

  const onKeyDown: React.FormEventHandler<HTMLElement> = (e) => e.stopPropagation();

  const onChange: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement> = (e) => {
    const delta = { [e.currentTarget.name]: e.currentTarget.value };
    store.merge(delta as unknown as IPromptFormData);
  };

  if (status === 'success' || searchParams.thankyou === 1) {
    return (
      <div className='p-3 py-5 px-lg-5 d-flex flex-column justify-content-center gap-4' style={{ maxWidth: 500 }}>
        <div className='p-5'>
          <div className='d-flex flex-column gap-3 text-center'>
            <h2>Thank you for your&nbsp;submission.</h2>
            <p>Our team will review and publish the prompt in a few&nbsp;days.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <form ref={$form} className='d-flex flex-column gap-3 p-4' onSubmit={onSubmit} style={{ width: 500, maxWidth: '100%' }}>
      <HiddenInput name='alias' value={alias} debug={debug} />
      <HiddenInput name='userid' value={userid} debug={debug} />
      <HiddenInput name='swellformid' value='promptSuggestion' debug={debug} />
      {Object.entries(UTMParams)
        .filter((e) => e[1])
        .map((e) => (
          <HiddenInput key={`hidden-${e[0]}`} name={e[0]} value={e[1]} debug={debug} />
        ))}
      <div className='d-flex flex-column gap-5'>
        <div className='d-flex flex-column gap-2 pt-4'>
          <div className='h4-normal'>Please submit your community prompt request below. Our team will review and publish the prompt.</div>
        </div>
        <div className='d-flex flex-column gap-2'>
          <label className='form-label h4'>Prompt Text (What should the prompt say)*</label>
          <textarea
            style={{ resize: 'none', fontSize: 16 }}
            disabled={disabled}
            className='form-control h-1' //
            placeholder='Enter text up to 200 characters...'
            defaultValue={store.data.prompt}
            maxLength={150}
            onChange={onChange}
            rows={6}
            tabIndex={1}
            required
            onKeyDown={onKeyDown}
            name='prompt'
          />
        </div>
        <div className='d-flex flex-column gap-2'>
          <label className='form-label h4'>Any Comments or Notes (optional)</label>
          <textarea
            style={{ resize: 'none', fontSize: 16 }}
            disabled={disabled}
            className='form-control' //
            placeholder='Enter text up to 500 characters...'
            defaultValue={store.data.notes}
            onChange={onChange}
            maxLength={500}
            rows={6}
            tabIndex={1}
            onKeyDown={onKeyDown}
            name='notes'
          />
          <div className='text-end mt-2'>
            <small className='text-mode'>*required</small>
          </div>
        </div>
        <div ref={recap.$container}></div>
      </div>
      {status === 'error' && errorMessage ? <ErrorBlock errorMessage={errorMessage} /> : null}
      <button className='btn btn-lg btn-primary shadow flex-center w-100' type='submit'>
        {status === 'loading' ? <CircleLoader size={20} color='#000' /> : 'Submit'}
      </button>
    </form>
  );
};

const HiddenInput = ({ name, value, debug }: { name: string; value: string; debug: boolean }) => {
  const $field = <input readOnly={true} className='form-control' type={debug ? 'text' : 'hidden'} name={name} value={value} />;

  if (debug) {
    return (
      <div className='d-flex gap-2 align-items-center'>
        <label className='form-label' style={{ flex: '0 0 33%' }}>
          {name}
        </label>
        {$field}
      </div>
    );
  }

  return $field;
};
