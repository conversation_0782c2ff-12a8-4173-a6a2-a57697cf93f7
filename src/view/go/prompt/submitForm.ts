import { isLocal } from '../../../framework/settings/settings';

export const postSwellForm = async (body: string) => {
  console.log('postSwellForm');
  if (isLocal) {
    return { isSuccess: true, message: '' };
  }
  return fetch('https://forms.swell.life/', {
    method: 'post',
    body,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
    .then((r) => r.json())
    .then(() => {
      return { isSuccess: true, message: '' };
    })
    .catch((e) => {
      console.log(e.message);
      return { isSuccess: false, message: 'There was a problem processing the form. Please try again.' };
    });
};
