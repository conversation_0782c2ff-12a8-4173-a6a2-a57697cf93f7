import { useEffect, useRef, useState } from 'react';

type RecapchaProps = {
  sitekey: string;
  onSuccess?: ReCaptchaV2.Parameters['callback'];
  onError?: ReCaptchaV2.Parameters['error-callback'];
};

export const useReCapcha = ({ sitekey, onSuccess, onError }: RecapchaProps) => {
  const $container = useRef<HTMLDivElement>(null);
  const [recapchaReady, setRecapchaReady] = useState(false);

  const isRecapchaReady = () => {
    return !!window?.grecaptcha?.render;
  };

  const renderRecapcha = () => {
    if ($container.current) {
      window.grecaptcha.render($container.current, { sitekey, callback: onSuccess, 'error-callback': onError });
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    const check = () => {
      const isReady = isRecapchaReady();
      setRecapchaReady(isReady);
      return isReady;
    };
    if (!check()) {
      timer = setTimeout(check, 1000);
      return () => {
        clearTimeout(timer);
      };
    }
  }, []);

  useEffect(() => {
    if (recapchaReady) {
      if ($container.current) {
        renderRecapcha();
      }
    }
  }, [recapchaReady]);

  const reset = () => {
    window?.grecaptcha?.reset();
  };

  return { $container, reset };
};
