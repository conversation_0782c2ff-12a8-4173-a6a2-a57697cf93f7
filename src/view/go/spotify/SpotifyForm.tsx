import React, { useMemo, useRef, useState } from 'react';
import { Spacer } from '../../../components/common/Spacer';
import { CircleLoader } from '../../../components/common/circleloader/CircleLoader';
import { useDebug } from '../../../components/debug/useDebug';
import { useAuth } from '../../../components/login/useAuth';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { getReCapchaKey } from '../../../framework/settings/reCaptchaKey';
import { dataLayer } from '../../../tracking/Tracking';
import { toBgUrl } from '../../../utils/Utils';
import { useLocalStorage } from '../../../utils/useLocalStorage';
import { useSearchParams } from '../../../utils/useSearchParams';
import { useUTMParams } from '../../../utils/useUTMParams';
import { AccessWall } from '../premium/AccessWall';
import { postSwellForm } from '../prompt/submitForm';
import { useReCapcha } from '../prompt/useReCapcha';
import HeaderImage from './publish-to-spotify-form-header-min.jpg';

interface ISpotifyFormData {
  hasMinSwells: boolean;
  willPostWeekly: boolean;
  notes: string;
}

const defaultFormData: ISpotifyFormData = {
  hasMinSwells: false,
  willPostWeekly: false,
  notes: '',
};

export const SpotifyFormPage = () => {
  const { debug } = useDebug('form');
  return (
    <AccessWall premium={false} ignore={debug}>
      <div className='position-relative flex-center overflow-hidden'>
        <div className='header-background' style={{ backgroundImage: toBgUrl(HeaderImage) }} />
        <img src={HeaderImage} style={{ maxWidth: BreakPoints.XL }} className='position-relative w-100' />
      </div>

      <div className='spotify-form-page d-flex flex-column align-items-center mx-auto gap-3' style={{ maxWidth: BreakPoints.S }}>
        <div className='rounded-sm border-grey-trans shadow-sm-md w-100 d-flex flex-column align-items-center'>
          <SpotifyForm context='website' />
          <Spacer style={{ height: 50 }} />
        </div>

        <Spacer />
      </div>
    </AccessWall>
  );
};

const ErrorBlock = ({ errorMessage }: { errorMessage: string }) => {
  return (
    <p
      className='text-center rounded-2 p-3 fw-bold'
      style={{
        color: '#c14d78',
        background: 'rgba(255,255,255,0.5)',
        border: '4px solid #c14d78',
      }}
      role='alert'
    >
      Error: {errorMessage}
    </p>
  );
};

const SpotifyForm = ({ context }: { context: string }) => {
  const UTMParams = useUTMParams();
  const $form = useRef<HTMLFormElement>(null);
  const [status, setStatus] = useState<'loading' | 'error' | 'success' | 'none'>('none');
  const [errorMessage, setErrorMessage] = useState('');
  const store = useLocalStorage<ISpotifyFormData>('spotify-form', defaultFormData);
  const auth = useAuth();
  const searchParams = useSearchParams<{ alias: string; userid: string; thankyou: number; debug: string }>({ alias: '', userid: '', thankyou: 0, debug: '' });
  const alias = useMemo(() => (auth.loggedIn ? auth.user?.alias ?? '' : searchParams.alias), [auth.isReady, auth.loggedIn]);
  const userid = searchParams.userid;
  const disabled = status === 'loading';
  const debug = searchParams.debug === '1';
  const [reCaptchaToken, setReCaptchaToken] = useState('');
  const recap = useReCapcha({
    sitekey: getReCapchaKey(),
    onSuccess: (t) => {
      setReCaptchaToken(t);
      setStatus('none');
    },
  });

  const onSubmit: React.FormEventHandler<HTMLFormElement> = async (e) => {
    e.preventDefault();
    setStatus('loading');

    if (!reCaptchaToken) {
      setStatus('error');
      setErrorMessage('Please complete the ReCaptcha test. Thanks.');
      return;
    }

    const form = new FormData(e.currentTarget);
    const params = new URLSearchParams(form as unknown as string);
    const body = params.toString();
    const result = await postSwellForm(body);

    if (result.isSuccess) {
      setStatus('success');
      store.merge(defaultFormData);
      dataLayer({ event: 'podcast-publish-submit', context, swellcastAlias: alias });
    } else {
      const retry = await postSwellForm(body);
      if (retry.isSuccess) {
        setStatus('success');
        store.merge(defaultFormData);
        dataLayer({ event: 'podcast-publish-submit', context, swellcastAlias: alias });
      } else {
        setStatus('error');
        setErrorMessage(retry.message);
        dataLayer({ event: 'podcast-publish-error', context, swellcastAlias: alias });
      }
    }
    recap.reset();
  };

  const onKeyDown: React.FormEventHandler<HTMLElement> = (e) => e.stopPropagation();

  const onChange: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement> = (e) => {
    const delta = { [e.currentTarget.name]: e.currentTarget.value };
    store.merge(delta as unknown as ISpotifyFormData);
  };

  const onChangeCheckbox: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const delta = { [e.currentTarget.name]: e.currentTarget.checked };
    store.merge(delta as unknown as ISpotifyFormData);
  };

  if (status === 'success' || searchParams.thankyou === 1) {
    return (
      <>
        {/* <Confetti recycle={false} numberOfPieces={200} colors={[ThemeColors.blue, ThemeColors.green, ThemeColors.premium_blue, ThemeColors.red, ThemeColors.squash]} /> */}
        <div className='p-3 py-5 px-lg-5 d-flex flex-column justify-content-center gap-4' style={{ maxWidth: 500 }}>
          <div className='p-5'>
            <div>
              {/* <h1>Suggest a Prompt</h1> */}
              <h2 className='mb-3'>Thank you for submitting your request.</h2>
              <p>Please allow us up to 5 business days to process your request. You will see a checkmark next to your profile image once your account has been upgraded. You will also receive an email once your Swellcast has been published to other podcasting platforms.</p>
              {/* <DownloadAppButton context='podcastPublish' /> */}
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <form ref={$form} className='d-flex flex-column gap-3 p-4' onSubmit={onSubmit} style={{ width: 500, maxWidth: '100%' }}>
      <HiddenInput name='alias' value={alias} debug={debug} />
      <HiddenInput name='userid' value={userid} debug={debug} />
      <HiddenInput name='swellformid' value='podcastPublish' debug={debug} />

      {Object.entries(UTMParams)
        .filter((e) => e[1])
        .map((e) => (
          <HiddenInput key={`hidden-${e[0]}`} name={e[0]} value={e[1] ?? ''} debug={debug} />
        ))}

      <div className='d-flex flex-column gap-5 fs-4'>
        {/* <div>
          <h1>Publish to Podcasting Platforms</h1>
          <p>Please submit this form if you would like your Swellcast to be considered for publishing by Swell to popular podcasting platforms such Spotify, Apple Podcasts and others.</p>
          <p>Please note, this feature will require a small monthly fee in the future. You will be able to try it for free for 3 months and then can decide to pay or discontinue.</p>
        </div> */}

        <div>
          <h1>Upgrade your Swellcast</h1>
          <p>Please submit this form if you would like your Swellcast to be upgraded to PRO. </p>
          <p>PRO accounts are automatically published by Swell to popular podcasting platforms such as Spotify, Apple, Amazon, Pandora, and more. PRO accounts also have other benefits such as longer (up to 15 min) record times, and ability to upload pre-recorded Swells up to 1 hour long. PRO accounts are recognizable by a check mark.</p>
          <p>Please note, this feature will require a monthly fee of US $9.99/month (Rs. 499/month in India) in the future. You will be able to try it for free for 1 months and can then decide to pay or downgrade back to the free account.</p>
          <p>
            For any questions, please email us at <a href='mailto:<EMAIL>'><EMAIL></a>.
          </p>
        </div>

        <div className='border rounded-1 p-3'>
          <label className='form-label h4'>Please confirm you meet the following requirement for PRO*</label>
          <div className='d-flex flex-column gap-2'>
            <div className='d-flex gap-2 p-2'>
              <input
                type='checkbox' //
                id='hasMinSwells'
                checked={store.data.hasMinSwells}
                value={store.data.hasMinSwells ? '1' : '0'}
                maxLength={100}
                onChange={onChangeCheckbox}
                tabIndex={1}
                required
                onKeyDown={onKeyDown}
                name='hasMinSwells'
              />
              <label htmlFor='hasMinSwells'>There are at least 2 Swells already posted to my Swellcast</label>
            </div>
            {/* <div className='d-flex gap-2 p-2'>
              <input
                type='checkbox' //
                id='willPostWeekly'
                //   className='form-control h-1'
                checked={store.data.willPostWeekly}
                value={store.data.willPostWeekly ? '1' : '0'}
                maxLength={100}
                onChange={onChangeCheckbox}
                tabIndex={1}
                required
                onKeyDown={onKeyDown}
                name='willPostWeekly'
              />
              <label htmlFor='willPostWeekly'>I plan to post at least 1 Swell every week on my Swellcast</label>
            </div> */}
          </div>
        </div>
        <div>
          <label className='form-label h4'>Any Comments or Notes (optional)</label>
          <textarea
            style={{ resize: 'none', fontSize: 16 }}
            disabled={disabled}
            className='form-control' //
            placeholder='Enter text up to 500 characters...'
            defaultValue={store.data.notes}
            onChange={onChange}
            maxLength={500}
            rows={6}
            tabIndex={1}
            onKeyDown={onKeyDown}
            name='notes'
          />

          <div className='text-end mt-2'>
            <small className='text-mode'>*required</small>
          </div>
        </div>
        <div ref={recap.$container}></div>
      </div>
      {status === 'error' && errorMessage ? <ErrorBlock errorMessage={errorMessage} /> : null}
      <button className='btn btn-lg btn-primary shadow flex-center w-100' type='submit' tabIndex={1}>
        {status === 'loading' ? <CircleLoader size={20} color='#000' /> : 'Submit'}
      </button>
    </form>
  );
};

const HiddenInput = ({ name, value, debug }: { name: string; value: string; debug: boolean }) => {
  const $field = <input readOnly={true} className='form-control' type={debug ? 'text' : 'hidden'} name={name} value={value} />;

  if (debug) {
    return (
      <div className='d-flex gap-2 align-items-center'>
        <label className='form-label' style={{ flex: '0 0 33%' }}>
          {name}
        </label>
        {$field}
      </div>
    );
  }

  return $field;
};
