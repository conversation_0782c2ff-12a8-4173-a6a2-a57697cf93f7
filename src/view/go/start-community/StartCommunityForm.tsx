import React, { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Spacer } from '../../../components/common/Spacer';
import { CircleLoader } from '../../../components/common/circleloader/CircleLoader';
import { DownloadAppButton } from '../../../components/common/downloadapp/DownloadAppButton';
import { useDebug } from '../../../components/debug/useDebug';
import { useAuth } from '../../../components/login/useAuth';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { getReCapchaKey } from '../../../framework/settings/reCaptchaKey';
import { dataLayer } from '../../../tracking/Tracking';
import { slugify } from '../../../utils/Utils';
import { useSearchParams } from '../../../utils/useSearchParams';
import { useUTMParams } from '../../../utils/useUTMParams';
import { AccessWall } from '../premium/AccessWall';
import { postSwellForm } from '../prompt/submitForm';
import { useReCapcha } from '../prompt/useReCapcha';

export const StartCommunityFormPage = () => {
  const { debug } = useDebug('form');
  return (
    <AccessWall premium={false} ignore={debug}>
      <div className='bg-grey-30'>
        <div className='container-sm swell-cloud-colors p-5 mx-auto' style={{ maxWidth: 850 }}>
          <h1 className='display-5 text-center text-white mb-0'>Community Podcast Request&nbsp;Form</h1>
        </div>
      </div>

      <div className='spotify-form-page d-flex flex-column align-items-center mx-auto gap-3' style={{ maxWidth: BreakPoints.L }}>
        <div className='rounded-sm border-grey-trans shadow-sm-md w-100 d-flex flex-column align-items-center p-3'>
          <StartCommunityForm context='website' />
          <Spacer style={{ height: 50 }} />
        </div>

        <Spacer />
      </div>
    </AccessWall>
  );
};

const ErrorBlock = ({ errorMessage }: { errorMessage: string }) => {
  return (
    <p
      className='text-center rounded-2 p-3 fw-bold'
      style={{
        color: '#c14d78',
        background: 'rgba(255,255,255,0.5)',
        border: '4px solid #c14d78',
      }}
      role='alert'
    >
      Error: {errorMessage}
    </p>
  );
};

const StartCommunityForm = ({ context }: { context: string }) => {
  return (
    <SwellForm formId='start-community-form' context={context}>
      <div className='fs-2'>Thank you for your interest in starting a Swell Community Podcast. Please provide the details below.</div>

      <InputText
        name='podcast_name' //
        title='Name of your Community Podcast*'
        maxLength={50}
        required
      />

      <InputRadio
        name='podcast_type' //
        title='What type of a Community Podcast are you interested in?*'
        options={[
          'Wedding Podcast', //
          'Fan Podcast',
          'Alumni Podcast',
          'Podcast with Friends',
          'Class Podcast',
          'Celebration Podcast',
          'Storytelling Podcast',
          'Small Business Podcast',
          'Faith Based Podcast',
          'Institutional Podcast',
          'Podcast for a LinkedIn Group',
          'Podcast for a Facebook Group',
          'Podcast for a WhatsApp Group',
          'Other',
        ]}
        required
      />

      <InputMultiSelect
        name='publish' //
        title='Where would you like the podcast published?*'
        options={[{ value: 'Swellcast.com', static: true }, { value: 'Swell App', static: true }, { value: 'Spotify' }, { value: 'Apple Podcasts' }]}
      />

      <InputRadio
        name='billing' //
        title='Preferred Billing*'
        description='You will be able to change this later, before you actually make a payment. We will send you payment form once your community podcast has been set-up.'
        options={[
          'Monthly US $9.99/month', //
          'Annual US $99/yr',
        ]}
        required
      />

      <InputTextarea
        name='notes' //
        title='Any Comments or Notes (optional)'
      />
    </SwellForm>
  );
};

const SwellForm = ({ formId, children, context }: { formId: string; children: ReactNode; context: string }) => {
  const { debug } = useDebug('form');
  const UTMParams = useUTMParams();
  const $form = useRef<HTMLFormElement>(null);
  const [status, setStatus] = useState<'loading' | 'error' | 'success' | 'none'>('none');
  const [errorMessage, setErrorMessage] = useState('');
  const [formData, setFormData] = useState<FormData>(new FormData());
  const auth = useAuth();
  const searchParams = useSearchParams<{ alias: string; userid: string; thankyou: number; debug: string }>({ alias: '', userid: '', thankyou: 0, debug: '' });
  const alias = useMemo(() => (auth.loggedIn ? auth.user?.alias ?? '' : searchParams.alias), [auth.isReady, auth.loggedIn]);
  const userid = searchParams.userid;
  const [recaptchaError, setRecaptchaError] = useState(false);

  const recap = useReCapcha({
    sitekey: getReCapchaKey(),
    onSuccess: () => {
      if ($form.current) {
        setFormData(new FormData($form.current));
      }
    },
    onError: () => {
      setRecaptchaError(true);
    },
  });

  const exportForm = useCallback(() => {
    const params = new URLSearchParams();
    for (const [key, value] of formData.entries()) {
      params.append(key, value as string);
    }
    return params.toString();
  }, [formData]);

  const onSubmit: React.FormEventHandler<HTMLFormElement> = async (e) => {
    e.preventDefault();
    setStatus('loading');

    if (recaptchaError) {
      setStatus('error');
      setErrorMessage('There was an issue validating ReCaptcha.');
      return;
    }

    if (!formData.get('g-recaptcha-response')) {
      setStatus('error');
      setErrorMessage('Please complete the ReCaptcha test. Thanks.');
      return;
    }

    const body = exportForm();
    const result = await postSwellForm(body);

    if (result.isSuccess) {
      setStatus('success');
      dataLayer({ event: `${formId}-submit`, context, swellcastAlias: alias });
    } else {
      const retry = await postSwellForm(body);
      if (retry.isSuccess) {
        setStatus('success');
        dataLayer({ event: `${formId}-submit`, context, swellcastAlias: alias });
      } else {
        setStatus('error');
        setErrorMessage(retry.message);
        dataLayer({ event: `${formId}-error`, context, swellcastAlias: alias });
      }
    }
    recap.reset();
  };

  const onChangeForm = useCallback(() => {
    if (debug && $form.current) {
      setFormData(new FormData($form.current));
    }
  }, [$form, debug]);

  useEffect(() => {
    if (debug && $form.current) {
      setFormData(new FormData($form.current));
    }
  }, [$form, debug]);

  if (status === 'success' || searchParams.thankyou === 1) {
    return (
      <div className='p-3 py-5 px-lg-5 d-flex flex-column justify-content-center gap-4' style={{ maxWidth: 500 }}>
        <div className='p-5'>
          <div>
            <h2 className='mb-3'>Thank you for submitting your request.</h2>
            <p> Please allow up to 3 business days for us to process your request.</p>
            <DownloadAppButton context={formId} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <form ref={$form} className='d-flex flex-column gap-3 p-2 p-lg-4' onSubmit={onSubmit} onChange={onChangeForm}>
      {debug ? (
        <pre className='p-2' style={{ maxWidth: 400, wordWrap: 'break-word', wordBreak: 'break-word', overflowWrap: 'break-word', position: 'fixed', top: 0, left: 0, zIndex: 9999 }}>
          {exportForm().split('&').join('\n')}
        </pre>
      ) : null}
      <input type='hidden' name='swellformid' value={formId} />
      <input type='hidden' name='alias' value={alias} />
      <input type='hidden' name='userid' value={userid} />
      {Object.entries(UTMParams)
        .filter((e) => e[1])
        .map((e) => (
          <input key={`hidden-${e[0]}`} type='hidden' name={e[0]} value={`${e[1] ?? ''}`} />
        ))}
      <div className='d-flex flex-column gap-5 fs-4'>
        <div className='d-flex flex-column gap-4'>
          {children}
          <div className='text-end'>*required</div>
        </div>

        <div ref={recap.$container}></div>

        {status === 'error' && errorMessage ? <ErrorBlock errorMessage={errorMessage} /> : null}

        <button className='btn btn-lg btn-primary shadow flex-center w-100 p-3' type='submit'>
          {status === 'loading' ? <CircleLoader size={20} color='#000' /> : 'Submit'}
        </button>
      </div>
    </form>
  );
};

const InputTextarea = ({ name, title, description, ...props }: { name: string; title: string; description?: string } & React.InputHTMLAttributes<HTMLTextAreaElement>) => {
  const maxLength = props?.maxLength ?? 500;
  return (
    <div>
      <div className='mb-3'>
        <h2>{title}</h2>
        {description ? <p>{description}</p> : null}
      </div>
      <textarea
        style={{ resize: 'none' }}
        placeholder={`Enter text up to ${maxLength} characters...`}
        maxLength={maxLength}
        rows={Math.ceil(maxLength / 100)}
        {...props}
        className='form-control fs-2' //
        name={name}
      />
    </div>
  );
};

const InputText = ({ name, title, description, ...props }: { name: string; title: string; description?: string } & React.InputHTMLAttributes<HTMLInputElement>) => {
  return (
    <div>
      <div className='mb-3'>
        <h2>{title}</h2>
        {description ? <p>{description}</p> : null}
      </div>
      <input
        {...props}
        className='form-control fs-2' //
        type='text'
        name={name}
        maxLength={50}
        required
      />
    </div>
  );
};

const InputRadio = ({ name, title, description, options, ...props }: { name: string; title: string; description?: string; options: string[] } & React.InputHTMLAttributes<HTMLInputElement>) => {
  return (
    <div>
      <div className='mb-3'>
        <h2>{title}</h2>
        {description ? <p>{description}</p> : null}
      </div>
      <div className='d-flex flex-column gap-2 ps-3'>
        {options.map((p, i) => (
          <div key={`type${i}`} className='form-check'>
            <input
              id={slugify(name, i)}
              {...props}
              className='form-check-input align-middle' //
              type='radio'
              name={name}
              value={p}
              required
            />
            <label className='ps-2 form-check-label' htmlFor={slugify(name, i)}>
              {p}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
};

const InputMultiSelect = ({ name, title, description, options }: { name: string; title: string; description?: string; options: { value: string; static?: boolean }[] }) => {
  const $select = useRef<HTMLSelectElement>(null);
  const [values, setValues] = useState<{ value: string; checked: boolean }[]>(options.map((o) => ({ value: o.value, checked: false })));

  const onChangeSelect = () => {};

  const onChangeMultiCheck: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    e.stopPropagation();
    const value = e.currentTarget.value;
    const checked = e.currentTarget.checked;
    setValues((vals) => vals.map((o) => ({ ...o, checked: value === o.value ? checked : o.checked })));
  };

  useEffect(() => {
    if ($select.current) {
      $select.current.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
    }
  }, [values]);

  return (
    <div>
      <div className='mb-3'>
        <h2>{title}</h2>
        {description ? <p>{description}</p> : null}
      </div>
      <div className='d-flex flex-column gap-2 position-relative ps-3'>
        <select
          ref={$select}
          name={name}
          value={values.filter((v) => v.checked).map((v) => v.value)}
          onChange={onChangeSelect}
          multiple
          required
          style={{
            opacity: 0,
            position: 'absolute',
            zIndex: -1,
          }}
        >
          {options
            .filter((o) => !o.static)
            .map((p, i) => (
              <option id={slugify(`option-${p.value}`)} key={`${name}_${i}`} value={p.value}>
                {p.value}
              </option>
            ))}
        </select>

        {options
          .filter((o) => o.static)
          .map((p, i) => (
            <div key={`type${i}`} className='form-check'>
              <input className='form-check-input align-middle' type='checkbox' readOnly checked disabled />
              <label className='ps-2 form-check-label'>{p.value}</label>
            </div>
          ))}

        {options
          .filter((o) => !o.static)
          .map((p, i) => {
            return (
              <div key={`type${i}`} className='form-check'>
                <input
                  id={slugify(name, i)}
                  className='form-check-input align-middle' //
                  type='checkbox'
                  data-name={name}
                  value={p.value}
                  onChange={onChangeMultiCheck}
                />
                <label className='ps-2 form-check-label' htmlFor={slugify(name, i)}>
                  {p.value}
                </label>
              </div>
            );
          })}
      </div>
    </div>
  );
};
