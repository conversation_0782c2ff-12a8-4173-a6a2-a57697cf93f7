import { useLatestVideos } from '../../../api/gql.getLatestVideos';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { OpenApiLatestVideoResponse } from '../../../generated/graphql';
import { Localize } from '../../../i18n/Localize';
import { useScrollRestore } from '../../../utils/useScrollRestore';
import { VideoBlock } from './VideoBlock';
import { VideoContextProvider } from './VideoContextProvider';
// import './videos.scss';

export const VideosPageContent = () => (
  <VideoContextProvider>
    <VideosView />
  </VideoContextProvider>
);

const VideosView = () => {
  const videos = useLatestVideos();
  useScrollRestore(videos.isSuccess);
  const isEmpty = videos.isSuccess && videos.data.length === 0;

  if (videos.isError) {
    return (
      <div className='mx-auto' style={{ maxWidth: BreakPoints.XL, position: 'relative', height: '100%', minHeight: '80vh' }}>
        <VideosError description={videos.failureReason?.description ?? Localize.GENERAL_ERROR} />
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className='mx-auto flex-center' style={{ maxWidth: BreakPoints.XL, position: 'relative', height: '100%', minHeight: '80vh' }}>
        <VideosError title='This page is still in the oven...' description='Try again later and hopefully we will have something freshly baked to share.' />
      </div>
    );
  }

  if (videos.isSuccess) {
    return <VideosGrid videos={videos.data.slice(0, 48)} />;
  }

  return <BounceLoaderPage />;
};

const VideosError = ({ title = '', description }: { title?: string; description: string }) => {
  return (
    <div className='p-5 flex-center' style={{ height: '100%' }}>
      <div className='text-center d-flex flex-column gap-3'>
        <h3>{title}</h3>
        <p>{description}</p>
      </div>
    </div>
  );
};

// const ScrollingVideos = ({ videos }: { videos: OpenApiLatestVideoResponse[] }) => {
//   if ((videos?.length ?? 0) === 0) return null;

//   return (
//     <ScrollerBlock uid='home-videos-scroller'>
//       <div className='d-flex gap-3 p-3 flex-center fit-content'>
//         {videos?.map((swell, i) => (
//           <VideoBlock key={`home-video-${i}`} {...swell} />
//         ))}
//       </div>
//     </ScrollerBlock>
//   );
// };

const VideosGrid = ({ videos }: { videos: OpenApiLatestVideoResponse[] }) => {
  if ((videos?.length ?? 0) === 0) return null;

  return (
    <div className='videos-grid'>
      {videos?.map((swell, i) => (
        <VideoBlock key={`home-video-${i}`} {...swell} />
      ))}
      <VideoBlockEmpty />
      <VideoBlockEmpty />
      <VideoBlockEmpty />
      <VideoBlockEmpty />
    </div>
  );
};

const VideoBlockEmpty = () => <div className='video-block p-2' />;
