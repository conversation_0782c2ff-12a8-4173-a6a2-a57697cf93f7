import BrunoWaveCardImage from '@images/bruno-wave-card.svg?url';
import { OpenInNew, PlayArrow } from '@mui/icons-material';
import React, { MouseEventHandler, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIsSwellApp, useWebviewAction } from '../../../finder/FinderUtils';
import { iconStyle } from '../../../framework/settings/iconStyle';
import { useOrchestration } from '../../../framework/useOrchestration';
import { OpenApiLatestVideoResponse } from '../../../generated/graphql';
import { dataLayer } from '../../../tracking/Tracking';
import { toBgUrl } from '../../../utils/Utils';
import { getSwellLink } from '../../../utils/swell-utils';
import { VideoContext } from './VideoContext';
import { VideoControls } from './VideoControls';
import { iconShadow } from './iconShadow';

export const VideoBlock = ({ videoUrl, thumbnail, swellCanonicalId, alias, swellId }: OpenApiLatestVideoResponse) => {
  const isSwellApp = useIsSwellApp();
  const orch = useOrchestration();
  const action = useWebviewAction({ id: 'videos' });
  const controller = useContext(VideoContext);
  const [height, setHeight] = useState<string | number>('1080/1920');
  //   const $playButton = useRef<HTMLDivElement>();
  const $video = useRef<HTMLVideoElement>(null);
  const $videoContainer = useRef<HTMLDivElement>(null);
  //   const [showPlayButton, setShowPlayButton] = useState(true);
  const [showVideo, setShowVideo] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  //   const playButtonStyle = { display: showPlayButton ? 'flex' : 'none' };
  const bgImage = toBgUrl(thumbnail ?? BrunoWaveCardImage);
//   const onClickVideo = () => setShowVideo(true);
  const onClickContainer:MouseEventHandler<HTMLDivElement> = useCallback((_e) => {
    if(!showVideo){
        // ? null
    //  } else {
        setShowVideo(true)
        // onClickVideo();
     }
    }, [showVideo]);
  const history = useNavigate();

  const onClickLink = useCallback(
    (e: React.MouseEvent<unknown, MouseEvent>) => {
      dataLayer({
        event: 'click-video-link',
        name: swellCanonicalId,
      });
      e.preventDefault();
      e.stopPropagation();
      if (isSwellApp) {
        if (swellId) {
          action.swell(swellId);
        }
      } else {
        if (swellCanonicalId && alias) {
          const url = getSwellLink({ canonicalId: swellCanonicalId, listId: alias });
          history(url);
        }
      }
    },
    [isSwellApp],
  );

  // Note: shouldn't you use intersection observer? Yes - but it didn't work for some reason. Feel free to try again some day.
  useEffect(() => {
    const handleScroll = () => {
      if ($videoContainer?.current) {
        const rect = $videoContainer.current.getBoundingClientRect();
        const viewHeight = Math.max(document.documentElement.clientHeight, window.innerHeight);
        const visible = !(rect.bottom < 0 || rect.top - viewHeight >= 0);
        setIsVisible(visible);
      }
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [$videoContainer.current]);

  useEffect(() => {
    if (!isVisible) {
      if (!isPlaying) {
        setShowVideo(false);
        // setShowPlayButton(true);
      }
    }
  }, [isVisible]);

  return (
    <div className='video-block'>
      <div className='d-flex flex-column justify-content-center' style={{ backgroundColor: 'black', backgroundImage: bgImage, backgroundSize: 'cover', backgroundPosition: 'center' }}>
        <div className='position-relative' onClick={onClickContainer}>
          <div ref={$videoContainer} style={{ width: '100%', aspectRatio: height }}>
            {showVideo ? (
              <video
                ref={$video}
                onLoadedMetadata={(_e) => {
                  setHeight(`${$video.current?.videoWidth}/${$video.current?.videoHeight}`);
                }}
                onPlay={() => {
                  controller.setVideo($video);
                  setIsPlaying(true);
                  orch.close();
                }}
                onPause={() => {
                  setIsPlaying(false);
                }}
                controls={false}
                controlsList=''
                autoPlay={true}
                className='w-100'
              >
                <source src={`${videoUrl}`} type='video/mp4' />
              </video>
            ) : //
            null}
          </div>

          {showVideo ? (
            <VideoControls target={$video} onClickLink={onClickLink} style={{ width: '100%', justifySelf: 'end' }} />
          ) : (
            <div
              className='absolute-fill'
              style={
                {
                  // background: 'rgba(0,0,0,0.5)'
                }
              }
            >
              <div onClick={() => setShowVideo(true)} className='p-2 fit-content absolute-center'>
                <PlayArrow style={{ color: '#fff', fontSize: 65, filter: iconShadow, WebkitBoxShadow: iconShadow }} />
              </div>

              <div className='p-2 fit-content absolute-se' onClick={onClickLink} title='Open link'>
                <OpenInNew style={{ ...iconStyle, minWidth: 24, minHeight: 24 }} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
