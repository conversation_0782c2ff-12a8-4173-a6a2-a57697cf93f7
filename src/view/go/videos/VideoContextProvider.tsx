import React, { RefObject, useState } from 'react';
import { VideoContext } from './VideoContext';

export const VideoContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [videoElement, setVideoElement] = useState<RefObject<HTMLVideoElement | null> | null>(null);

  function setVideo($v: RefObject<HTMLVideoElement>) {
    if (videoElement?.current && videoElement.current !== $v.current) {
      videoElement.current.pause();
    }
    setVideoElement($v);
  }
  return <VideoContext.Provider value={{ $video: null, setVideo }}>{children}</VideoContext.Provider>;
};
