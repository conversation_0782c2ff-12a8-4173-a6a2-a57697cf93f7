import { Fullscreen, OpenInNew, Pause, PlayArrow } from '@mui/icons-material';
import React, { RefObject, useEffect, useMemo, useState } from 'react';
import { Scrubber } from 'react-scrubber';
import { CircleLoader } from '../../../components/common/circleloader/CircleLoader';
import { AudioEvents } from '../../../components/player/AudioPlayer/IAudioPlayer';
import { ScrubberDisabled } from '../../../components/player/AudioPlayerUI/AudioTrackScrubber';
import { iconStyle } from '../../../framework/settings/iconStyle';
import { formatDuration } from '../../../utils/Utils';

export const VideoControls = ({ target, onClickLink, style = {} }: { style?: React.CSSProperties; target: RefObject<HTMLVideoElement | null>; onClickLink(e: React.MouseEvent<unknown, MouseEvent>): void }) => {
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [shouldPlay, setShouldPlay] = useState(true);
  const progress = useMemo(() => (duration > 0 ? position / duration : 0), [position, duration]);
  const isPlaying = target.current && target.current.currentTime > 0 && !target.current.paused && !target.current.ended;

  useEffect(() => {
    if (target.current) {
      const handleEvent = (e: Event) => {
        switch (e.type) {
          case AudioEvents.DURATIONCHANGE:
            setDuration(target.current?.duration ?? 0);
            break;
          case AudioEvents.TIMEUPDATE:
            setPosition(target.current?.currentTime ?? 0);
            break;
          case AudioEvents.LOADSTART:
            setIsLoading(true);
            break;
          case AudioEvents.LOADEDDATA:
            setIsLoading(false);
            break;
        }
      };

      target.current.addEventListener(AudioEvents.DURATIONCHANGE, handleEvent);
      target.current.addEventListener(AudioEvents.TIMEUPDATE, handleEvent);
      target.current.addEventListener(AudioEvents.LOADSTART, handleEvent);
      target.current.addEventListener(AudioEvents.LOADEDDATA, handleEvent);

      return () => {
        target?.current?.removeEventListener(AudioEvents.DURATIONCHANGE, handleEvent);
        target?.current?.removeEventListener(AudioEvents.TIMEUPDATE, handleEvent);
        target?.current?.removeEventListener(AudioEvents.LOADSTART, handleEvent);
        target?.current?.removeEventListener(AudioEvents.LOADEDDATA, handleEvent);
      };
    }
  }, [target.current]);

  const seekStart = (progress: number) => {
    setShouldPlay(isPlaying === true);
    if (isPlaying) target.current.pause();
    setPosition(progress * duration);
  };

  const seek = (progress: number) => {
    setPosition(progress * duration);
  };

  const seekEnd = (progress: number) => {
    if (target.current) {
      target.current.currentTime = progress * duration;
      if (shouldPlay) target.current.play();
    }
  };

  const centerStyle: React.CSSProperties = { position: 'absolute', left: '50%', top: '50%', transform: 'translate(-50%, -50%)' };

  return (
    <div className='d-flex flex-column' style={{ position: 'absolute', inset: 0 }}>
      <div style={{ flex: '1 1 99%' }} onClick={() => (isPlaying ? target.current.pause() : target.current?.play())}></div>
      <div className='text-white fade-to-black d-flex flex-column' style={style}>
        <div className='d-flex gap-2 justify-content-between'>
          <div className='position-relative px-2'>
            <PlayArrow style={{ ...iconStyle, visibility: 'hidden' }} />
            <div style={{ ...centerStyle, visibility: isLoading ? 'visible' : 'hidden' }}>
              <CircleLoader size={28} color='#fff' />
            </div>
            <Pause style={{ ...iconStyle, ...centerStyle, visibility: isPlaying ? 'visible' : 'hidden' }} onClick={() => target.current?.pause()} />
            <PlayArrow style={{ ...iconStyle, ...centerStyle, visibility: isPlaying ? 'hidden' : 'visible' }} onClick={() => target.current?.play()} />
          </div>
          <div style={{ flex: '1 1 99%' }} />
          <div
            className='px-2'
            title='Watch in full screen mode'
            style={{ width: 'fit-content' }}
            onClick={() => {
              target.current?.requestFullscreen?.();
              target.current?.play();
            }}
          >
            <Fullscreen style={iconStyle} />
          </div>
          <div className='px-2 flex-center' title='Open link' onClick={onClickLink}>
            <OpenInNew style={{ ...iconStyle, minWidth: 24, minHeight: 24 }} />
          </div>
        </div>
        <div className='flex-center gap-2 py-2 ps-3 pe-2'>
          {isLoading ? <ScrubberDisabled /> : <Scrubber min={0} max={1} value={progress} onScrubStart={seekStart} onScrubChange={seek} onScrubEnd={seekEnd} />}
          <div style={{ width: '6ch', textAlign: 'end' }}>{formatDuration(duration - position)}</div>
        </div>
      </div>
    </div>
  );
};
