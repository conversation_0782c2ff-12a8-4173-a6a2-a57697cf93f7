import '../../assets/css/main.scss';
import img from '../../assets/images/PremiumOG.jpg';
import { Mugshot } from '../../components/common/Mugshot';
import { useLocalLogin } from './useLocalLogin';

export const MeApp = () => {
  //   const [info, setInfo] = useState<UserLoggedStatus>(userLoggedOut);
  //   const [isReady, setIsReady] = useState(false);
  const login = useLocalLogin();

  //   useEffect(() => {
  //     const ia = window.localStorage.getItem('local_test_sw_ia') ?? 'false';
  //     const loggedIn = JSON.parse(ia) === true;
  //     const gu = window.localStorage.getItem('local_test_sw_gu') ?? 'null';
  //     const user = JSON.parse(gu);
  //     const ah = window.localStorage.getItem('local_test_sw_ah') ?? 'null';
  //     const token = JSON.parse(ah);
  //     const ph = window.localStorage.getItem('local_test_sw_phub') ?? 'false';
  //     const isPremium = JSON.parse(ph);
  //     setInfo({ loggedIn, info: user, token, isPremium });
  //     setIsReady(true);
  //   }, []);

  //   useEffect(() => {
  //     if (isReady) {
  //       window.localStorage.setItem('local_test_sw_ia', JSON.stringify(info.loggedIn));
  //       window.localStorage.setItem('local_test_sw_ah', JSON.stringify(info.token));
  //       window.localStorage.setItem('local_test_sw_gu', JSON.stringify(info.info));
  //       window.localStorage.setItem('local_test_sw_phub', JSON.stringify(info.isPremium));
  //     }
  //   }, [info]);

  //   const doLogin: React.MouseEventHandler<HTMLButtonElement> = () => {
  //     // setInfo(userLoggedIn);
  //     login.login();
  //   };

  //   const doLogout: React.MouseEventHandler<HTMLButtonElement> = () => {
  //     // setInfo(userLoggedOut);
  //     login.logout();
  //   };

  //   const togglePremium = () => {
  //     login.togglePremium();
  //     // setInfo((i) => ({ ...i, isPremium: !i.isPremium }));
  //   };

  //   const updateToken: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
  //     const token = e?.currentTarget?.value;
  //     setInfo((i) => ({ ...i, token }));
  //   };

  return (
    <div className='d-flex flex-column gap-3 p-3'>
      <div>
        <Mugshot image={img} isLoading={true} progress={1} color={'#ff6600'} alt={'jason'} />
      </div>
      <a href='/'>Swellcast.com</a>
      <div>
        <button type='button' className='p-2' onClick={login.login}>
          Sign in
        </button>
        <button type='button' className='p-2' onClick={login.logout}>
          Sign out
        </button>
      </div>

      <div>
        <label className='d-block'>Token</label>
        <textarea className='w-100' rows={4} value={login.info?.token ?? ''} onChange={(e) => login.updateToken(e)} />
      </div>

      <label>
        <input type='checkbox' checked={login.info.isPremium} onChange={login.togglePremium} /> Premium User
      </label>

      <pre className='p-2' style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>
        {JSON.stringify(login.info, null, 2)}
      </pre>
    </div>
  );
};
