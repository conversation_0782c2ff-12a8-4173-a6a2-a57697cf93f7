export type UserLoggedStatus = {
  loggedIn: boolean;
  info: {
    alias: string;
    image: string | null;
    name: string;
    id: string;
  } | null;
  token: string | null;
  isPremium: boolean;
};

export const userLoggedIn: UserLoggedStatus = {
  loggedIn: true,
  info: {
    alias: 'drivej',
    image: null, //'https://stagecdnimages.swell.life/70027ca3964c1d5c65a8094e04aa8d4ef2633f5d8f11a35e26193f788cdf6b9e_SWELL_LOW.jpg',
    name: '<PERSON> Contento',
    id: 'a56419f0782b44b3ac6087fd453cf3de',
  },
  token: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.tZzjxF-Sjn_EKECPhC0ILvMdiaNIT4shJ0dUlB1Rp1I',
  isPremium: false,
};

export const userLoggedOut: UserLoggedStatus = {
  loggedIn: false,
  info: null,
  token: null,
  isPremium: false,
};

// createRoot(document.getElementById('root')).render(<MeApp />);
