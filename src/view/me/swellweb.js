if (this?.module?.hot) {
  this.module.hot.decline(); // disables HMR for this entry
}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

window.sw_ia = async () => {
  //   console.log('sw_ia()');
  await sleep(1000);
  return JSON.parse(window.localStorage.getItem('local_test_sw_ia') ?? JSON.stringify(false));
};

window.sw_ah = async () => {
  //   console.log('sw_ah()');
  //   const d = new Date();
  //   const token = d.getMinutes();
  //   window.localStorage.setItem('local_test_sw_ah', JSON.stringify('Bearer ' + token));
  return JSON.parse(window.localStorage.getItem('local_test_sw_ah') ?? JSON.stringify(null));
};

window.sw_gu = async () => {
  //   console.log('sw_gu()');
  return JSON.parse(window.localStorage.getItem('local_test_sw_gu') ?? JSON.stringify(null));
};

window.sw_ta = (_info) => {
  //   console.log('sw_ta()', _info);
};

window.sw_phub = async () => {
  return JSON.parse(window.localStorage.getItem('local_test_sw_phub') ?? JSON.stringify(false));
};

window.sw_cw = async (_waveData) => {
  return [1234512345, 2345234543];
};

window.sw_react = async ({ swellId, replyId, pressedState }) => {
  if (Math.random() > 2) console.log('sw_react', { swellId, replyId, pressedState });
  return pressedState === 'PRESSED' ? 'NOTPRESSED' : 'PRESSED';
};

window.sw_notfn = async () => {
  return Math.random() < 0.5;
};
