import { useEffect, useState } from 'react';
import { UserLoggedStatus, userLoggedIn, userLoggedOut } from './me';

export const useLocalLogin = () => {
  const [info, setInfo] = useState<UserLoggedStatus>(userLoggedOut);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const ia = window.localStorage.getItem('local_test_sw_ia') ?? 'false';
    const loggedIn = JSON.parse(ia) === true;
    const gu = window.localStorage.getItem('local_test_sw_gu') ?? 'null';
    const user = JSON.parse(gu);
    const ah = window.localStorage.getItem('local_test_sw_ah') ?? 'null';
    const token = JSON.parse(ah);
    const ph = window.localStorage.getItem('local_test_sw_phub') ?? 'false';
    const isPremium = JSON.parse(ph);
    setInfo({ loggedIn, info: user, token, isPremium });
    setIsReady(true);
  }, []);

  useEffect(() => {
    if (isReady) {
      window.localStorage.setItem('local_test_sw_ia', JSON.stringify(info.loggedIn));
      window.localStorage.setItem('local_test_sw_ah', JSON.stringify(info.token));
      window.localStorage.setItem('local_test_sw_gu', JSON.stringify(info.info));
      window.localStorage.setItem('local_test_sw_phub', JSON.stringify(info.isPremium));
    }
  }, [info]);

  const login = () => {
    setInfo(userLoggedIn);
  };

  const logout = () => {
    setInfo(userLoggedOut);
  };

  const togglePremium = () => {
    setInfo((i) => ({ ...i, isPremium: !i.isPremium }));
  };

  const updateToken: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    const token = e?.currentTarget?.value;
    setInfo((i) => ({ ...i, token }));
  };

  return { info, isReady, login, logout, togglePremium, updateToken };
};
