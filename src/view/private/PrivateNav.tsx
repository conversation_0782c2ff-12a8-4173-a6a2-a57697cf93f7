import classNames from 'classnames';
import { useEffect } from 'react';
import { NavLink } from 'react-router-dom';

export const PrivateNav = () => {
  const className = ({ isActive }: { isActive: boolean }) => classNames('p-3 fix rounded-1', isActive ? 'bg-grey-30' : '');

  useEffect(() => {
    const $root = document.getElementById('root');
    if ($root) {
      $root.style.display = '';
    }
  }, []);

  return (
    <nav className='d-flex gap-1 p-4' style={{ maxWidth: '100vw', overflow: 'auto' }}>
      <NavLink to='/components' className={className}>
        Components
      </NavLink>
      <NavLink to='/marketing' className={className}>
        Marketing
      </NavLink>
      <NavLink to='/sitemap' className={className}>
        Sitemap
      </NavLink>
      <NavLink to='/typography' className={className}>
        Typography
      </NavLink>
      <NavLink to='/iconography' className={className}>
        Iconography
      </NavLink>
      <NavLink to='/buttons' className={className}>
        Buttons
      </NavLink>
      <NavLink to='/colors' className={className}>
        Colors
      </NavLink>
      <NavLink to='/mugshots' className={className}>
        Mugshots
      </NavLink>
      <NavLink to='/popups' className={className}>
        Popups
      </NavLink>
      <NavLink to='/settings' className={className}>
        Settings
      </NavLink>
    </nav>
  );
};
