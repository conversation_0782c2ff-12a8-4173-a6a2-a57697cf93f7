import { isServer } from '@tanstack/react-query';
import { Pre } from '../../components/debug/PrettyPre';
import { useNavSettings } from '../../components/header/useNavSettings';
import { useSettings } from '../../components/settings/useSettings';
import { isLocal, isStage } from '../../framework/settings/settings';
import { useEnv } from '../../framework/useEnv';
import { timeago } from '../../utils/timeago';
import { useLocalStorage } from '../../utils/useLocalStorage';
import { useSettingsData } from '../../utils/useSettingsData';
import './private.scss';

export const SettingsView = () => {
  const env = useEnv();
  const settings = useSettings();
  const navSettings = useNavSettings();
  const loginPopupStore = useLocalStorage<{ popped: boolean; timestamp: number }>('login-popup', { popped: false, timestamp: 0 });
  const lastPop = timeago(new Date(loginPopupStore.data.timestamp));
  const settingsData = useSettingsData();
  //   const isLocal = settings.stage==='local';

  return (
    <div className='p-4'>
      <Pre data={{ loginPopupStore: loginPopupStore.data, lastPop }} />
      <table className='table table-xs'>
        <thead>
          <tr className='bg-grey'>
            <th colSpan={2}>Props</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>isLocal*</td>
            <td>{JSON.stringify({ STAGE: process.env.STAGE, isLocal: process.env.STAGE === 'local' })}</td>
          </tr>
          <tr>
            <td>isLocal</td>
            <td>{JSON.stringify(isLocal)}</td>
          </tr>
          <tr>
            <td>isServer</td>
            <td>{JSON.stringify(isServer)}</td>
          </tr>
          <tr>
            <td>isStage</td>
            <td>{JSON.stringify(isStage)}</td>
          </tr>
        </tbody>
        <thead>
          <tr className='bg-grey'>
            <th colSpan={2}>Settings Data</th>
          </tr>
        </thead>
        <tbody>
          {(Object.keys(settingsData) as Array<keyof typeof settingsData>).map((e, i) => (
            <tr key={`env-tr-${i}`}>
              <td>{e}</td>
              <td>{JSON.stringify(settingsData[e])}</td>
            </tr>
          ))}
        </tbody>
        <thead>
          <tr className='bg-grey'>
            <th colSpan={2}>Settings</th>
          </tr>
        </thead>
        <tbody>
          {(Object.keys(settings) as Array<keyof typeof settings>).map((e, i) => (
            <tr key={`env-tr-${i}`}>
              <td>{e}</td>
              <td>{JSON.stringify(settings[e])}</td>
            </tr>
          ))}
        </tbody>

        <thead>
          <tr className='bg-grey'>
            <th colSpan={2}>ENV</th>
          </tr>
        </thead>
        <tbody>
          {(Object.keys(env) as Array<keyof typeof env>).map((e, i) => (
            <tr key={`env-tr-${i}`}>
              <td>{e}</td>
              <td>{String(env[e])}</td>
            </tr>
          ))}
        </tbody>

        <thead>
          <tr className='bg-grey'>
            <th colSpan={2}>navSettings</th>
          </tr>
        </thead>
        <tbody>
          {(Object.keys(navSettings) as Array<keyof typeof navSettings>).map((e, i) => (
            <tr key={`env-tr-${i}`}>
              <td>{e}</td>
              <td>{JSON.stringify(navSettings[e])}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
