import { NormalSection } from '../../../components/sections/InfiniteSectionsView';
import { EMPTY_SECTION } from '../../../components/sections/sections-models';
import { SectionsView } from '../../../components/sections/SectionsView';
import { HomePageHeaderModel, HomePageRenderType, HomePageSectionDataType, OpenApiHomePageSectionResponse } from '../../../generated/graphql';

const Promo: HomePageHeaderModel = {
  heading: '',
  text: '',
  description: '',
  actions: [
    {
      type: 'webview',
      label: '',
      referrerId: 'Promo-600-12032025',
      params: {
        url: 'https://qrkode.link/boGtBO',
        channels: ['SwellAppChannel'],
      },
      style: {
        backgroundColorHex: '2e4359',
        fontColorHex: 'ffffff',
      },
    },
  ],
  style: {
    headingColorHex: 'FCB950',
    backgroundImageUrl: 'https://stageassets.swell.life/app/images/AmazonPromo.png',
    backgroundColorHex: '80000000',
    fontColorHex: '888888',
  },
};

const Promos: HomePageHeaderModel[] = [
  {
    ...Promo,
    text: 'Hello this is text',
    description: 'And here is the description text that should be short but you never know!',
    actions: [
      { ...Promo.actions![0], label: 'Button One' },
      { ...Promo.actions![0], label: 'Button Two' },
    ],
  },
  Promo,
  { ...Promo, text: 'Hello this is text', description: 'And here is the description text that should be short but you never know!' },
  {
    text: 'Sign in to see more',
    description: 'See more prompts and conversations, or start your own, with a free Swell account.',
    actions: [
      {
        type: 'webview',
        label: 'Sign in',
        route: null,
        style: null,
        params: {
          id: null,
          canonicalId: null,
          searchKey: null,
          tag: null,
          doReply: null,
          url: 'https://www.swellcast.com/me/',
          title: null,
          description: null,
          imageUrl: null,
          categoryId: null,
          channels: ['SwellAppChannel'],
          alias: null,
        },
        referrerId: 'login',
      },
    ],
    style: null,
    heading: '',
  },
];

const sections: NormalSection[] = [
  {
    ...EMPTY_SECTION, //
    id: 'XXX',
    type: HomePageRenderType.Horizontal,
    sectionType: 'PROMOS',
    sectionDataType: HomePageSectionDataType.Promos,
    promos: Promos,
  } as OpenApiHomePageSectionResponse,
];

export const ComponentsPage = () => {
  return (
    <div>
      <SectionsView sections={sections} />
    </div>
  );
};
