import { useState } from 'react';
import { Mugshot } from '../../../components/common/Mugshot';

export const MugshotDemo = () => {
  const [progress, setProgress] = useState(0.66);

  return (
    <div className='p-4'>
      <div className='d-flex gap-2 p-2 flex-wrap'>
        <div>
          <input type='range' min={0} max={1} step={0.001} value={progress} onChange={(e) => setProgress(parseFloat(e.currentTarget.value))} />
        </div>
        <table className='table'>
          <tbody>
            <tr>
              <td>Mugshot CSS</td>
              <td className='p-2'>
                <div className='d-flex gap-3 align-items-center'>
                  <Mugshot progress={progress} pieType='css' />
                </div>
              </td>
            </tr>

            <tr>
              <td>Mugshot SVG</td>
              <td className='p-2'>
                <div className='d-flex gap-3 align-items-center'>
                  <Mugshot progress={progress} pieType='svg' />
                </div>
              </td>
            </tr>

            <tr>
              <td>Mugshot CVS (default)</td>
              <td className='p-2'>
                <div className='d-flex gap-3 align-items-center'>
                  <Mugshot progress={progress} pieType='cvs' />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};
