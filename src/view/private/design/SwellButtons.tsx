import { useMemo, useState } from 'react';
import { ColorModeToggle } from '../../../components/common/colormode/ColorModeToggle';
import { NormalSection } from '../../../components/sections/InfiniteSectionsView';
import { LikeButton } from '../../../components/swell-card/LikeButton';
import { SwellButton } from '../../../components/swellbutton/SwellButton';
import { ReactState } from '../../../generated/graphql';

export const SwellButtonsDemo = () => {
  const [disabled, setDisabled] = useState(false);
  const buttons = useMemo(
    () =>
      (Object.keys(SwellButton) as Array<keyof typeof SwellButton>).map((k) => {
        const El = SwellButton[k] as React.FC<{ children?: React.ReactNode; disabled?: boolean }>;
        switch (k) {
          case 'Base':
          case 'Primary':
          case 'Secondary':
          case 'Blue':
          case 'Unlock':
          case 'Tag':
            return [
              k,
              <El key={k} disabled={disabled}>
                Example Text
              </El>,
            ];
          case 'ReplyFacesXS':
            return [
              k,
              [
                <SwellButton.ReplyFacesXS key='face-0' disabled={disabled} count={0} />,
                <SwellButton.ReplyFacesXS key='face-1' disabled={disabled} count={1} faces={[{ alias: '', url: '' }]} />,
                <SwellButton.ReplyFacesXS
                  key='face-2'
                  disabled={disabled}
                  count={10}
                  faces={[
                    { alias: '', url: '' },
                    { alias: '', url: '' },
                    { alias: '', url: '' },
                    { alias: '', url: '' },
                  ]}
                />,
              ],
            ];
          case 'Like':
            return [
              k,
              [
                <LikeButton key='like-1x' canonicalId='' replyId='' pressState={ReactState.Notpressed} totalReactions={0} />, //
                <LikeButton key='like-1x' canonicalId='' replyId='' pressState={ReactState.Notpressed} totalReactions={1} />,
                <LikeButton key='like-2x' canonicalId='' replyId='' pressState={ReactState.Notpressed} totalReactions={9} />,
                <LikeButton key='like-3x' canonicalId='' replyId='' pressState={ReactState.Pressed} totalReactions={0} />,
                <LikeButton key='like-3x' canonicalId='' replyId='' pressState={ReactState.Pressed} totalReactions={1} />,
                <LikeButton key='like-4x' canonicalId='' replyId='' pressState={ReactState.Pressed} totalReactions={9} />,
              ],
            ];
          case 'LikeXS':
            return [
              k,
              [
                <SwellButton.LikeXS key='like-0' disabled={disabled} />, //
                <SwellButton.LikeXS key='like-1' count={27} disabled={disabled} />,
                <SwellButton.LikeXS key='like-2' disabled={disabled} state={ReactState.Pressed} count={1} />,
                <SwellButton.LikeXS key='like-2' disabled={disabled} state={ReactState.Notpressed} count={1} />,
              ],
            ];
          case 'SeeAll':
            return [
              k,
              [
                <SwellButton.SeeAll key='seeall-0' section={{ sectionParams: { seeAllUrl: 'http://www.swellcast.com' } } as unknown as NormalSection} />, //
                <SwellButton.SeeAll key='seeall-1' section={{ sectionParams: { seeAllUrl: 'http://www.google.com' } } as unknown as NormalSection} />,
              ],
            ];
        }
        return [k, <El key={k} disabled={disabled} />];
      }),
    [disabled],
  );

  return (
    <div className='d-flex gap-2 p-2 flex-wrap'>
      <div className='d-flex gap-2 p-2'>
        <div style={{ width: 200 }}>
          <ColorModeToggle />
        </div>
        <SwellButton.Secondary onClick={() => setDisabled((d) => !d)}>{disabled ? 'enable' : 'disable'}</SwellButton.Secondary>
      </div>
      <table className='table'>
        <tbody>
          {buttons.map((b, i) => (
            <tr key={`btn-${i}`}>
              <td>{b[0]}</td>
              <td className='p-2'>
                <div className='d-flex gap-2'>{b[1]}</div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
