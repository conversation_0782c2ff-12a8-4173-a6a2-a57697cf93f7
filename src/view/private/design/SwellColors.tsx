import classNames from 'classnames';
import { ThemeColors } from '../../../assets/css/ThemeColors';

export const SwellColors = () => {
  // bg-squash-dark, bg-squash-darker, bg-grey-lighter, bg-grey-light, bg-squash-light, bg-green, bg-primary, .bg-grey-darker
  // bg-recording, bg-premium-blue, bg-pink
  return (
    <div className='d-flex flex-column gap-3 p-4'>
      {/* <p>Note: Only color variations actually used in the site are exported. So many of these will be blank.</p> */}

      {(Object.keys(ThemeColors) as Array<keyof typeof ThemeColors>).map((k, i) => (
        <div key={`color${i}`} className={classNames(`bg-${k}`, 'p-3')}>
          {k} {ThemeColors[k]}
        </div>
      ))}
    </div>
  );
};

// {Object.keys(ThemeColors).map((k, i) => {
//     const color = ThemeColors[k];
//     return (
//       <div key={`colorrow${i}`} className='d-flex flex-column gap-2'>
//         <h5>
//           {k} | {color}
//         </h5>
//         <div className='d-flex'>
//           {[10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map((alpha) => {
//             //   const scaled = Math.round((alpha / 100) * 255);
//             //   const c = scaled.toString(16).padStart(2, '0').toUpperCase();
//             return (
//               <div
//                 key={`colors-${k}-${alpha}`}
//                 className={`p-2 flex-center bg-${k}-${alpha}`}
//                 style={{
//                   height: 80,
//                   //   backgroundColor: ThemeColors[k] + c
//                 }}
//               >
//                 <p className='p-2 text-shadow'>{alpha}</p>
//               </div>
//             );
//           })}
//           {/* <div key={`colors-${i}`} className={`p-2 flex-center bg-${k}`} style={{ width: 80, height: 80 }}>
//                       <p className='p-2 text-shadow'></p>
//                     </div> */}
//         </div>
//       </div>
//     );
//   })}
