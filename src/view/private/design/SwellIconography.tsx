import classNames from 'classnames';
import { CSSProperties, useState } from 'react';
import { ThemeColors } from '../../../assets/css/ThemeColors';
import { SwellIcons } from '../../../assets/icons/SwellIcons';

export const SwellIconography = () => {
  const [iconFill, setIconFill] = useState('none');
  const [iconColor, setIconColor] = useState<string>('grey');

  const colors: (keyof typeof ThemeColors)[] = [
    'white', //
    'black',
    'grey',
    'squash',
    'blue',
  ];

  return (
    <div className='p-4'>
      <h2>Spacing</h2>
      <div className='d-flex'>
        <div className='d-flex gap-1'>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
        </div>
        <div className='d-flex gap-2'>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
        </div>
        <div className='d-flex gap-3'>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
        </div>
        <div className='d-flex gap-4'>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
        </div>
        <div className='d-flex gap-5'>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
          <div className='bg-blue' style={{ width: 20, height: 100 }}></div>
        </div>
      </div>
      <br />
      <h2>Rounded</h2>
      <div className='d-flex gap-3'>
        <div className='border p-2 rounded' style={{ width: 100, height: 100 }}>
          .rounded
        </div>
        <div className='border p-2 rounded-1' style={{ width: 100, height: 100 }}>
          .rounded-1
        </div>
        <div className='border p-2 rounded-2' style={{ width: 100, height: 100 }}>
          .rounded-2
        </div>
        <div className='border p-2 rounded-3' style={{ width: 100, height: 100 }}>
          .rounded-3
        </div>
        <div className='border p-2 rounded-4' style={{ width: 100, height: 100 }}>
          .rounded-4
        </div>
        <div className='border p-2 rounded-5' style={{ width: 100, height: 100 }}>
          .rounded-5
        </div>
      </div>
      <br />
      <div className='d-flex gap-2 align-items-center'>
        <div>Icon Color</div>
        <button onClick={() => setIconColor('')} className={classNames('rounded-circle', iconColor === '' ? 'border-grey-dark-80' : 'border-grey-20')} style={{ width: 30, height: 30, borderWidth: 3 }}>
          &times;
        </button>
        {colors.map((c) => (
          <button onClick={() => setIconColor(ThemeColors[c])} className={classNames(`bg-${c} rounded-circle`, iconColor === ThemeColors[c] ? 'border-grey-dark-80' : 'border-grey-20')} style={{ width: 30, height: 30, borderWidth: 3 }}></button>
        ))}
        <div>Icon Fill</div>
        <button onClick={() => setIconFill('')} className={classNames('rounded-circle', iconFill === '' ? 'border-grey-dark-80' : 'border-grey-20')} style={{ width: 30, height: 30, borderWidth: 3 }}>
          &times;
        </button>
        {colors.map((c) => (
          <button onClick={() => setIconFill(ThemeColors[c])} className={classNames(`bg-${c} rounded-circle`, iconFill === ThemeColors[c] ? 'border-grey-dark-80' : 'border-grey-20')} style={{ width: 30, height: 30, borderWidth: 3 }}></button>
        ))}
      </div>
      <br />
      <div className='d-flex gap-1 flex-wrap' style={{ '--icon-fill': iconFill, '--icon-color': iconColor } as CSSProperties}>
        {(Object.keys(SwellIcons) as Array<keyof typeof SwellIcons>).map((k, i) => {
          const Icon = SwellIcons[k];
          return (
            <div key={`icon-${i}`} className='border-grey p-2 d-flex flex-column justify-content-between gap-2' style={{ width: 150, height: 150 }}>
              <p>&nbsp;</p>
              <div className='w-100 flex-center'>
                <Icon style={{ width: 24, height: 24 }} />
              </div>
              <p className='text-center'>{k}</p>
            </div>
          );
        })}
      </div>
    </div>
  );
};
