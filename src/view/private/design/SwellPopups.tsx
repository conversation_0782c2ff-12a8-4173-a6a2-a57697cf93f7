import { useSwell } from '../../../api/gql.loadSwellById';
import { DynamicDownloadPopup } from '../../../components/common/downloadapp/DownloadAppButton';
import { FollowPopupInner } from '../../../components/common/followpopup/FollowPopupInner';
import { LoginPopupInner } from '../../../components/common/loginpopup/LoginPopupInner';
import { NoReplyPopup, QandAPopup, SubscriptionPopup } from '../../../components/player/AudioRecorder/buttons/ReplyButton';
import { AudioErrorMessage } from '../../../components/player/AudioRecorder/popups/AudioErrorMessage';
import { MicAccessMessage } from '../../../components/player/AudioRecorder/popups/MicAccessMessage';
import { ProblemSavingEmptyMessage } from '../../../components/player/AudioRecorder/popups/ProblemSavingEmptyMessage';
import { ProblemSavingMessage } from '../../../components/player/AudioRecorder/popups/ProblemSavingMessage';
import { RecordingPausedMessage } from '../../../components/player/AudioRecorder/popups/RecordingPausedMessage';
import { TimeLimitMessage } from '../../../components/player/AudioRecorder/popups/TimeLimitMessage';
import { UploadMessage } from '../../../components/player/AudioRecorder/popups/UploadMessage';
import { usePopup } from '../../../components/popup/usePopup';
import { UploadStatus } from '../../../models/models';
import { BecomePremiumPopupInner, SignInPopupInner } from '../../go/premium/PremiumWall';
import { TOSMessage } from '../../website/TOS';

export const SwellPopups = () => {
  const popup = usePopup();
  const canonicalId = '387309d9-aa6c-465d-8538-897b5d0bf3a6';
  const swell = useSwell({ canonicalId }); //:'776775f8-7080-4812-930f-4db39116556c' });
  const popups = {
    SignInPopupInner: <SignInPopupInner close={popup.close} />, //
    BecomePremiumPopupInner: <BecomePremiumPopupInner close={popup.close} />,
    DynamicDownloadPopup: <DynamicDownloadPopup close={popup.close} context='' message='custom message' />,
    LoginPopupInner: <LoginPopupInner />,
    QandAPopup: <QandAPopup />,
    SubscriptionPopup: <SubscriptionPopup swell={swell} />,
    NoReplyPopup: <NoReplyPopup />,
    MicAccessMessage: <MicAccessMessage />,
    TimeLimitMessage: <TimeLimitMessage />,
    FollowPopupInner: <FollowPopupInner params={swell.data?.masterRef?.params ?? {}} />,
    ProblemSavingMessage: <ProblemSavingMessage />,
    ProblemSavingEmptyMessage: <ProblemSavingEmptyMessage />,
    AudioErrorMessage: <AudioErrorMessage />,
    RecordingPausedMessage: <RecordingPausedMessage />,
    TOSMessage: <TOSMessage />,
    [`Upload: ${UploadStatus[UploadStatus.COMPLETED]}`]: <UploadMessage status={UploadStatus.COMPLETED} />,
    // [`Upload: ${UploadStatus[UploadStatus.NONE]}`]: <UploadMessage status={UploadStatus.NONE} />,
    [`Upload: ${UploadStatus[UploadStatus.OFFLINE_ERR]}`]: <UploadMessage status={UploadStatus.OFFLINE_ERR} />,
    [`Upload: ${UploadStatus[UploadStatus.SERVER_ERR]}`]: <UploadMessage status={UploadStatus.SERVER_ERR} />,
    [`Upload: ${UploadStatus[UploadStatus.TIMEOUT_ERR]}`]: <UploadMessage status={UploadStatus.TIMEOUT_ERR} />,
    [`Upload: ${UploadStatus[UploadStatus.UPLOADING]}`]: <UploadMessage status={UploadStatus.UPLOADING} />,
    [`Upload: ${UploadStatus[UploadStatus.WEB_ERR]}`]: <UploadMessage status={UploadStatus.WEB_ERR} />,
  };

  return (
    <div className='d-flex gap-3 p-3 flex-wrap'>
      {(Object.keys(popups) as Array<keyof typeof popups>).map((p, i) => (
        <div key={`pop${i}`} style={{ width: 'min-content' }}>
          <div className='d-flex gap-2 justify-content-between align-items-center'>
            <h2>{p}</h2>
            <button className='btn btn-secondary' onClick={() => popup.showPopup(popups[p], true)}>
              Pop
            </button>
          </div>
          {popups[p]}
        </div>
      ))}
    </div>
  );
};
