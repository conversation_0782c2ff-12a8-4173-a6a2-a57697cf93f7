export const SwellTypography = () => {
  const cls = [
    'h1', //
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'display-1',
    'display-2',
    'display-3',
    'display-4',
    'display-5',
    'display-6',
    'fs-1',
    'fs-2',
    'fs-3',
    'fs-4',
    'fs-5',
    'fs-6',
  ];

  return (
    <div className='p-4 d-flex flex-column gap-3'>
      {cls.map((c) => (
        <div className={c}>.{c} Bootstrap Text</div>
      ))}
    </div>
  );
  //   return (
  //     <div className='d-flex flex-column gap-2 p-4'>
  //       <table className='table'>
  //         <tbody>
  //           <tr>
  //             <td>h1.fs-xxl</td>
  //             <td>
  //               <h1 className='fs-xxl'>Explore Swell communities</h1>
  //             </td>
  //           </tr>
  //           <tr>
  //             <td>h1</td>
  //             <td>
  //               <h1>Heading Text</h1>
  //             </td>
  //           </tr>
  //           <tr>
  //             <td>h2</td>
  //             <td>
  //               <h2>Heading Text</h2>
  //             </td>
  //           </tr>
  //           <tr>
  //             <td>h3</td>
  //             <td>
  //               <h3>Heading Text</h3>
  //             </td>
  //           </tr>
  //           <tr>
  //             <td>h4</td>
  //             <td>
  //               <h4>Heading Text</h4>
  //             </td>
  //           </tr>
  //           <tr>
  //             <td>h5</td>
  //             <td>
  //               <h5>Heading Text</h5>
  //             </td>
  //           </tr>
  //           <tr>
  //             <td>h6</td>
  //             <td>
  //               <h6>Heading Text</h6>
  //             </td>
  //           </tr>

  //           <tr>
  //             <td>p</td>
  //             <td>
  //               <p>Default Paragraph Text</p>
  //             </td>
  //           </tr>

  //           <tr>
  //             <td>p.fs-m</td>
  //             <td>
  //               <p className='fs-m'>Paragraph Text</p>
  //             </td>
  //           </tr>

  //           <tr>
  //             <td>p.fs-s</td>
  //             <td>
  //               <p className='fs-s'>Paragraph Text</p>
  //             </td>
  //           </tr>

  //           <tr>
  //             <td>p.fs-xs</td>
  //             <td>
  //               <p className='fs-xs'>Paragraph Text</p>
  //             </td>
  //           </tr>

  //           <tr>
  //             <td>p.fs-xxs</td>
  //             <td>
  //               <p className='fs-xxs'>Paragraph Text</p>
  //             </td>
  //           </tr>

  //           <tr>
  //             <td>small</td>
  //             <td>
  //               <small>Small Text</small>
  //             </td>
  //           </tr>
  //         </tbody>
  //       </table>
  //     </div>
  //   );
};
