import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useSwellcastMarketing } from '../../../api/gql.getSwellcastMarketing';
import { useSettings } from '../../../components/settings/useSettings';
import { SwellButton } from '../../../components/swellbutton/SwellButton';
import { inBrowser } from '../../../utils/Utils';

export const MarketingPage = () => {
  const queryClient = useQueryClient();
  const settings = useSettings();
  const initialNames = inBrowser ? window.localStorage.getItem('users') ?? '' : '';
  const [names, setNames] = useState(initialNames);
  const [namesArray, setNamesArray] = useState(splitNames(initialNames));

  function splitNames(str: string): string[] {
    return str.split(/[\s,]+/g);
  }

  const doSubmit = () => {
    window.localStorage.setItem('users', names);
    const n = names.split(/[\s,]+/g);
    setNamesArray(n);
  };

  useEffect(doSubmit, []);

  useEffect(() => {
    queryClient.invalidateQueries();
  }, [settings.stage]);

  const onClickStage = () => {
    settings.update({ stage: 'stage' });
    // queryClient.invalidateQueries();
  };

  const onClickProd = () => {
    settings.update({ stage: 'prod' });
    // queryClient.invalidateQueries();
  };

  //   const refresh = () => {
  //     queryClient.invalidateQueries();
  //   };

  const onSubmit: React.FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault();
    if (names.trim().length > 0) {
      console.log('submit');
      doSubmit();
    }
  };

  const stageClass = `btn ${settings.stage === 'stage' ? 'bg-squash' : 'bg-grey-30'}`;
  const prodClass = `btn ${settings.stage === 'prod' ? 'bg-squash' : 'bg-grey-30'}`;

  return (
    <div className='p-4'>
      <p>Enter a comma separated list of user names (ex: &quot;arish, drivej, sudha&quot; </p>
      <form className='d-flex gap-2' onSubmit={onSubmit}>
        <input className='form-control' placeholder='Enter usernames: arish, sudha, bowie...' value={names} onChange={(e) => setNames(e.currentTarget.value)} />
        <div>
          <SwellButton.Base onClick={onClickStage} className={stageClass}>
            Stage
          </SwellButton.Base>
        </div>
        <div>
          <SwellButton.Base onClick={onClickProd} className={prodClass}>
            Prod
          </SwellButton.Base>
        </div>
        <div>
          <SwellButton.Base type='submit'>Go</SwellButton.Base>
        </div>
      </form>
      <br />
      <table className='table'>
        <tbody>
          {namesArray?.map((user, i) => (
            <SwellcastRow key={`user${i}`} listId={user} />
          ))}
        </tbody>
      </table>
    </div>
  );
};

const SwellcastRow = ({ listId }: { listId: string }) => {
  const settings = useSettings();
  const server = settings.stage === 'prod' ? 'https://www.swellcast.com' : '';
  const [enabled, setEnabled] = useState(false);
  const swellcast = useSwellcastMarketing({ alias: listId }, { enabled });

  useEffect(() => {
    setEnabled(true);
  }, []);

  //   return <pre>{JSON.stringify(swellcast, null, 2)}</pre>;

  if (swellcast.isSuccess) {
    const image = swellcast.data?.image ?? swellcast.data?.ogImage ?? '';
    const owner = swellcast.data?.owner ?? { alias: '', image: '' };
    const swells = swellcast.data?.swells ?? [];

    return (
      <tr className='p-3'>
        <td>
          <a href={`${server}/${owner.alias?.toLowerCase()}`}>
            <h3>@{owner.alias}</h3>
          </a>
          <p>{swellcast.data?.description ?? '(no description)'}</p>
          {/* <pre>{JSON.stringify(user.data, null, 2)}</pre> */}
        </td>
        <td>
          <ImageInfo label='mugshot' src={owner.image ?? ''} />
        </td>
        <td>
          <ImageInfo label='header' src={image} />
        </td>
        <td>
          {swells.slice(0, 5).map((swell) => {
            return (
              <a key={swell.id} className='d-block p-2' href={`${server}/${swell.author?.alias?.toLowerCase()}/${swell.canonicalId}`}>
                {swell.title}
              </a>
            );
          })}
        </td>
      </tr>
    );
  }

  if (swellcast.isLoading) {
    return (
      <tr>
        <td colSpan={4}>loading... @{listId}</td>
      </tr>
    );
  }

  if (swellcast.isError) {
    return (
      <tr>
        <td colSpan={4}>error loading @{listId}</td>
      </tr>
    );
  }

  return null; //<tr></tr>;
};

const ImageInfo = ({ src, label }: { src: string; label: string }) => {
  const [size, setSize] = useState<{ width: number; height: number }>({ width: 0, height: 0 });
  return (
    <figure className='p-3'>
      <a className='p-2' download={true} href={src}>
        <img onLoad={(e) => setSize({ width: e.currentTarget.naturalWidth, height: e.currentTarget.naturalHeight })} src={src} style={{ maxHeight: 200, maxWidth: 800 }} />
      </a>
      <figcaption className='p-2'>
        {label} {size.width}x{size.height}
      </figcaption>
    </figure>
  );
};
