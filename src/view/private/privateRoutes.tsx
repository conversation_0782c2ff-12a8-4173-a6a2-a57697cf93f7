import { Outlet, RouteObject } from 'react-router-dom';
import { SwellBase } from '../../framework/shared/SwellBase';
import { PrivateNav } from './PrivateNav';
import { SettingsView } from './SettingsView';
import { ComponentsPage } from './design/ComponentsPage';
import { MugshotDemo } from './design/MugshotDemo';
import { SwellButtonsDemo } from './design/SwellButtons';
import { SwellColors } from './design/SwellColors';
import { SwellIconography } from './design/SwellIconography';
import { SwellPopups } from './design/SwellPopups';
import { SwellTypography } from './design/SwellTypography';
import { MarketingPage } from './marketing/Marketing';
import { SitemapRoot } from './sitemap/SitemapRoot';

export const privateRoutes: RouteObject[] = [
  {
    path: '/',
    element: (
      <SwellBase>
        <PrivateNav />
        <Outlet />
      </SwellBase>
    ),
    errorElement: <div>Error</div>,
    children: [
      {
        index: true,
        element: <div></div>,
      },
      {
        path: 'components',
        element: <ComponentsPage />,
      },
      {
        path: 'marketing',
        element: <MarketingPage />,
      },
      {
        path: 'sitemap',
        element: <SitemapRoot />,
      },
      {
        path: 'typography',
        element: <SwellTypography />,
      },
      {
        path: 'iconography',
        element: <SwellIconography />,
      },
      {
        path: 'buttons',
        element: <SwellButtonsDemo />,
      },
      {
        path: 'colors',
        element: <SwellColors />,
      },
      {
        path: 'mugshots',
        element: <MugshotDemo />,
      },
      {
        path: 'popups',
        element: <SwellPopups />,
      },
      {
        path: 'settings',
        element: <SettingsView />,
      },
    ],
  },
  {
    path: '*',
    element: <div></div>,
  },
];
