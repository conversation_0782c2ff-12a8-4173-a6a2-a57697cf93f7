import { buildUrl } from '../../../framework/buildUrl';

export const SitemapRoot = () => {
  const routes = [
    { name: 'Home', path: '', url: '/', parent: 'swellcast' },
    { name: '404', path: '/x/x/x/x/x', url: '/x/x/x/x/x', parent: 'swellcast' },
    { name: 'Record', path: '/rc', url: '/rc', parent: 'swellcast' },
    { name: 'Search', path: '/search/:search', url: '/search/test', parent: 'swellcast' },
    { name: 'Stations', path: '', url: '/stations', parent: 'swellcast' },
    { name: 'Categories', path: '', url: '/categories', parent: 'swellcast' },
    { name: 'Countries', path: '', url: '/countries', parent: 'swellcast' },
    { name: 'Languages', path: '', url: '/languages', parent: 'swellcast' },
    { name: 'Videos', path: '', url: '/go/videos', parent: 'swellcast' },
    { name: '<PERSON>wells Grid (US)', path: '/go/bio', url: '/go/bio', parent: 'swellcast' },
    { name: '<PERSON>wells Grid (IN)', path: '/go/bio/:countryCode', url: '/go/bio/in', parent: 'swellcast' },
    { name: 'Record', path: '', url: '/do/record', parent: 'swellcast' },
    { name: 'Swellcast', path: '/:listId', url: '/arish', parent: 'swellcast' },
    { name: 'Swell', path: '/:listId/:canonicalId/:slug?', url: '/arish/6835abc8-b69c-4ce2-8fc3-8590371d8b4f', parent: 'swellcast' },
    { name: 'Reply', path: '/:listId/:canonicalId/:slug/:replyId', url: '/arish/bda8c962-e7fd-4b60-9891-1662468f466e/questions-deb-phil-resident-program/RTfnfQeatj6L', parent: 'swellcast' },
    { name: 'Hash', path: '/:listId/hashtag/:hash', url: '/arish/hashtag/TestSeries', parent: 'swellcast' },
    { name: 'Hash', path: '/:listId/hashtag/:hash/swell/:canonicalId/:slug', url: '/manoj/hashtag/test/cd74127a-a369-4250-a502-f6dec55728ba/new-swell', parent: 'swellcast' },
    { name: 'Station', path: '/station/:stationId/:listSlug?', url: '/station/TRygrBMBKnvs/news-events', parent: 'swellcast' },
    { name: 'Category', path: '/category/:stationId/:listSlug?', url: '/category/TRygrBMBKnvs/news-events', parent: 'swellcast' },
    { name: 'Station (country)', path: '/country/:country/:listSlug?', url: '/country/us', parent: 'swellcast' },
    { name: 'Station (language)', path: '/language/:language/:listSlug?', url: '/language/en', parent: 'swellcast' },
    { name: 'Featured Swells List', path: '', url: '/go/finder/featured', parent: 'finder_featured' },
    { name: 'Latest Swells List', path: '', url: '/go/finder/latest', parent: 'finder_latest' },
    { name: 'Search Swells List', path: '', url: '/go/finder/search?search=test', parent: 'finder_search' },
    { name: 'Search Swells List (Edit)', path: '', url: '/go/finder/search?search=test&edit=1', parent: 'finder_search' },
    { name: 'Premium Hub', path: '/go/premiumhub', url: '/go/premiumhub?isApp=1', parent: 'premiumhub' },
    { name: 'Prompt Suggestion Form', path: '', url: '/go/promptsuggestion', parent: 'swellcast' },
    { name: 'Podcast Publish Form', path: '', url: '/go/podcastPublish', parent: 'swellcast' },
    { name: 'Community Podcast Request Form', path: '', url: '/go/start-a-community', parent: 'swellcast' },
    { name: 'Widget (Swellcast)', path: '/widget/:listId', url: '/widget/arish', parent: 'widget_swellcast' },
    { name: 'Widget (Station)', path: '/widget/station/:listId', url: '/widget/station/TSZqB1CXR8Da/arts-creativity', parent: 'widget_swellcast' },
    { name: 'Widget (Category)', path: '/widget/category/:listId', url: '/widget/category/TSZqB1CXR8Da/arts-creativity', parent: 'widget_swellcast' },
    { name: 'Widget (Swell)', path: '/widget-swell/:listId/:canonicalId', url: '/widget-swell/skv/e96a8841-a81f-4daa-91b1-3fa5cd88fce3/new-swell-skv-cohost', parent: 'widget_swell' },
    { name: 'Forward', path: '/t/:id', url: '/t/STjixLmT0nMyc7d', parent: 'N/A' },
    { name: 'Marketing Tools', path: '/__private/marketing', url: '/__private/marketing', parent: 'marketing' },
    { name: 'New Player', path: '/media', url: '/media', parent: 'media' },
    { name: 'Down Time Test', path: 'Run Test', url: `/?debug=downtime&maintenanceend=${new Date(Date.now() + 1000 * 60 * 60).toISOString()}`, parent: '' },
  ];

  return (
    <div className='p-4'>
      <table className='table'>
        <thead>
          <tr>
            <th>Name</th>
            <th>Path</th>
            <th>Webview</th>
            <th>Chunk</th>
          </tr>
        </thead>
        <tbody>
          {routes.map((r, i) => (
            <tr key={`row_${i}`}>
              <td>{r.name}</td>
              <td>
                <a className='d-block' target='route_view' href={r.url}>
                  {r.path ? r.path : r.url}
                </a>
              </td>
              <td>
                <a
                  className='d-block'
                  target='route_view'
                  href={buildUrl({ href: 'http://www.this.com' + r.url, searchParams: { isApp: '1' } })
                    .split('.com')
                    .pop()}
                >
                  Webview
                </a>
              </td>
              <td>{r.parent}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
