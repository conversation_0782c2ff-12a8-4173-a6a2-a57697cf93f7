import { useEffect } from 'react';
import { AudioRecorderUI } from '../../components/player/AudioRecorder/AudioRecorderUI';
import { useOrchestration } from '../../framework/useOrchestration';
import { AudioMode } from '../../models/models';

// TODO: in progress
export const RecordWidgetRoot = () => {
  const orch = useOrchestration();

  useEffect(() => {
    orch.open({ mode: AudioMode.RECORD_PROMPT_RESPONSE });
  }, [orch]);

  return (
    <div className='backdrop-blur-recorder p-3'>
      <AudioRecorderUI />
    </div>
  );
};
