import { RouteObject } from 'react-router-dom';
import { VisitSwellLife } from '../../utils/VisitSwellLifeScreen';
import { FinderSearch } from '../go/finder/hash/FinderSearch';
import { SearchWidgetLayout } from './SearchWidgetLayout';

export const searchWidgetRoutes: RouteObject[] = [
  {
    path: '/widget/search',
    element: <SearchWidgetLayout />,
    children: [
      { index: true, element: <FinderSearch forceParams={{ ui: 'dynamic' }} /> },
      { path: '*', element: <VisitSwellLife /> },
    ],
  },
];
