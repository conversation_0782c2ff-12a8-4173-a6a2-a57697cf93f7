import { createContext } from 'react';

export const AudioContextContext = createContext<{
  instance: AudioContext | null;
  state: AudioContextState; //
  getContext(): AudioContext;
  getContextAsync(): Promise<AudioContext>;
  close(): Promise<void>;
  kill(): Promise<void>;
  resume(): Promise<void>;
  suspend(): Promise<void>;
}>({
  instance: null,
  state: 'closed', //
  getContext: () => new AudioContext(),
  close: () => Promise.resolve(),
  kill: () => Promise.resolve(),
  getContextAsync: () => Promise.resolve(new AudioContext()),
  resume: Promise.resolve,
  suspend: Promise.resolve,
});
