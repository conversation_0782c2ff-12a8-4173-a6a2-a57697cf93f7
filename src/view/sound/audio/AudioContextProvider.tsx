import { useRef, useState } from 'react';
import { useDebug } from '../../../components/debug/useDebug';
import { sleep } from '../../../utils/sleep';
import { AudioContextContext } from './AudioContextContext';

// removed: waveConfig.SAMPLE_RATE - it's best to never set the sampleRate
export const AudioContextProvider = ({ children, sampleRate = undefined }: { sampleRate?: number; children: React.ReactNode }) => {
  const { debug, prepareArgs } = useDebug('audiocontext', { color: '#229900' });
  const audioContext = useRef<AudioContext | null>(null);
  const [audioContextState, setAudioContextState] = useState<AudioContextState>('closed');

  function handleAudioContextStateChange() {
    if (debug) console.log(...prepareArgs({ state: audioContext.current?.state }));
    setAudioContextState(audioContext.current?.state ?? 'closed');
  }

  async function getContextAsync(): Promise<AudioContext> {
    if (!audioContext.current) {
      getContext();
    }

    if (audioContext.current) {
      if (audioContext.current.state === 'suspended') {
        try {
          if (debug) console.log(...prepareArgs('getContext(): resuming suspended context'));
          await audioContext.current.resume();
        } catch (err) {
          if (debug) console.warn(...prepareArgs('getContext(): resume failed, refreshing context', err));
          kill();
          audioContext.current = getContext(); // retry with a fresh context
        }
      }

      // verify context is running - Safari takes it's sweet time
      if (audioContext.current.state !== 'running') {
        if (debug) console.log(...prepareArgs('audio context NOT running!!'));

        let attempts = 1;

        while ((audioContext.current.state as AudioContext['state']) !== 'running' && attempts < 3) {
          await sleep(100);
          attempts++;
          if (debug) console.log(...prepareArgs('ATTEMPT'));
        }
      }

      return audioContext.current;
    }
    return getContext();
  }

  function getContext() {
    if (!audioContext.current || audioContext.current.state === 'closed') {
      if (debug) console.log(...prepareArgs('getContext() NEW'));
      audioContext.current = new AudioContext({ sampleRate });
      audioContext.current.addEventListener('statechange', handleAudioContextStateChange);
      setAudioContextState(audioContext.current.state);
    } else {
      if (debug) console.log(...prepareArgs('getContext() RECYCLE'));
    }

    if (audioContext.current.state === 'suspended') {
      if (debug) console.log('SUSPENDED!!!!');
    }
    return audioContext.current;
  }

  async function close() {
    if (audioContext.current && audioContext.current.state !== 'closed') {
      audioContext.current.removeEventListener('statechange', handleAudioContextStateChange);
      await audioContext.current?.close();
    }
  }
  // TODO: poorly named
  async function kill() {
    if (audioContext.current && audioContext.current.state !== 'closed') {
      audioContext.current.removeEventListener('statechange', handleAudioContextStateChange);
      await audioContext.current.close();
    }
    audioContext.current = null;
    setAudioContextState('closed');
  }

  async function suspend() {
    const ac = getContext();
    if (ac.state === 'running') {
      return ac.suspend();
    }
  }

  async function resume() {
    const ac = getContext();
    if (ac.state === 'suspended') {
      return ac.resume();
    }
  }

  return (
    <AudioContextContext.Provider
      value={{
        state: audioContextState,
        instance: audioContext.current,
        getContext,
        getContextAsync,
        close,
        kill,
        resume,
        suspend,
      }}
    >
      {children}
    </AudioContextContext.Provider>
  );
};
