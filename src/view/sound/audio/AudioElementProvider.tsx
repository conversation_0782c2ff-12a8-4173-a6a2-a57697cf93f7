import React, { useRef } from 'react';
import { AudioElementContext } from './AudioElementContext';

export const AudioElementProvider = ({ children }: { children: React.ReactNode }) => {
  const $audio = useRef<HTMLAudioElement>(null!);
  return (
    <AudioElementContext.Provider value={{ $audio }}>
      {children}
      <audio style={{ display: 'none' }} ref={$audio} controls={true} autoPlay={true} />
    </AudioElementContext.Provider>
  );
};
