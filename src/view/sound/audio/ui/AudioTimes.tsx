import React from 'react';

import { formatDuration } from '../../../../utils/Utils';

// export const AudioTimes = ({ style = {}, className = '' }: { style?: React.CSSProperties; className?: string }) => {
//   const $audio = useAudioElement();
//   const { duration } = useMediaDuration($audio);
//   const { position } = useMediaPosition($audio);
//   return <AudioTimesUI className={className} style={style} duration={duration} position={position} />;
// };

export const AudioTimesUI = ({ children = null, style = {}, className = '', duration = 0, position = 0 }: { children?: React.ReactNode; style?: React.CSSProperties; className?: string; duration: number; position: number }) => {
  return (
    <div className={`gap-3 fs-5 align-items-center w-100 d-grid ${className}`} style={{ ...style, gridTemplateColumns: 'minmax(6ch, min-content) auto minmax(6ch, min-content)' }}>
      <div className='text-start'>{formatDuration(position)}</div>
      <div className='text-center'>{children}</div>
      <div className='text-end'>{formatDuration(duration)}</div>
    </div>
  );
};
