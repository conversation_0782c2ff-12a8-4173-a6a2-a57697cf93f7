import React, { useCallback } from 'react';
import { useDebug } from '../../../../components/debug/useDebug';
import { useMediaToggle } from '../../media/hooks/useMediaToggle';
import { useAudioElement } from '../hooks/useAudioElement';

export const AudioToggle = ({ children, active = true, className = '', style = {} }: { children: React.ReactNode; active?: boolean; className?: string; style?: React.CSSProperties }) => {
  const $audio = useAudioElement();
  const { debug } = useDebug('autoplay');
  const toggle = useMediaToggle($audio);
  const onClick = useCallback<React.MouseEventHandler<HTMLDivElement>>(
    (e) => {
      if (active) {
        e.stopPropagation();
        if (debug) console.log('AudioToggle()::togglePlay');
        toggle.togglePlay();
      }
    },
    [active],
  );

  return (
    <div onClick={onClick} className={className} style={style}>
      {children}
    </div>
  );
};
