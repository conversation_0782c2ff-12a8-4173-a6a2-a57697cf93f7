import { RefObject, useState } from 'react';
import { <PERSON>rubbe<PERSON> } from 'react-scrubber';
import { AudioEvents } from '../../../../components/player/AudioPlayer/IAudioPlayer';
import { ScrubberDisabled } from '../../../../components/player/AudioPlayerUI/AudioTrackScrubber';
import { useMediaProgress } from '../../media/hooks/useMediaProgress';

export const MediaScrubber = ({ $media, disabled = false }: { $media: RefObject<HTMLMediaElement>; disabled?: boolean }) => {
  const { progress, setProgress } = useMediaProgress($media);
  const [active, setActive] = useState(false);
  const [shouldPlay, setShouldPlay] = useState(false);
  const [localValue, setLocalValue] = useState(0);
  const value = active ? localValue : progress;

  const seekStart = (progress: number) => {
    setLocalValue(progress);
    setActive(true);
    setShouldPlay(!$media.current?.paused);
    $media.current?.pause();
    setProgress(progress);
  };

  const seek = (progress: number) => {
    setLocalValue(progress);
    setProgress(progress);
  };

  const seekEnd = (progress: number) => {
    setProgress(progress);
    if (shouldPlay) $media.current?.play();
    $media.current?.addEventListener(AudioEvents.SEEKED, () => setActive(false), { once: true });
  };

  if (disabled) {
    return (
      <div className='scrubber-container'>
        <ScrubberDisabled />
      </div>
    );
  }

  return (
    <div className='scrubber-container'>
      <Scrubber min={0} max={1} value={value} onScrubStart={seekStart} onScrubChange={seek} onScrubEnd={seekEnd} />
    </div>
  );
};
