import React, { RefObject } from 'react';
import { MediaScrubber } from './MediaScrubber';
import { MediaTimes } from './MediaTimes';

export const MediaScrubberWithTimes = ({ $media, style = {}, className = 'text-white' }: { $media: RefObject<HTMLMediaElement>; style?: React.CSSProperties; className?: string }) => {
  return (
    <div className={className} style={style}>
      <MediaScrubber $media={$media} />
      <div className='no-select' style={{ marginTop: -15 }}>
        <MediaTimes $media={$media} />
      </div>
    </div>
  );
};
