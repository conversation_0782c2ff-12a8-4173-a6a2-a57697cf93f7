import React, { RefObject } from 'react';

import { useMediaDuration } from '../../media/hooks/useMediaDuration';
import { useMediaPosition } from '../../media/hooks/useMediaPosition';
import { AudioTimesUI } from './AudioTimes';

export const MediaTimes = ({ $media, style = {}, className = '' }: { $media: RefObject<HTMLMediaElement>; style?: React.CSSProperties; className?: string }) => {
  const { duration } = useMediaDuration($media);
  const { position } = useMediaPosition($media);
  const resolvedDuration = duration > 0 ? Math.max(1, duration) : duration; // prevent 0.3s duration showing up as 0
  return <AudioTimesUI className={className} style={style} duration={resolvedDuration} position={position} />;
};
