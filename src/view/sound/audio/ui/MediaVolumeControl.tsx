import { ChangeEvent, RefObject } from 'react';
import { useMediaVolume } from '../../media/hooks/useMediaVolume';

export const MediaVolumeControl = ({ $media, ...props }: { $media: RefObject<HTMLMediaElement> } & React.InputHTMLAttributes<HTMLInputElement>) => {
  const { volume, setVolume: setMediaVolume } = useMediaVolume($media);

  const onChange = (e: ChangeEvent<HTMLInputElement>) => {
    const delta = parseFloat(e.currentTarget.value);
    setMediaVolume(delta);
  };

  return <input type='range' min={0} max={1} step={0.05} value={volume} onChange={onChange} {...props}></input>;
};
