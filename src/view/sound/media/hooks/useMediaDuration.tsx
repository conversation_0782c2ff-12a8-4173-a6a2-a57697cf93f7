import { RefObject, useCallback, useEffect, useState } from 'react';
import { AudioEvents } from '../../../../components/player/AudioPlayer/IAudioPlayer';

export const useMediaDuration = ($media: RefObject<HTMLMediaElement>, active = true) => {
  const [duration, setDuration] = useState(0);

  const handleEvent = useCallback(
    (e: Event) => {
      switch (e.type) {
        case AudioEvents.EMPTIED:
          // funny thing, duration doesn't update when src is set to ""
          setDuration(0);
          break;
        case AudioEvents.DURATIONCHANGE:
          if ($media.current?.duration != Infinity) {
            setDuration($media.current?.duration ?? 0);
          } else {
            // TODO: Is this needed? Can we repro this issue?
            // solely to handle this chrome bug: https://issues.chromium.org/issues/40482588
            $media.current.addEventListener(
              'timeupdate',
              () => {
                if ($media.current) {
                  $media.current.currentTime = 0;
                }
              },
              { once: true },
            );
            // Set it to bigger than the actual duration
            $media.current.currentTime = 1e101;
          }
          break;
      }
    },
    [$media.current],
  );

  useEffect(() => {
    if (active && $media.current) {
      $media.current.addEventListener(AudioEvents.DURATIONCHANGE, handleEvent);
      $media.current.addEventListener(AudioEvents.EMPTIED, handleEvent);
      return () => {
        if ($media.current) {
          $media.current.removeEventListener(AudioEvents.DURATIONCHANGE, handleEvent);
          $media.current.removeEventListener(AudioEvents.EMPTIED, handleEvent);
        }
      };
    }
  }, [active, $media.current]);
  return { duration };
};
