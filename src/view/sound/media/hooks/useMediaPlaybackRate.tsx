import { RefObject, useCallback, useEffect, useState } from 'react';
import { AudioEvents } from '../../../../components/player/AudioPlayer/IAudioPlayer';
import { playbackRatesListLookup } from '../../../../framework/settings/IPlaybackRate';
// TODO: use in audio player
export const useMediaPlaybackRate = ($media: RefObject<HTMLMediaElement>) => {
  const [playbackRate, setPlaybackRate] = useState(1);
  const label = playbackRatesListLookup[playbackRate].label;

  const handleEvent = useCallback(
    (e: Event) => {
      switch (e.type) {
        case AudioEvents.RATECHANGE:
          setPlaybackRate($media.current?.playbackRate ?? 1);
          break;
      }
    },
    [$media],
  );

  const setMediaPlaybackRate = useCallback(
    (val: number) => {
      if ($media.current) {
        $media.current.playbackRate = Math.max(0.5, Math.min(2, val));
      }
    },
    [$media],
  );

  const next = () => {
    setMediaPlaybackRate(playbackRatesListLookup[playbackRate]?.next?.value ?? 1);
  };

  useEffect(() => {
    setMediaPlaybackRate(playbackRate);
  }, [playbackRate, setMediaPlaybackRate]);

  useEffect(() => {
    const ref = $media.current;
    if (ref) {
      ref.addEventListener(AudioEvents.RATECHANGE, handleEvent);
      return () => {
        ref.removeEventListener(AudioEvents.RATECHANGE, handleEvent);
      };
    }
  }, [$media, handleEvent]);

  return { min: 1, max: 2, step: 0.25, value: playbackRate, label, setPlaybackRate, next };
};
