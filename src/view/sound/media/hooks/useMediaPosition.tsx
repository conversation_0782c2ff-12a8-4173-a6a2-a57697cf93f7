import { RefObject, useCallback, useEffect, useState } from 'react';
import { AudioEvents } from '../../../../components/player/AudioPlayer/IAudioPlayer';

export const useMediaPosition = ($media: RefObject<HTMLMediaElement>, active = true) => {
  const [position, setPosition] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleEvent = useCallback(
    (e: Event) => {
      if ($media.current) {
        switch (e.type) {
          case AudioEvents.TIMEUPDATE:
            setPosition($media.current.currentTime);
            break;
          case AudioEvents.PLAYING:
            setIsPlaying(true);
            setPosition($media.current.currentTime);
            break;
          case AudioEvents.PAUSE:
            setIsPlaying(false);
            setPosition($media.current.currentTime);
            break;
        }
      }
    },
    [$media.current],
  );

  // smooth position between currentTime updates
  useEffect(() => {
    if (isPlaying && $media.current) {
      let timer: number;

      const update = () => {
        setPosition($media.current?.currentTime ?? 0);
        timer = requestAnimationFrame(update);
      };

      update();

      return () => {
        cancelAnimationFrame(timer);
      };
    }
  }, [isPlaying]);

  const setMediaPosition = useCallback(
    (val: number) => {
      if ($media.current) {
        $media.current.currentTime = Math.max(0, Math.min($media.current.duration, val));
      }
    },
    [$media.current],
  );

  useEffect(() => {
    if (active && $media.current) {
      $media.current.addEventListener(AudioEvents.TIMEUPDATE, handleEvent);
      $media.current.addEventListener(AudioEvents.PLAYING, handleEvent);
      $media.current.addEventListener(AudioEvents.PAUSE, handleEvent);
      return () => {
        if ($media.current) {
          $media.current.removeEventListener(AudioEvents.TIMEUPDATE, handleEvent);
          $media.current.removeEventListener(AudioEvents.PLAYING, handleEvent);
          $media.current.removeEventListener(AudioEvents.PAUSE, handleEvent);
        }
      };
    }
  }, [$media.current, active]);

  return { position, setPosition: setMediaPosition };
};
