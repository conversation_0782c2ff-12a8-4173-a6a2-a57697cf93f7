import { RefObject, useCallback } from 'react';
import { useMediaDuration } from './useMediaDuration';
import { useMediaPosition } from './useMediaPosition';

export const useMediaProgress = ($media: RefObject<HTMLMediaElement>, active = true) => {
  const { position } = useMediaPosition($media, active);
  const { duration } = useMediaDuration($media, active);
  const progress = active ? (duration > 0 ? position / duration : 0) : 0;

  const setMediaProgress = useCallback(
    (val: number) => {
      setMediaPosition(Math.max(0, Math.min(1, val)) * duration);
    },
    [duration],
  );

  const setMediaPosition = useCallback(
    (val: number) => {
      if ($media.current) {
        $media.current.currentTime = Math.max(0, Math.min(duration, val));
      }
    },
    [duration],
  );

  return { progress, position, duration, setProgress: setMediaProgress, setPosition: setMediaPosition };
};
