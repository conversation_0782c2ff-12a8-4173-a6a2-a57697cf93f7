import { RefObject, useCallback, useEffect, useMemo, useState } from 'react';
import { AudioEvents, AudioReadyState } from '../../../../components/player/AudioPlayer/IAudioPlayer';
import { PlayerStatus } from '../../../../models/models';

// const useMediaSource = ($media: React.MutableRefObject<HTMLMediaElement>, active = true) => {
//   const [src, setSrc] = useState($media?.current?.currentSrc ?? '');
//   useEffect(() => {
//     if ($media.current.currentSrc !== src) {
//       $media.current.src = src;
//     }
//   }, [src]);
//   const handleEvent = useCallback(
//     (e: Event) => {
//       switch (e.type) {
//         case AudioEvents.LOADEDMETADATA:
//           setSrc($media?.current?.currentSrc ?? '');
//           break;
//       }
//     },
//     [$media.current],
//   );
//   useEffect(() => {
//     if (active && $media.current) {
//       $media.current.addEventListener(AudioEvents.LOADEDMETADATA, handleEvent);
//       return () => {
//         if ($media.current) {
//           $media.current.removeEventListener(AudioEvents.LOADEDMETADATA, handleEvent);
//         }
//       };
//     }
//   }, [active, $media.current]);
//   return { src, setSrc };
// };

export const useMediaStatus = ($media: RefObject<HTMLMediaElement>, active = true) => {
  const [isPlaying, setIsPlaying] = useState($media.current && $media.current.currentTime > 0 && !$media.current.paused && !$media.current.ended);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isComplete, setIsComplete] = useState(!!$media.current?.ended);
  const [isBlocked, setIsBlocked] = useState(false);
  const [isEmpty, setIsEmpty] = useState(true);
  const [canPlay, setCanPlay] = useState(false);
  const [readyState, setReadyState] = useState<AudioReadyState>($media.current?.readyState ?? AudioReadyState.HAVE_NOTHING);
  const status = useMemo(() => {
    if (active) {
      switch (true) {
        case isPlaying:
          return PlayerStatus.PLAYING;
        case isLoading:
          return PlayerStatus.LOADING;
        case isError:
          return PlayerStatus.ERROR;
        case isComplete:
          return PlayerStatus.COMPLETED;
        case isBlocked:
          return PlayerStatus.BLOCKED;
        case isEmpty:
          return PlayerStatus.NONE;
        default:
          return PlayerStatus.PAUSED;
      }
    }
    return PlayerStatus.NONE;
  }, [active, isPlaying, isLoading, isError, isComplete, isBlocked, isEmpty]);

  const handleEvent = useCallback(
    (e: Event) => {
      setReadyState($media.current?.readyState ?? 0);
      switch (e.type) {
        case AudioEvents.EMPTIED:
          setIsEmpty(true);
          setIsPlaying(false);
          setIsComplete(false);
          setCanPlay(false);
          setIsLoading(true);
          setIsError(false);
          break;

        case AudioEvents.LOADSTART:
          //   setBuffer(0);
          //   setPosition(0);
          //   setDuration(0);
          setIsEmpty(false);
          setIsPlaying(false);
          setIsComplete(false);
          setCanPlay(false);
          setIsLoading(true);
          setIsError(false);
          break;

        case AudioEvents.CANPLAY:
          setIsEmpty(false);
          setCanPlay(true);
          if ($media?.current && $media.current.paused && $media.current.autoplay && $media.current.currentTime === 0) {
            setIsBlocked(true);
          }
          break;

        case AudioEvents.CANPLAYTHROUGH:
          setIsEmpty(false);
          setIsLoading(false);
          break;

        case AudioEvents.PLAY:
          setIsEmpty(false);
          setIsPlaying(true);
          setIsError(false);
          setIsComplete(false);
          break;

        case AudioEvents.PLAYING:
          setIsEmpty(false);
          setIsPlaying(true);
          setIsError(false);
          setIsBlocked(false);
          break;

        case AudioEvents.PAUSE:
          setIsPlaying(false);
          if ($media?.current && $media.current.currentTime === 0) {
            // this is the safari browser blocking audio play - no way a human can pause at 0
            setIsBlocked(true);
          }
          break;

        case AudioEvents.ENDED:
          setIsComplete(true);
          setIsPlaying(false);
          break;

        case AudioEvents.ERROR:
          if ($media.current?.currentSrc !== '') {
            setIsError(true);
          }
          setIsPlaying(false);
          setIsLoading(false);
          setCanPlay(false);
          break;

        case AudioEvents.STALLED:
        case AudioEvents.ABORT:
          setIsEmpty(true);
          setIsPlaying(false);
          setIsLoading(false);
          setCanPlay(false);
          break;

        case AudioEvents.SEEKING:
          setIsBlocked(false);
          break;
      }
    },
    [$media.current],
  );

  useEffect(() => {
    if (active && $media.current) {
      $media.current.addEventListener(AudioEvents.EMPTIED, handleEvent);
      $media.current.addEventListener(AudioEvents.LOADSTART, handleEvent);
      $media.current.addEventListener(AudioEvents.CANPLAY, handleEvent);
      $media.current.addEventListener(AudioEvents.CANPLAYTHROUGH, handleEvent);
      $media.current.addEventListener(AudioEvents.PLAY, handleEvent);
      $media.current.addEventListener(AudioEvents.PLAYING, handleEvent);
      $media.current.addEventListener(AudioEvents.PAUSE, handleEvent);
      $media.current.addEventListener(AudioEvents.ENDED, handleEvent);
      $media.current.addEventListener(AudioEvents.ERROR, handleEvent);
      $media.current.addEventListener(AudioEvents.STALLED, handleEvent);
      $media.current.addEventListener(AudioEvents.ABORT, handleEvent);
      $media.current.addEventListener(AudioEvents.SEEKING, handleEvent);
      return () => {
        if ($media.current) {
          $media.current.removeEventListener(AudioEvents.EMPTIED, handleEvent);
          $media.current.removeEventListener(AudioEvents.LOADSTART, handleEvent);
          $media.current.removeEventListener(AudioEvents.CANPLAY, handleEvent);
          $media.current.removeEventListener(AudioEvents.CANPLAYTHROUGH, handleEvent);
          $media.current.removeEventListener(AudioEvents.PLAY, handleEvent);
          $media.current.removeEventListener(AudioEvents.PLAYING, handleEvent);
          $media.current.removeEventListener(AudioEvents.PAUSE, handleEvent);
          $media.current.removeEventListener(AudioEvents.ENDED, handleEvent);
          $media.current.removeEventListener(AudioEvents.ERROR, handleEvent);
          $media.current.removeEventListener(AudioEvents.STALLED, handleEvent);
          $media.current.removeEventListener(AudioEvents.ABORT, handleEvent);
          $media.current.removeEventListener(AudioEvents.SEEKING, handleEvent);
        }
      };
    }
  }, [active, $media.current]);

  return { isPlaying, isLoading, isError, isComplete, isBlocked, isEmpty, canPlay, readyState, status };
};
