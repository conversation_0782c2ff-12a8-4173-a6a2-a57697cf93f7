import { RefObject, useCallback } from 'react';
import { useMediaStatus } from './useMediaStatus';

export const useMediaToggle = ($media: RefObject<HTMLMediaElement>, active = true) => {
  const status = useMediaStatus($media, active);

  const togglePlay = () => {
    if ($media.current) {
      if ($media.current.paused) {
        $media.current.play().catch((err) => {
          // failed play toggle
          console.log('useMediaToggle.togglePlay()', err);
        });
      } else {
        $media.current.pause();
      }
    }
  };

  const play = () => {
    if ($media.current?.paused) $media.current?.play();
  };

  const pause = useCallback(() => {
    if (!$media.current?.paused) $media.current?.pause();
  }, [$media.current]);

  return { ...status, togglePlay, play, pause };
};
