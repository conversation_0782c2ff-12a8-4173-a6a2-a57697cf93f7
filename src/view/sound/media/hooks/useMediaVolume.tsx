import { RefObject, useCallback, useEffect, useState } from 'react';
import { AudioEvents } from '../../../../components/player/AudioPlayer/IAudioPlayer';

export const useMediaVolume = ($media: RefObject<HTMLMediaElement>) => {
  const [volume, setVolume] = useState(1);

  const handleChangeVolume = useCallback(() => {
    setVolume($media.current?.volume ?? 1);
  }, [$media]);

  const setMediaVolume = useCallback(
    (val: number) => {
      if ($media.current) {
        $media.current.volume = Math.max(0, Math.min(1, val));
      }
    },
    [$media],
  );

  // Initialize volume on mount
  useEffect(() => {
    if ($media.current) {
      setVolume($media.current.volume);
    }
  }, [$media]);

  useEffect(() => {
    const ref = $media.current;
    if (!ref) return;
    ref.addEventListener(AudioEvents.VOLUMECHANGE, handleChangeVolume);
    return () => ref.removeEventListener(AudioEvents.VOLUMECHANGE, handleChangeVolume);
  }, [$media, handleChangeVolume]);

  return { volume, setVolume: setMediaVolume };
};
