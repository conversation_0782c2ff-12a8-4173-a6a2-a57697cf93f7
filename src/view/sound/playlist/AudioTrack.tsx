import { IMediaCut } from './hooks/useMediaPlaylist';

export class AudioTrack {
  src: string = '';
  cut: IMediaCut = { in: 0, out: 9999 };
  $audio: HTMLAudioElement = new Audio();
  file: File | null = null;
  duration: number = 0;
  position: number = 0;
  progress: number = 0;
  rawDuration: number = 0;
  index = 0;

  constructor(config: Partial<AudioTrack>) {
    Object.assign(this, config);
    this.$audio.autoplay = false;
  }

  load() {
    if (this.file) {
      URL.revokeObjectURL(this.$audio.src);
      this.$audio.src = URL.createObjectURL(this.file);
      this.$audio.autoplay = false;
      this.$audio.currentTime = 0;
    } else {
      this.$audio.src = this.src;
    }
  }

  update() {
    this.rawDuration = this.$audio.duration;
    this.duration = Math.min(this.$audio.duration, this.cut.out - this.cut.in);
  }
}
