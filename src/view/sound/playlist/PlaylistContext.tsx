import React from 'react';
import { IAudioTrack } from './PlaylistProvider';

export const PlaylistContext = React.createContext<{
  id: string;
  trackIndex: number; //
  trackId: string;
  tracks: IAudioTrack[];
  currentTrack: IAudioTrack | null;
  next(): void;
  prev(): void;
  getTrack(query: number | string): IAudioTrack | null;
  hasNext: boolean;
  hasPrev: boolean;
  isComplete: boolean;
  setTrackIndex: React.Dispatch<React.SetStateAction<number>>;
  setCanonicalId: React.Dispatch<React.SetStateAction<string>>;
  playTrack(trackId: string | null, playlistId?: string): void;
}>({
  tracks: [],
  id: '',
  trackIndex: 0,
  trackId: '',
  currentTrack: null,
  hasNext: false,
  hasPrev: false,
  isComplete: false,
  next: () => {},
  prev: () => {},
  getTrack: (_query: string | number): IAudioTrack | null => null,
  setTrackIndex: (_value: React.SetStateAction<number>) => {},
  setCanonicalId: (_value: React.SetStateAction<string>) => {},
  playTrack: (_trackId: string | null, _playlistId?: string) => {},
});
