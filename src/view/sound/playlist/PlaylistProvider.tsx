import React, { useEffect, useMemo, useState } from 'react';
import { OpenApiReplyResponseEnhanced, OpenApiSwellResponseEnhanced, useSwell } from '../../../api/gql.loadSwellById';
import { OpenApiAuthorModel } from '../../../generated/graphql';
import { RouteParams } from '../../../models/models';
import { useAudioElement } from '../audio/hooks/useAudioElement';
import { useMediaStatus } from '../media/hooks/useMediaStatus';
import { PlaylistContext } from './PlaylistContext';

interface IAudioTrackData {
  author: OpenApiAuthorModel;
  createdOn: string;
  params: RouteParams;
  id: string;
}

export interface IAudioTrack {
  id: string;
  src: string;
  index: number;
  duration: number;
  data: IAudioTrackData;
}

function replyToTrack(t: OpenApiSwellResponseEnhanced | OpenApiReplyResponseEnhanced, i: number): IAudioTrack {
  return {
    id: t.id ?? '', //
    src: t?.audio?.url ?? '',
    index: i,
    duration: t?.audio?.duration ?? 0,
    data: {
      id: t.id ?? '', //
      author: t?.author ?? {},
      createdOn: t.createdOn ?? '',
      params: t.masterRef.params,
    },
  };
}

function swellToTracks(swell: OpenApiSwellResponseEnhanced): IAudioTrack[] {
  console.log('swellToTracks');
  try {
    // const tracks = [swell, ...swell.replies].filter((t) => t?.placeholder !== true).map(replyToTrack);
    // console.log({ tracks });
    // return tracks;
    return swell && swell?.replies ? [swell, ...swell.replies].map(replyToTrack) : [];
    // return swell?.replies?.length ? [swell, ...swell.replies].map(replyToTrack) : [];
  } catch (err) {
    console.log(err, { swell });
  }
  return [];
}

export const PlaylistProvider = ({ children }: { children: React.ReactNode }) => {
  const $audio = useAudioElement();
  const [canonicalId, setCanonicalId] = useState('');
  const [playTrackId, setPlayTrackId] = useState('');
  const swell = useSwell({ canonicalId });
  //   const swell = useSwellContext();
  const id = swell.isSuccess ? swell.data?.canonicalId ?? '' : '';
  const tracks = useMemo(() => (swell.isSuccess && !swell.isPlaceholderData ? swellToTracks(swell.data) : []), [swell.isSuccess, swell.isPlaceholderData]);
  //   const [tracks, setTracks] = useState<IAudioTrack[]>([]);
  const [trackIndex, setTrackIndex] = useState(-1);
  const track = tracks?.[trackIndex] ?? { id: null };
  const status = useMediaStatus($audio);
  const hasPrev = tracks.length > 0 && trackIndex > 0;
  const hasNext = tracks.length > 0 && trackIndex < tracks.length - 1;
  const isComplete = status.isComplete && trackIndex === tracks.length - 1;
  const trackId = tracks?.[trackIndex]?.id ?? null;
  const currentTrack = useMemo(() => tracks?.[trackIndex], [tracks, trackIndex]);

  useEffect(() => {
    if (swell.isSuccess) {
      //   setTracks(swellToTracks(swell.data));
      if (playTrackId) {
        playTrack(playTrackId);
      } else {
        if (trackIndex === 0) {
          setTrackIndex(-1);
        } else {
          setTrackIndex(0);
        }
      }
    }
  }, [swell.isSuccess]);

  useEffect(() => {
    if (status.isComplete) {
      if (hasNext) {
        setTrackIndex((i) => i + 1);
      }
    }
  }, [status.isComplete]);

  useEffect(() => {
    // console.log('trackIndex', trackIndex);
    if (trackIndex > -1 && trackIndex < tracks.length) {
      const track = tracks[trackIndex];
      if ($audio.current) {
        $audio.current.src = track.src;
        console.log('blocked play');
        $audio.current.play().catch(() => {
          console.log('blocked');
        });
      }
    }
  }, [trackIndex]);

  const next = () => {
    console.log('next');
    if (hasNext) {
      setTrackIndex((i) => i + 1);
    }
  };

  const prev = () => {
    if (hasPrev) {
      setTrackIndex((i) => i - 1);
    }
  };

  const playTrack = (trackId: string, playlistId?: string) => {
    if (playlistId && canonicalId !== playlistId) {
      setPlayTrackId(trackId);
      setCanonicalId(playlistId);
    } else {
      if (track.id !== trackId) {
        const found = tracks.find((t) => t.id === trackId);
        if (found) {
          setTrackIndex(found.index);
        }
      }
    }
  };

  const getTrack = (query: string | number) => {
    // match on index
    if (typeof query === 'number') {
      return tracks?.[query] ?? null;
    }
    // sugar
    if (query === 'next') {
      return tracks?.[trackIndex + 1] ?? null;
    }
    // sugar
    if (query === 'prev') {
      return tracks?.[trackIndex - 1] ?? null;
    }
    // match on id
    if (typeof query === 'string') {
      if (query !== trackId) {
        const found = tracks.find((t) => t.id === query);
        return found ?? null;
      }
    }
    return null;
  };

  return (
    <PlaylistContext.Provider
      value={{
        playTrack, //
        setCanonicalId,
        id,
        tracks,
        trackId,
        trackIndex,
        currentTrack,
        next,
        prev,
        setTrackIndex,
        getTrack,
        hasNext,
        hasPrev,
        isComplete,
      }}
    >
      {children}
    </PlaylistContext.Provider>
  );
};
