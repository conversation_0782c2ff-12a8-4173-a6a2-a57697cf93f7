import { useRef, useState } from 'react';
import { AudioEvents } from '../../../../components/player/AudioPlayer/IAudioPlayer';
import { AudioTrack } from '../AudioTrack';

export interface IMediaCut {
  in: number;
  out: number;
}

export const useMediaPlaylist = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);
  const progress = duration > 0 ? position / duration : 0;
  const track = useRef<AudioTrack | null>(null);
  const tracks = useRef<AudioTrack[]>([]);
  const [publicTracks, setPublicTracks] = useState<AudioTrack[]>([]);

  const addTrack = (thisTrack: AudioTrack, i = -1) => {
    thisTrack.$audio.addEventListener(AudioEvents.CANPLAYTHROUGH, handleEvent);
    thisTrack.$audio.addEventListener(AudioEvents.PLAYING, handleEvent);
    thisTrack.$audio.addEventListener(AudioEvents.TIMEUPDATE, handleEvent);
    thisTrack.$audio.addEventListener(AudioEvents.PAUSE, handleEvent);
    thisTrack.$audio.addEventListener(AudioEvents.ENDED, handleEvent);
    thisTrack.$audio.addEventListener(AudioEvents.DURATIONCHANGE, handleEvent);
    if (i === -1) {
      tracks.current.push(thisTrack);
    } else {
      tracks.current = [...tracks.current.slice(0, i), thisTrack, ...tracks.current.slice(i)];
    }
    updateTracks();
    // track.current = thisTrack;
    thisTrack.load();
  };

  const removeTrack = (thisTrack: AudioTrack) => {
    if (thisTrack.index === track.current?.index) {
      track.current.$audio.pause();
      track.current = null;
    }
    thisTrack.$audio.removeEventListener(AudioEvents.CANPLAYTHROUGH, handleEvent);
    thisTrack.$audio.removeEventListener(AudioEvents.PLAYING, handleEvent);
    thisTrack.$audio.removeEventListener(AudioEvents.TIMEUPDATE, handleEvent);
    thisTrack.$audio.removeEventListener(AudioEvents.PAUSE, handleEvent);
    thisTrack.$audio.removeEventListener(AudioEvents.ENDED, handleEvent);
    thisTrack.$audio.removeEventListener(AudioEvents.DURATIONCHANGE, handleEvent);
    tracks.current.splice(thisTrack.index, 1);
    updateTracks();
  };

  const updateTracks = () => {
    let position = 0;
    let playlistDuration = 0;
    tracks.current.forEach((t, i) => {
      t.index = i;
      t.position = position;
      t.$audio.dataset.trackIndex = `${i}`;
      position += t.duration;
      playlistDuration += t.duration;
    });
    tracks.current.forEach((t) => {
      t.progress = t.position / playlistDuration;
    });
    setDuration(playlistDuration);
    setPublicTracks([...tracks.current]);
  };

  const updatePosition = () => {
    let p = 0;
    if (track.current) {
      p = track.current.position + (track.current.$audio.currentTime - track.current.cut.in);
    }
    // const p = (track.current.position + (track.current.$audio.currentTime - track.current.cut.in)) / duration;
    // let i = 0;
    // const trackIndex = getTrackIndex(track.current.id);
    // let p = 0;
    // while (i < trackIndex) {
    //   p += tracks.current[i].duration;
    //   i++;
    // }
    // p += tracks.current[i].$audio.currentTime;
    setPosition(p);
  };

  //   const getTrack = (i: number) => {
  //     return tracks[i];
  //   };

  const handleEvent = (e: Event) => {
    // const trackId = (e.currentTarget as HTMLAudioElement).dataset?.trackId;
    const trackIndex = parseInt((e.currentTarget as HTMLAudioElement).dataset?.trackIndex ?? '0'); // getTrackIndex(trackId);
    const thisTrack = tracks.current[trackIndex];

    if (e.type !== AudioEvents.TIMEUPDATE) {
      console.log({ type: e.type, trackIndex, thisTrack });
    }
    switch (e.type) {
      case AudioEvents.DURATIONCHANGE:
        thisTrack.update();
        updateTracks();
        break;

      case AudioEvents.PLAYING:
        setIsPlaying(true);
        setIsComplete(false);
        break;

      case AudioEvents.PLAY:
        setIsComplete(false);
        // thisTrack.$audio.currentTime = thisTrack.cut.in;
        // case AudioEvents.ABORT:
        // case AudioEvents.EMPTIED:
        // case AudioEvents.ERROR:
        // setIsPlaying(false);
        break;

      case AudioEvents.PAUSE:
        // case AudioEvents.ABORT:
        // case AudioEvents.EMPTIED:
        // case AudioEvents.ERROR:
        updatePosition();
        setIsPlaying(false);
        break;

      case AudioEvents.CANPLAYTHROUGH:
        break;

      case AudioEvents.TIMEUPDATE:
        updatePosition();
        if (thisTrack && thisTrack.$audio.currentTime >= thisTrack.cut.out) {
          thisTrack.$audio.pause();
          thisTrack.$audio.dispatchEvent(new Event('ended'));
        }
        // case AudioEvents.ABORT:
        // case AudioEvents.EMPTIED:
        // case AudioEvents.ERROR:
        // setIsPlaying(false);
        break;

      case AudioEvents.ENDED:
        {
          //   const trackId = (e.currentTarget as HTMLAudioElement).dataset?.trackId;
          //   const trackIndex = getTrackIndex(trackId);
          if (trackIndex === tracks.current.length - 1) {
            setIsComplete(true);
            setPosition(duration);
          } else {
            playTrack(trackIndex + 1);
          }
        }
        // case AudioEvents.ABORT:
        // case AudioEvents.EMPTIED:
        // case AudioEvents.ERROR:
        //   setIsPlaying(false);
        break;
    }
  };

  //   useEffect(() => {
  //     if ($audio.current) {
  //       $audio.current.addEventListener(AudioEvents.PLAYING, handleEvent);
  //     }
  //   }, [$audio.current]);

  const getProgressInfo = (progress: number = 0) => {
    if (progress >= 0 && progress < 1) {
      const playlistPosition = duration * progress;
      const thisTrack = tracks.current.find((t) => t.position + t.duration > playlistPosition) ?? { progress: 0, duration: 0, index: -1, cut: { in: 0, out: 0 } };
      if (thisTrack) {
        const pDif = progress - thisTrack.progress;
        const trackPosition = thisTrack.cut.in + Math.min(pDif * duration, thisTrack.duration);
        return { track: thisTrack, trackIndex: thisTrack.index, position: trackPosition };
      }
      return {};
    }
    if (progress === 0) {
      const thisTrack = tracks.current[0];
      const trackPosition = thisTrack.cut.in;
      return { track: thisTrack, trackIndex: thisTrack.index, position: trackPosition };
    }
    if (progress === 1) {
      const thisTrack = tracks.current[tracks.current.length - 1];
      const trackPosition = thisTrack.cut.in + thisTrack.duration;
      return { track: thisTrack, trackIndex: thisTrack.index, position: trackPosition };
    }
    return {};
  };

  const getTrack = () => {
    return track.current;
  };

  const play = (progress: number = -1) => {
    console.log('play', progress);
    if (tracks.current.length === 0) {
      return;
    }
    // if (track.current) {
    //   if (!track.current.$audio.paused) {
    //     track.current?.$audio.pause();
    //   }
    // }
    if (progress >= 0) {
      const info = getProgressInfo(progress);
      if (info.track?.index !== track.current?.index) {
        pause();
      }
      playTrack(info?.trackIndex ?? 0, info.position);
      //   track.current = info.track;
      //   track.current.$audio.currentTime = info.position;
      //   track.current.$audio.play();
    } else {
      // resume
      if (isComplete) {
        playTrack(0);
      } else {
        if (track.current) {
          track.current.$audio.play();
        } else {
          playTrack(0);
        }
      }
    }
  };

  const playTrack = (trackIndex: number, cutIn: number = 0) => {
    console.log('playTrack', trackIndex);
    track.current = tracks.current[trackIndex];
    track.current.$audio.currentTime = cutIn || track.current.cut.in;
    track.current.$audio.play();
  };

  const pause = (progress: number = -1) => {
    if (!isPlaying) return;
    if (track.current) {
      if (!track.current.$audio.paused) {
        track.current?.$audio.pause();
      }
    }
    if (progress >= 0) {
      const info = getProgressInfo(progress);
      if (info.track) {
        track.current = info.track as AudioTrack;
        if (track.current?.$audio) {
          track.current.$audio.currentTime = info.position;
        }
        if (!track.current.$audio.paused) {
          track.current.$audio.pause();
        }
      }
    }
  };

  const cutOutAt = (progress: number = -1) => {
    if (progress >= 0) {
      const info = getProgressInfo(progress);
      if (info.track) {
        info.track.cut.out = info.position;
        (info.track as AudioTrack).update();
        tracks.current = tracks.current.slice(0, info.track.index + 1);
        updateTracks();
      }
    }
  };

  return { cutOutAt, getProgressInfo, getTrack, addTrack, removeTrack, tracks: publicTracks, play, pause, progress, isPlaying, duration, position, isComplete };
};
