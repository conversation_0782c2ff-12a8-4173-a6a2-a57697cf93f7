import { useState } from 'react';
import { Scrubber } from 'react-scrubber';
import { useMediaPlaylist } from '../hooks/useMediaPlaylist';

export const PlaylistScrubber = ({ playlist }: { playlist: ReturnType<typeof useMediaPlaylist> }) => {
  const [shouldPlay, setShouldPlay] = useState(false);
  const [active, setActive] = useState(false);
  const [localValue, setLocalValue] = useState(0);
  const scrubberPos = active ? localValue : playlist.progress;

  const seekStart = (progress: number) => {
    setActive(true);
    setShouldPlay(playlist.isPlaying);
    playlist.play(progress);
  };

  const seek = (progress: number) => {
    setLocalValue(progress);
    playlist.play(progress);
  };

  const seekEnd = (progress: number) => {
    if (shouldPlay) {
      playlist.play(progress);
    } else {
      playlist.pause(progress);
    }
    setActive(false);
  };

  return <Scrubber min={0} max={1} value={scrubberPos} onScrubStart={seekStart} onScrubChange={seek} onScrubEnd={seekEnd} />;
};
