import { PlayButton } from '../../../../components/player/AudioPlayerUI/PlayButton';
import { useAudioMode } from '../../../../framework/useAudioMode';
import { AudioMode } from '../../../../models/models';
import { useAudioElement } from '../../audio/hooks/useAudioElement';
import { AudioToggle } from '../../audio/ui/AudioToggle';
import { useMediaStatus } from '../../media/hooks/useMediaStatus';
import { usePlaylist } from '../hooks/usePlaylist';

export const PlaylistToggleControl = ({ trackId = '', className = 'play-button-squash' }: { trackId?: string; className?: string }) => {
  const { currentTrack } = usePlaylist();
  const { audioMode } = useAudioMode();
  const isActive = audioMode === AudioMode.PLAY && (trackId === '' || trackId === currentTrack?.id);
  const $audio = useAudioElement();
  const { status } = useMediaStatus($audio, isActive);
  return (
    <AudioToggle>
      <div style={{ padding: 3 }}>
        <PlayButton status={status} className={className} />
      </div>
    </AudioToggle>
  );
};
