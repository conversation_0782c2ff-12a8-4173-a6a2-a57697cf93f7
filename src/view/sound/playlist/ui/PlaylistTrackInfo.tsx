import { Mugshot } from '../../../../components/common/Mugshot';
import { SmartLink } from '../../../../components/common/SmartLink';
import { TrackCopy } from '../../../../components/swell-card/TrackInfo';
import { getAuthorLink } from '../../../../utils/swell-utils';
import { useAudioElement } from '../../audio/hooks/useAudioElement';
import { useMediaDuration } from '../../media/hooks/useMediaDuration';
import { useMediaProgress } from '../../media/hooks/useMediaProgress';
import { useMediaStatus } from '../../media/hooks/useMediaStatus';
import { usePlaylist } from '../hooks/usePlaylist';

export const PlaylistTrackInfo = ({ trackId = '' }: { trackId?: string }) => {
  const { currentTrack } = usePlaylist();
  const isActive = trackId === '' || trackId === currentTrack?.id;
  const swell = currentTrack?.data;
  const $audio = useAudioElement();
  const { duration } = useMediaDuration($audio, isActive);
  const { progress, position } = useMediaProgress($audio, isActive);
  const { isLoading } = useMediaStatus($audio, isActive);
  const authorLink = swell ? getAuthorLink(swell.author) : '';
  const authorImage = swell ? swell.author.image : '';
  const authorAlias = swell ? `@${swell.author.alias}` : '';

  if (swell) {
    return (
      <div className='d-grid gap-1 w-100 align-items-center' style={{ gridTemplateColumns: 'min-content auto min-content' }}>
        <SmartLink to={authorLink}>
          <Mugshot image={authorImage ?? ''} size={50} isLoading={isLoading} progress={progress} alt={authorAlias} />
        </SmartLink>
        <div className='overflow-hidden'>
          <TrackCopy author={swell.author} createdOn={swell.createdOn} duration={duration - position} />
        </div>
      </div>
    );
  }

  return null;
};
