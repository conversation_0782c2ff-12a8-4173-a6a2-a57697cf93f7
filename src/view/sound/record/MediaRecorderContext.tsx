import { createContext } from 'react';
import { IRecordingOutput } from '../../../components/player/AudioRecorder/AudioRecorderContext';
import { useMediaPlaylist } from '../playlist/hooks/useMediaPlaylist';

interface IMediaRecorderContext {
  isActive: boolean;
  isConnecting: boolean;
  isBlocked: boolean;
  isRecording: boolean;
  isMuted: boolean;
  hasData: boolean;
  duration: number;
  maxDuration: number;
  isMaxDuration: boolean;
  setMaxDuration(n: number): void;
  getWaveData(): number[];
  stopRecording(): void;
  toggleRecording(): Promise<boolean>;
  resetRecording(): void;
  setSpliceTime(t: number): void;
  init(): void;
  completeRecording(): Promise<IRecordingOutput | null>;
  playlist: ReturnType<typeof useMediaPlaylist>;
}

export const MediaRecorderContext = createContext<IMediaRecorderContext>({} as IMediaRecorderContext);
