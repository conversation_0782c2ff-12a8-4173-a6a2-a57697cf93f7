@use '../../../../assets/css/base' as *;

.media-recorder-toggle {
  @extend .btn-reset, .rounded-circle, .text-white, .overflow-hidden;
  $scale: 1;
  position: relative;
  width: $scale * 60px;
  height: $scale * 60px;
  aspect-ratio: 1/1;
  --icon-color: #fff;
  --icon-fill: #fff;
  transition: opacity 0.3s;

  &:before {
    @extend .bg-recording;
    content: '';
    display: block;
    position: absolute;
    inset: 0;
    background-color: green;
    transition: background-color 0.3s, opacity 0.3s;
  }

  > * {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .swell-icon {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .swell-icon-block path {
    stroke-width: 2px;
  }

  &[data-status='none'] {
    .swell-icon-mic {
      opacity: 1;
    }
  }
  &[data-status='connecting'] {
    opacity: 0.8;
    .swell-icon-mic {
      opacity: 1;
    }
    .swell-icon-loader {
      opacity: 1;
    }
    &:before {
      @extend .bg-grey;
      //   @extend .bg-recording;
    }
  }
  &[data-status='blocked'] {
    .swell-icon-block {
      opacity: 0.5;
    }
    .swell-icon-mic {
      opacity: 1;
    }
    &:before {
      @extend .bg-grey;
    }
  }
  &[data-status='muted'] {
    .swell-icon-mic {
      opacity: 1;
    }
    &:before {
      //   @extend .bg-red;
      @extend .bg-recording;
    }
  }
  &[data-status='recording'] {
    .swell-icon-pause {
      opacity: 1;
    }
    &:before {
      @extend .bg-recording;
      animation: undulate 2.5s infinite;
    }
  }
  &[data-status='maxduration'] {
    opacity: 0.8;
    .swell-icon-mic {
      opacity: 1;
    }
    &:before {
      @extend .bg-grey;
    }
  }
}
