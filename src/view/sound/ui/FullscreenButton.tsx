import { Fullscreen } from '@mui/icons-material';
import classNames from 'classnames';
import { useCallback } from 'react';
import { useVideoElement } from '../video/hooks/useVideoElement';

type CrossBrowserFullScreen = HTMLVideoElement & {
  webkitRequestFullscreen?(): Promise<void>;
  mozRequestFullScreen?(): Promise<void>;
  msRequestFullscreen?(): Promise<void>;
  webkitEnterFullscreen?(): void;
};

export const FullscreenButton = ({ className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const { $video } = useVideoElement();

  const enterFullscreen = useCallback(async () => {
    const video = $video.current as CrossBrowserFullScreen;
    if (!video) {
      throw new Error('no video');
    }

    // standard
    if (video.requestFullscreen) {
      return video.requestFullscreen();
    }
    // WebKit
    else if (video.webkitRequestFullscreen) {
      video.webkitRequestFullscreen();
    }
    // Firefox
    else if (video.mozRequestFullScreen) {
      return video.mozRequestFullScreen();
    }
    // IE/Edge
    else if (video.msRequestFullscreen) {
      return video.msRequestFullscreen();
    }
    // iOS/Chrome
    else if (video.webkitEnterFullscreen) {
      return video.webkitEnterFullscreen();
    }
    return false;
  }, [$video]);

  const handleFullscreen: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    if (!$video.current) return;

    if (document.fullscreenElement) {
      document.exitFullscreen().catch(() => {});
    } else {
      enterFullscreen()
        .then(() => $video.current!.play().catch(() => {}))
        .catch(() => {});
    }
  };

  return (
    <button className={classNames('btn btn-link text-white bg-black-40 rounded-circle min-aspect-square p-1', className)} {...props} onClick={handleFullscreen}>
      <Fullscreen />
    </button>
  );
};
