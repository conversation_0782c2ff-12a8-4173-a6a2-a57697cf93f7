import './video.scss';

import { isServer } from '@tanstack/react-query';
import { useCallback, useEffect, useRef } from 'react';

import { VideoElementContext } from './VideoElementContext';

export const VideoElementProvider = ({ children }: { children: React.ReactNode }) => {
  const $video = useRef<HTMLVideoElement>(isServer ? null! : document.createElement('video'));
  const $ref = useRef<HTMLDivElement>(null!);

  useEffect(() => {
    if ($video.current) {
      $video.current.controls = false;
      ($video.current as unknown as { controlsList: string }).controlsList = 'nodownload';
      $video.current.playsInline = true;
      $video.current.disablePictureInPicture = true;

      const handleClick = (e: MouseEvent) => {
        // without this, fullscreen mode toggles play twice resulting in a blip in playback only
        e.stopPropagation();
        e.preventDefault();
        if ($video.current.paused) {
          $video.current.play().catch(() => {});
        } else {
          $video.current.pause();
        }
      };

      $video.current.addEventListener('click', handleClick);

      return () => {
        $video.current.removeEventListener('click', handleClick);
      };
    }
    setVideoParent($ref.current);
  }, []);

  const setVideoParent = useCallback((el: HTMLElement | null) => {
    if ($video.current.parentElement) $video.current.parentElement.removeChild($video.current);
    (el ?? $ref.current).appendChild($video.current);
  }, []);

  return (
    <VideoElementContext.Provider value={{ $video, setVideoParent }}>
      {children}
      <div ref={$ref} style={{ display: 'none' }} />
    </VideoElementContext.Provider>
  );
};
