import { useEffect, useRef } from 'react';
import { useVideoElement } from './hooks/useVideoElement';

export const VideoOutlet = ({ active = false, ...props }: { active?: boolean } & React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>) => {
  const { setVideoParent } = useVideoElement();
  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (active && ref.current) {
      setVideoParent(ref.current);
      return () => setVideoParent(null);
    }
  }, [active, setVideoParent]);

  return <div ref={ref} className='video-outlet' {...props} />;
};
