import { useEffect } from 'react';
import { useAudioTrack } from '../../components/player/AudioPlayer/useAudioTrack';
import { useAudioTracking } from '../../tracking/useTrackerAudio';
import { useTrackerWidgetContainer } from '../../tracking/useTrackerWidgetContainer';
import { useIFrameMessage } from '../../utils/useIFrameMessage';
import { useListenTimePopup } from '../../utils/useListenTimePopup';

export const SwellWidgetController = () => {
  const track = useAudioTrack();
  // tracking
  useTrackerWidgetContainer();
  useAudioTracking();
  useListenTimePopup();

  const message = useIFrameMessage();

  // send playing status to widget manager
  useEffect(() => {
    message.send('status', track.isPlaying ? 'playing' : 'paused');
  }, [track.isPlaying]);

  // pause if widget manager requests
  useEffect(() => {
    if (message.data?.action === 'status' && message.data?.payload === 'pause') {
      track.pause();
    }
  }, [message.data]);

  return <div className='d-none' />;
};
