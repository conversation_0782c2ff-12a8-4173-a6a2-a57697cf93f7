import { RouteObject } from 'react-router-dom';
import { VisitSwellLife } from '../../utils/VisitSwellLifeScreen';
import { SwellView } from '../website/SwellView';
import { SwellWidgetLayout } from './SwellWidgetLayout';

export const swellWidgetRoutes: RouteObject[] = [
  {
    path: '/widget-swell',
    element: <SwellWidgetLayout />,
    children: [
      { index: true, element: <VisitSwellLife /> },
      { path: ':listId/hashtag/:hash/:canonicalId/:slug?/:replyId?', element: <SwellView /> },
      { path: ':listId/:canonicalId/:slug?/:replyId?', element: <SwellView /> },
      { path: '*', element: <VisitSwellLife /> },
    ],
  },
];
