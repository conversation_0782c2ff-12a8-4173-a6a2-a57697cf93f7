import { useEffect } from 'react';
import { useAudioStatus } from '../../components/player/AudioPlayer/useAudioStatus';
import { useAudioTrack } from '../../components/player/AudioPlayer/useAudioTrack';
import { useOrchestration } from '../../framework/useOrchestration';
import { AudioMode, PlayerStatus } from '../../models/models';
import { useAudioTracking } from '../../tracking/useTrackerAudio';
import { useTrackerWidgetContainer } from '../../tracking/useTrackerWidgetContainer';
import { getElementY, scrollWindowTo } from '../../utils/scrollWindowTo';
import { useIFrameMessage } from '../../utils/useIFrameMessage';
import { useListenTimePopup } from '../../utils/useListenTimePopup';
import { useRouteParams } from '../../utils/useRouteParams';

export const SwellcastWidgetController = () => {
  useAudioTracking();
  useListenTimePopup();
  useTrackerWidgetContainer();
  useAudioTracking();

  const audioStatus = useAudioStatus();
  const track = useAudioTrack();
  const orch = useOrchestration();
  const message = useIFrameMessage();
  const params = useRouteParams();

  // scroll to active swell card
  useEffect(() => {
    if (params.canonicalId) {
      const $card = document.querySelector<HTMLElement>(`[data-swellcard="${params.canonicalId}"]`);
      if ($card) {
        scrollWindowTo(getElementY($card));
      }
      // force player to open when clicking previously selected swell
      track.play();
      orch.open({ mode: AudioMode.PLAY });
    }
  }, [params.canonicalId]);

  // send playing status to widget manager
  useEffect(() => {
    // console.log({ audioStatus: PlayerStatus[audioStatus] });
    switch (audioStatus) {
      case PlayerStatus.PLAYING:
        message.send('status', 'playing');
        break;
      case PlayerStatus.PAUSED:
      case PlayerStatus.NONE:
        message.send('status', 'paused');
        break;
    }
  }, [audioStatus]);

  // send playing status to widget manager
  useEffect(() => {
    message.send('statusX', track.isPlaying ? 'playing' : 'paused');
  }, [track.isPlaying]);

  // pause if widget manager requests
  useEffect(() => {
    if (message.data?.action === 'status' && message.data?.payload === 'pause') {
      track.pause();
    }
  }, [message.data]);

  return null;
};
