import { Outlet } from 'react-router-dom';
import { WatermarkLogo } from '../../components/common/WatermarkLogo';
import { WidgetFooter } from '../../components/common/WidgetFooter';
import { SwellAudioPlayer } from '../../components/player/AudioPlayerUI/SwellAudioPlayer';
import { SwellBase } from '../../framework/shared/SwellBase';
import { SwellcastWidgetController } from './SwellcastWidgetController';

export const SwellcastWidgetLayout = () => {
  return (
    <SwellBase>
      <WatermarkLogo />
      <SwellcastWidgetController />
      <div className='d-flex flex-column' style={{ flex: '1 1 auto', minHeight: '100vh' }}>
        <div style={{ flex: '1 1 auto' }}>
          <Outlet />
        </div>
      </div>
      <WidgetFooter />
      <SwellAudioPlayer />
    </SwellBase>
  );
};
