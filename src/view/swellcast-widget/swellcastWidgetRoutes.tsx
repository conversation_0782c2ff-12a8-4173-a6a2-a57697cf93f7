import { RouteObject } from 'react-router-dom';
import { StationType } from '../../generated/graphql';
import { VisitSwellLife } from '../../utils/VisitSwellLifeScreen';
import { SwellcastView } from '../website/SwellcastView';
import { StationView } from '../website/stations/StationView';
import { SwellcastWidgetLayout } from './SwellcastWidgetLayout';

export const swellcastWidgetRoutes: RouteObject[] = [
  {
    path: '/widget',
    element: <SwellcastWidgetLayout />,
    children: [
      { index: true, element: <VisitSwellLife /> },
      { path: 'category/:listId/:slug?', element: <StationView type={StationType.Category} /> },
      { path: 'country/:listId/:slug?', element: <StationView type={StationType.Country} /> },
      { path: 'language/:listId/:slug?', element: <StationView type={StationType.Language} /> },
      { path: ':listId/hashtag/:hash?', element: <SwellcastView /> },
      { path: ':listId/hashtag/:hash/:canonicalId/:slug?/:replyId?', element: <SwellcastView /> },
      { path: ':listId/:canonicalId/:slug?/:replyId?', element: <SwellcastView /> },
      { path: ':listId', element: <SwellcastView /> },
      { path: '*', element: <VisitSwellLife /> },
    ],
  },
];
