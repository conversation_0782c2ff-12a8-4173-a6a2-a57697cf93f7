import { Suspense } from 'react';
import { Link, Outlet } from 'react-router-dom';
import { WebsiteHeader, WebsiteHeaderSpacer } from '../../../components/header/header';
import { SwellBase } from '../../../framework/shared/SwellBase';

const Layout = () => {
  return (
    <SwellBase>
      <Suspense fallback={<div>loading...</div>}>
        <div className='d-grid' style={{ gridTemplateRows: 'min-content auto min-content', minHeight: '100vh', maxWidth: '100vw' }}>
          <WebsiteHeader />
          <main>
            <WebsiteHeaderSpacer />
            <nav className='d-flex gap-3'>
              <Link to='/'>home</Link>
            </nav>
            <Outlet />
          </main>
          <footer className='p-3 d-flex gap-3'>Footer</footer>
        </div>
      </Suspense>
    </SwellBase>
  );
};

export default Layout;
