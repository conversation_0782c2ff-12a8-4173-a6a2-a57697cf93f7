import { RouteObject } from 'react-router-dom';
import Layout from './pages/Layout';
import { PageIndex } from './pages/Pages';

export const testRouteMap: RouteObject[] = [
  {
    path: '/',
    element: <Layout />,
    children: [
      { index: true, element: <PageIndex /> },
      {
        path: '*',
        element: (
          <div>
            <h1>Relax...</h1>
            <p>This test endpoint is only used in local and stage. It's just by chance that a username exists called "test".</p>
          </div>
        ),
      },
    ],
  },
];
