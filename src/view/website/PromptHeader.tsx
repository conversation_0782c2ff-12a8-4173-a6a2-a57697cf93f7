import { SmartLink } from '../../components/common/SmartLink';
import { SharePromptButton } from '../../components/embed-code/SharePromptButton';
import { SwellDescription } from '../../components/swell-card/CardDescription';
import { Bruno<PERSON>aveCard } from '../../framework/settings/settings';
import { OpenApiSwellSwellcastModel } from '../../generated/graphql';
import { getAuthorFullname, getOwnerLink } from '../../utils/swell-utils';
import { toBgUrl } from '../../utils/Utils';

export const PromptHeader = ({ swellcast, promptId }: { swellcast: OpenApiSwellSwellcastModel; promptId: string }) => {
  const ownerLink = getOwnerLink(swellcast);
  const fullname = getAuthorFullname(swellcast.owner);
  const bgImage = swellcast?.image ?? BrunoWaveCard;

  return (
    <div style={{ position: 'relative', height: 'min(40vh, 200px)' }}>
      <div
        className='fade-mask-bottom absolute-fill'
        style={{
          height: 'inherit',
          backgroundImage: toBgUrl(bgImage), //
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          filter: 'blur(5px) contrast(0.5) opacity(0.8)',
          transform: 'scale(1)',
          transformOrigin: 'center',
        }}
      />
      <div className='mx-auto position-relative' style={{ height: 'inherit', maxWidth: 800 }}>
        <div
          className='fade-mask-bottom absolute-fill'
          style={{
            height: 'inherit',
            backgroundImage: toBgUrl(bgImage), //
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        ></div>

        <div className='absolute-fill position-relative d-flex flex-column w-100 justify-content-between grid-pad'>
          <div className='d-flex w-100 gap-2 justify-content-between'>
            <div className='d-flex gap-2'>{/* Future home of station share button */}</div>
            <div className='d-flex gap-2' style={{ position: 'relative', zIndex: 20 }}>
              <SharePromptButton promptId={promptId} />
            </div>
          </div>
          <div className='d-flex flex-column gap-2 text-shadow-mode'>
            {/* <SmartLink to={ownerLink}> */}
            <div className='fs-2 fw-bold text-break'>{fullname}</div>
            <div className='fs-4 fw-light opacity-75'>{swellcast.name}</div>
            {/* </SmartLink> */}
            <SwellDescription description={swellcast.description} className='m-0 d-none d-lg-block' style={{ zIndex: 20, position: 'relative' }} />
          </div>
        </div>
      </div>
      <SmartLink to={ownerLink} className='absolute-fill' style={{ zIndex: 10 }}></SmartLink>
    </div>
  );
};
