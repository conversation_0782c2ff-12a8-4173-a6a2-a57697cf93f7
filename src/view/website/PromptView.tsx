import { Edit } from '@mui/icons-material';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { usePromptPage } from '../../api/gql.getPromptPage';
import { ThemeColors } from '../../assets/css/ThemeColors';
import { SwellIcons } from '../../assets/icons/SwellIcons';
import { BounceLoaderPage } from '../../components/common/bounceloader/BounceLoaderPage';
import { HR } from '../../components/common/HR';
import { useAuth } from '../../components/login/useAuth';
import { InfiniteSectionsView } from '../../components/sections/InfiniteSectionsView';
import { SwellLink } from '../../components/swellbutton/SwellButton';
import { HelmetBuilder } from '../../framework/HelmetBuilder';
import { AllowedPromptResponseTypes } from '../../generated/graphql';
import { dataLayer } from '../../tracking/Tracking';
import { getOGFromPrompt, getPromptLink2 } from '../../utils/swell-utils';
import { useRouteParams } from '../../utils/useRouteParams';
import { useScrollRestore } from '../../utils/useScrollRestore';
import { PromptHeader } from './PromptHeader';
import { SwellcastError } from './SwellcastError';

export const PromptView = () => {
  const auth = useAuth();
  const params = useRouteParams();
  const query = usePromptPage({ id: params?.promptId ?? '' });
  const nav = useNavigate();
  const og = query.isSuccess ? getOGFromPrompt(query.data.pages[0]) : {};
  const prompt = query.data?.pages[0];

  useScrollRestore(query.isSuccess);

  const onClickRecord: React.MouseEventHandler<HTMLAnchorElement> = () => {
    if (prompt) {
      // JIRA: https://anecure.atlassian.net/browse/SA-7772
      dataLayer({
        event: 'record_episode',
        promptId: params.promptId,
        swellcastAlias: prompt.swellcast.owner?.alias,
        userAlias: auth.getAlias(),
        type: 'audio',
      });
    }
  };

  const onClickWrite: React.MouseEventHandler<HTMLAnchorElement> = () => {
    if (prompt) {
      // JIRA: https://anecure.atlassian.net/browse/SA-7772
      dataLayer({
        event: 'record_episode',
        promptId: params.promptId,
        swellcastAlias: prompt.swellcast.owner?.alias,
        userAlias: auth.getAlias(),
        type: 'text',
      });

      // JIRA: https://anecure.atlassian.net/browse/SA-7839
      dataLayer({
        context: 'start_swell',
        event: 'swellrecorder',
        promptId: params.promptId,
        swellcastAlias: prompt.swellcast.owner?.alias,
        swellcastId: prompt.swellcast.owner?.id,
        swellid: 'NewSwell',
        userAlias: auth.getAlias(),
        type: 'text',
      });
    }
  };

  useEffect(() => {
    if (query.isSuccess && prompt) {
      // correct alias if unknown
      const swellcastAlias = prompt.swellcast?.owner?.alias ?? null;
      if (prompt && params?.listId?.toLowerCase() !== swellcastAlias?.toLowerCase()) {
        // Preserve the slug when redirecting
        nav(getPromptLink2(prompt), { replace: true });
      }
    }
  }, [nav, params?.listId, prompt, query.isSuccess]);

  if (query.isError) {
    return <SwellcastError description={query?.error?.message} />;
  }

  if (query.isSuccess) {
    const prompt = query.data.pages[0];
    const allowAudio = prompt.allowedResponseTypes?.some((v) => v == AllowedPromptResponseTypes.Audio);
    const allowText = prompt.allowedResponseTypes?.some((v) => v == AllowedPromptResponseTypes.Text);
    const writeLink = prompt.promptLink + '/T';

    return (
      <div>
        <HelmetBuilder {...og} />
        <PromptHeader swellcast={prompt.swellcast} promptId={params.promptId!} />
        <div className='container-md'>
          <div className='flex-center p-5'>
            <div className='d-flex flex-column align-items-center gap-4 fix'>
              <img src={prompt.promptImageUrl} style={{ maxWidth: '80vw', width: 250 }} alt={prompt.altText} />

              <div className='d-flex gap-2'>
                {allowAudio ? (
                  <SwellLink.Primary to={prompt.promptLink} reloadDocument={true} onClick={onClickRecord} Icon={SwellIcons.Mic} className='px-4'>
                    Record
                  </SwellLink.Primary>
                ) : null}
                {allowText ? (
                  <SwellLink.Primary to={writeLink} reloadDocument={true} onClick={onClickWrite} Icon={Edit} className='write-btn px-4'>
                    Write
                  </SwellLink.Primary>
                ) : null}
              </div>

              {prompt.isEditorsPick ? (
                <div>
                  <EditorsPick />
                </div>
              ) : null}
            </div>
          </div>
          <HR />
          <InfiniteSectionsView query={query} />
        </div>
      </div>
    );
  }

  return <BounceLoaderPage />;
};

const EditorsPick = () => (
  <div className='rounded-pill Xbg-grey-30 flex-center Xpy-2 Xpx-3 gap-2'>
    <SwellIcons.Star style={{ height: 16, '--icon-color': 'none', '--icon-fill': ThemeColors.squash }} /> Editor's Pick
  </div>
);
