import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
// TODO: can this be replaced by RefreshPage?
// force end points to reload. They are a separate build.
export const RedirectReload = () => {
  const loc = useLocation();
  useEffect(() => window.location.reload(), []);
  if (loc.search) {
    return <Navigate to={loc.pathname + loc.search} />;
  } else {
    // stop recursive load at /go/finder
    return <Navigate to={'/'} />;
  }
};
