import { useEffect, useRef } from 'react';
import { buildSchema } from '../../../server/buildSchema';
import { isValidCanonicalId, useSwell } from '../../api/gql.loadSwellById';
import { BounceLoaderPage } from '../../components/common/bounceloader/BounceLoaderPage';
import { HR } from '../../components/common/HR';
import { Spacer } from '../../components/common/Spacer';
import { SidebarCTA } from '../../components/common/swellui/SidebarCTA';
import { SwellcastMiniHeader } from '../../components/common/swellui/SwellcastMiniHeader';
import { SwellMaster } from '../../components/common/swellui/SwellMaster';
import { SwellReply } from '../../components/common/swellui/SwellReply';
import { SwellSuggestions } from '../../components/common/swellui/SwellSuggestions';
import { HelmetBuilder } from '../../framework/HelmetBuilder';
import { useAudioMode } from '../../framework/useAudioMode';
import { trackSwellPageView } from '../../tracking/Tracking';
import { isSwellResponseOpen } from '../../utils/isOpenSwellcast';
import { getOGFromReply, getOGFromSwell, getSwellContentType } from '../../utils/swell-utils';
import { useAutoPlay } from '../../utils/useAutoPlay';
import { useRouteParams } from '../../utils/useRouteParams';
import { useScrollRestore } from '../../utils/useScrollRestore';
import { SwellcastError } from './SwellcastError';

export const SwellView = () => {
  const params = useRouteParams();
  const swell = useSwell(params);
  const canonicalId = useRef('');
  const { setTrackingData } = useAudioMode();

  useScrollRestore(swell.isSuccess);
  useAutoPlay();

  // tracking
  useEffect(() => {
    if (swell.isSuccess) {
      if (canonicalId.current !== params.canonicalId) {
        // need to dedupe tracking - I think login is seeing this as a new load
        canonicalId.current = params?.canonicalId ?? '';
        trackSwellPageView(swell.data);
      }
    }
  }, [params.canonicalId, swell.data, swell.isSuccess]);

  // need to set values for tracking
  useEffect(() => {
    if (swell.isSuccess) {
      setTrackingData({
        type: getSwellContentType(swell.data), //
        promptId: swell.data?.promptId ?? null, // set prompt/overwrite
        swellcastId: swell.data.swellcast?.id,
        swellcastAlias: swell.data.swellcast?.owner?.alias,
      });
    }
  }, [setTrackingData, swell.data, swell.isSuccess]);

  if (swell.isError || !isValidCanonicalId(params?.canonicalId)) {
    return <SwellcastError description={swell?.error?.description ?? swell.failureReason?.description} url={`/${params.listId}`} />;
  }

  if (swell.isSuccess && swell.data) {
    // show reply og if this is the url
    const reply = params?.replyId ? swell.data?.replies?.find((r) => r.id === params.replyId) : null;
    const og = reply ? getOGFromReply(swell.data, reply) : getOGFromSwell(swell.data);
    const _isOpenSwellcast = isSwellResponseOpen(swell.data);

    return (
      <>
        <HelmetBuilder {...og} schema={buildSchema(og.canonicalPath ?? '', swell.data)} />
        <div data-component='swellview' className='w-100'>
          {_isOpenSwellcast && swell.data.swellcast ? <SwellcastMiniHeader swellcast={swell.data.swellcast} /> : null}

          <div className='swellview p-2'>
            <div className='swellview-left' />

            <div className='swellview-center'>
              <SwellMaster swell={swell.data} />
            </div>

            <div className='swellview-right flex-center p-3'>
              <div className='swell-cta-sidebar'>
                <SidebarCTA context='swell-sidebar' />
              </div>
            </div>

            <div className='swellview-left' />

            <div className='swellview-center'>
              <div className='d-flex flex-column gap-3'>
                {swell.data.replies?.map((reply) => (
                  <SwellReply key={reply.id} reply={reply} swell={swell.data} />
                ))}
              </div>
            </div>

            <div className='swellview-right' />
          </div>

          <div className='swell-cta-bottombar'>
            <div className='flex-center p-3' style={{ height: '40vh' }}>
              <SidebarCTA context='swell-bottombar' />
            </div>
          </div>
          <HR className='mt-5' />
          <SwellSuggestions canonicalId={params.canonicalId ?? ''} />
        </div>
        <Spacer />
      </>
    );
  }

  return <BounceLoaderPage />;
};

// export default SwellView;
