import { SwellIcons } from '../../assets/icons/SwellIcons';
import { SwellLink } from '../../components/swellbutton/SwellButton';
import { Localize } from '../../i18n/Localize';

export const SwellcastError = ({
  title = Localize.GENERAL_ERROR, //
  description = Localize.GENERAL_ERROR_DESCRIPTION,
  url = '/search/latest%20swells',
  label = "Let's try something else",
}: {
  title?: string;
  description?: string;
  url?: string;
  label?: string;
}) => (
  <div className='p-5 flex-center' style={{ height: '100%', minHeight: '33vh' }}>
    <div className='text-center d-flex flex-column gap-3' style={{ maxWidth: '80vw', width: 400 }}>
      {title ? <h2>{title}</h2> : null}
      {description ? <p>{description}</p> : null}
      <div className='mx-auto'>
        <SwellLink.Primary to={url} Icon={SwellIcons.ChevronRight} style={{ width: 'fit-content' }}>
          {label}
        </SwellLink.Primary>
      </div>
    </div>
  </div>
);
