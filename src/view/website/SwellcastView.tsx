import { useEffect, useMemo, useRef } from 'react';
import { useProfilePage } from '../../api/gql.getProfilePage';
import { OpenApiSwellcastResponseEnhanced } from '../../api/gql.getSwellcast';
import { OwnerProvider } from '../../components/common/owner/OwnerProvider';
import { SwellcastHashHeader } from '../../components/common/swellcastui/SwellcastHashHeader';
import { SwellcastHeader } from '../../components/common/swellcastui/SwellcastHeader';
import { InfiniteSectionsView } from '../../components/sections/InfiniteSectionsView';
import { HelmetBuilder } from '../../framework/HelmetBuilder';
import { useAudioMode } from '../../framework/useAudioMode';
import { useOrchestration } from '../../framework/useOrchestration';
import { AudioMode, RouteQuery } from '../../models/models';
import { trackSwellcastPageView } from '../../tracking/Tracking';
import { getOGFromProfile } from '../../utils/swell-utils';
import { useIsCurrentUser } from '../../utils/useIsCurrentUser';
import { useRouteParams } from '../../utils/useRouteParams';
import { useScrollRestore } from '../../utils/useScrollRestore';
import { useSearchParams } from '../../utils/useSearchParams';
import { SwellcastError } from './SwellcastError';

export const SwellcastView = () => {
  const params = useRouteParams();
  const alias = params?.listId ?? '';
  const query = useProfilePage({ alias, hashtagFilter: params?.hash });
  const og = query.isSuccess ? getOGFromProfile(query.data.pages[0], params) : {};

  useScrollRestore(query.isSuccess);
  useNewSwellAction(query.isSuccess, query.isSuccess ? query.data.pages?.[0]?.owner?.alias ?? '' : '');
  useSwellcastTracker(query.isSuccess, query.isSuccess ? query.data.pages?.[0] : null);

  // need to set values for tracking
  const { setTrackingData } = useAudioMode();
  const pages = useMemo(() => (query.isSuccess ? query.data.pages : []), [query.isSuccess, query.data]);

  useEffect(() => {
    if (query.isSuccess) {
      setTrackingData({ swellcastId: pages[0].id, swellcastAlias: pages[0].owner?.alias });
    }
  }, [query.isSuccess, pages, setTrackingData]);

  if (query.isError) {
    if (query.failureReason?.message === 'ERROR_SWELLCAST_NOT_FOUND') {
      return <SwellcastError title={query?.error?.description} description={''} />;
    }
    return <SwellcastError description={query?.error?.description} />;
  }

  return (
    <OwnerProvider alias={query.data?.pages[0].owner?.alias ?? ''}>
      <HelmetBuilder {...og} />
      <SwellcastHeader alias={alias} />
      <SwellcastHashHeader alias={alias} />
      <InfiniteSectionsView query={query} />
    </OwnerProvider>
  );
};

const useSwellcastTracker = (isSuccess: boolean, swellcast: Pick<OpenApiSwellcastResponseEnhanced, 'id' | 'name'> | null) => {
  const params = useRouteParams();
  const listId = useRef('');

  useEffect(() => {
    if (isSuccess && swellcast) {
      if (listId.current !== params.listId) {
        listId.current = params?.listId ?? '';
        trackSwellcastPageView(swellcast);
      }
    }
  }, [isSuccess, params.listId, swellcast]);
};

// start a new swell if conditions are right
const useNewSwellAction = (isSuccess: boolean, alias: string) => {
  const orch = useOrchestration();
  const isCurrentUser = useIsCurrentUser(alias ?? '');
  const searchParams = useSearchParams<RouteQuery & { action?: string }>({ offset: '0', action: '' });

  useEffect(() => {
    if (isSuccess && isCurrentUser && searchParams.action === 'new') {
      const t = setTimeout(() => {
        orch.open({ mode: AudioMode.RECORD_NEW });
      }, 100);

      return () => {
        clearTimeout(t);
      };
    }
  }, [isCurrentUser, searchParams.action, isSuccess, orch]);
};
