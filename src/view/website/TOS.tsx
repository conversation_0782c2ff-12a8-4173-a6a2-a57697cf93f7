import { useEffect } from 'react';
import { PopupContainer, PopupMessage } from '../../components/popup/PopupContainer';
import { usePopup } from '../../components/popup/usePopup';

export const TOSMessage = () => {
  const popup = usePopup();

  const onClick = () => {
    popup.close();
    window.dispatchEvent(new Event('agreed_to_tos'));
  };

  return (
    <PopupContainer>
      <PopupMessage
        title={
          <>
            We have updated our{' '}
            <a className='text-squash py-2 d-block' href='/termsofservice' target='_blank'>
              Terms&nbsp;of&nbsp;Service
            </a>
          </>
        }
        description="By clicking 'Ok' below, you accept the latest Terms of Service and Privacy Policy."
      >
        <button onClick={onClick} className='btn btn-primary' style={{ minWidth: 120 }}>
          OK
        </button>
      </PopupMessage>
    </PopupContainer>
  );
};

export const TOS = () => {
  const popup = usePopup();

  const onLaunchTOS = () => {
    popup.showPopup(<TOSMessage />, false);
  };

  const agreedToTOS = () => {
    console.log('User Agreed to TOS');
  };

  const showTOS = () => {
    window.dispatchEvent(new Event('show_tos'));
  };
  useEffect(() => {
    window.addEventListener('show_tos', onLaunchTOS);
    window.addEventListener('agreed_to_tos', agreedToTOS);
    window.swell_showTOS = showTOS;

    return () => {
      window.removeEventListener('show_tos', onLaunchTOS);
      window.removeEventListener('agreed_to_tos', agreedToTOS);
    };
  }, []);
  return null;
};
