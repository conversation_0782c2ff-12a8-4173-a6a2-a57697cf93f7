import { useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { useWindowSize } from 'usehooks-ts';
import { Footer } from '../../components/footer/footer';
import { WebsiteHeader, WebsiteHeaderSpacer } from '../../components/header/header';
import { SwellAudioPlayer } from '../../components/player/AudioPlayerUI/SwellAudioPlayer';
import { useIsSwellApp } from '../../finder/FinderUtils';
import { SwellBase } from '../../framework/shared/SwellBase';
import { DenyIframe } from '../../utils/DenyIframe';
import { TOS } from './TOS';
import { WebsiteController } from './WebsiteController';

// TODO: use open nav for desktop from swell developers

export const WebsiteLayout = () => {
  const isApp = useIsSwellApp();
  const size = useWindowSize();

  useEffect(() => {
    const cardWidth = 270;
    const padding = 20 * 2;
    const z = (size.width - padding) / cardWidth;
    document.body.style.setProperty('--swell-card-zoom', z.toString());
  }, [size.width]);

  return (
    <SwellBase>
      <TOS />
      <DenyIframe>
        <WebsiteController />
        {isApp ? null : <WebsiteHeader />}
        <div className='d-flex flex-column justify-content-center' style={{ flex: '1 1 auto', minHeight: '100vh' }}>
          {isApp ? null : <WebsiteHeaderSpacer />}
          <div style={{ flex: '1 1 auto' }}>
            <Outlet />
          </div>
        </div>
        {isApp ? null : <Footer />}
        <SwellAudioPlayer />
      </DenyIframe>
    </SwellBase>
  );
};
