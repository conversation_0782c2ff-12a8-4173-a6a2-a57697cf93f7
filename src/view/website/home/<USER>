import { SwellLogo } from '../../../components/common/SwellLogo';
import { StoreAction } from '../../../components/common/downloadapp/DownloadAppButton';
import { SearchForm } from '../search/SearchView';

export const HomeHero = () => {
  return (
    <>
      <DesktopHero />
      <MobileHero />
    </>
  );
};

const DesktopHero = () => {
  return (
    <div className='d-none d-lg-flex justify-content-center align-items-center position-relative overflow-hidden swell-cloud-colors'>
      <div className='d-flex flex-column gap-2 text-center text-white p-4'>
        <StoreAction context='website-homepage-hero'>
          <div style={{ width: 200 }}>
            <SwellLogo id='DesktopHero' />
          </div>
        </StoreAction>
        <a className='fix d-block fs-4' href='https://www.swell.life' target='_blank' rel='noreferrer'>
          New to Swell? Learn more <u>here</u>
        </a>
      </div>
      {new Array(32).fill(0).map((_e, i) => (
        <div key={`circle${i}`} className='circle-container'>
          <div className='circle'></div>
        </div>
      ))}
    </div>
  );
};

const MobileHero = () => {
  return (
    <div className='d-block d-lg-none'>
      <div className='d-flex flex-column gap-3 p-3'>
        <SearchForm className='rounded-pill' />
        <a className='fix d-block text-center' href='https://www.swell.life' target='_blank' rel='noreferrer'>
          New to Swell? Learn more <u>here</u>
        </a>
      </div>
    </div>
  );
};
