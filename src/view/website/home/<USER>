@use '../../../assets/css/base' as *;
/*

homepage header promo

*/
.headerpromo-fade {
  position: absolute;
  inset: 0 0 -1px 0;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 10%);
}

[data-component='headerpromo'],
[data-component='headerpromocard'] {
  width: 100vw;
  max-width: 100vw;
  aspect-ratio: 500/420;
  position: relative;
  flex: 0 0 100vw;
  scroll-snap-align: center;
  cursor: pointer;

  .headerpromo-frame {
    @extend .shadow;
    inset: 0px;
  }
  .headerpromo-gradient {
    background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 20%, rgba(255, 255, 255, 0.04) 90%);
  }
}
[data-bs-theme='dark'] [data-component='headerpromo'],
[data-bs-theme='dark'] [data-component='headerpromocard'] {
  .headerpromo-gradient {
    background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 20%, rgba(0, 0, 0, 0.04) 90%);
  }
  .headerpromo-fade {
    background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 10%);
  }
}

@media only screen and (min-width: 768px) {
  [data-component='headerpromo'],
  [data-component='headerpromocard'] {
    width: 400px;
    flex: 0 0 400px;
  }
  [data-component='headerpromo'] .headerpromo-frame,
  [data-component='headerpromocard'] .headerpromo-frame {
    overflow: hidden;
    border: 1px solid rgba(128, 128, 128, 0.2);
    &:hover {
      box-shadow: none;
    }
  }
}
