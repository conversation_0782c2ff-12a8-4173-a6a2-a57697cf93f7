@use 'sass:math';
@use '../../../assets/css/base' as *;

.circle-container {
  $particleNum: 36;
  $particleColor: hsl(180, 100%, 80%);
  position: absolute;
  transform: translate3d(0, 0, 0);
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  z-index: 1;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    mix-blend-mode: screen;
    background-image: radial-gradient(hsl(180, 100%, 80%, 0.9) 0%, hsl(180, 100%, 80%, 0.5) 10%, hsla(180, 100%, 80%, 0) 60%);

    animation: fadein-frames 5000ms infinite, scale-frames 10s infinite;

    @keyframes fadein-frames {
      0% {
        opacity: 1;
      }

      50% {
        opacity: 0.6;
      }

      100% {
        opacity: 1;
      }
    }

    @keyframes scale-frames {
      0% {
        transform: scale3d(0.7, 0.7, 1);
      }

      50% {
        transform: scale3d(1.7, 1.7, 1);
      }

      100% {
        transform: scale3d(0.7, 0.7, 1);
      }
    }
  }

  @for $i from 1 through $particleNum {
    &:nth-child(#{$i}) {
      .circle {
        $circleSize: calc(7 + math.random(5));
        width: $circleSize + px;
        height: $circleSize + px;
      }

      $startY: calc(1.1 + math.random(100) / 100);
      $endY: -0.25;
      $startX: calc(math.random(100) / 100);
      $endX: calc($startX + (-0.2 + (math.random(40) / 100)));
      $framesName: 'move-frames-' + $i;
      $moveDuration: 15000 + math.random(15000);
      $moveDelay: calc(-1 * math.random($moveDuration));

      animation-name: #{$framesName};
      animation-duration: $moveDuration + ms;
      animation-delay: $moveDelay + ms;

      @keyframes #{$framesName} {
        from {
          transform: translate3d(#{math.percentage($startX)}, #{math.percentage($startY)}, 0);
        }

        to {
          transform: translate3d(#{math.percentage($endX)}, #{math.percentage($endY)}, 0);
        }
      }

      .circle {
        animation-delay: math.random(4000) + ms;
      }
    }
  }
}
