import { useEffect, useState } from 'react';
import { useHomePage } from '../../../api/gql.getHomePageV2';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { useLoginPopup } from '../../../components/common/loginpopup/LoginPopup';
import { useAuth } from '../../../components/login/useAuth';
import { InfiniteSectionsView } from '../../../components/sections/InfiniteSectionsView';
import { useScrollRestore } from '../../../utils/useScrollRestore';
import { SwellcastError } from '../SwellcastError';
import { useSearchRedirect } from '../search/useSearchRedirect';
import { HomeHero } from './HomeHero';

const HomeView = () => {
  const query = useHomePage();
  const auth = useAuth();
  // Use a ref to track if we've initialized the component
  const [isInitialized, setIsInitialized] = useState(false);

  useLoginPopup();
  useScrollRestore(query.isSuccess);
  useSearchRedirect();

  // Run once on mount to set initialized state
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  // Show loading state until auth is ready and component is initialized
  if (!isInitialized || !auth.isReady) {
    return <BounceLoaderPage />;
  }

  // Once auth is ready and component is initialized, show content based on query state
  if (query.isSuccess) {
    return (
      <>
        <HomeHero />
        <InfiniteSectionsView query={query} />
      </>
    );
  }

  if (query.isError) {
    return <SwellcastError description={query?.error?.message} />;
  }

  return <BounceLoaderPage />;
};

export default HomeView;
