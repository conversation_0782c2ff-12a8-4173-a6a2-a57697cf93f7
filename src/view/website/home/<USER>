import { useNavigate } from 'react-router-dom';
import { WebViewAction } from '../../../finder/models';
import { HomePageActionModel } from '../../../generated/graphql';
import { SwellListType } from '../../../models/models';
import { slugify } from '../../../utils/Utils';
import { getLocalPath, isLocalPath } from '../../../utils/isLocalUrl';
import { DEFAULT_LIST_SLUG, getPromptLink, getSwellLink, getSwellcastLink } from '../../../utils/swell-utils';

export const usePromoAction = () => {
  const nav = useNavigate();

  const onClick = (action?: HomePageActionModel | null) => {
    if (action) {
      switch (action.type) {
        case WebViewAction.WEB_VIEW:
          if (action.params?.url) {
            window.open(action.params.url, '_blank');
          }
          break;

        case WebViewAction.NAVIGATE_TO_DEEPLINK:
          {
            const url = action.params?.url ?? action.route;
            if (url) {
              const params = new URLSearchParams();
              if (action.referrerId) {
                params.set('referrerId', action.referrerId);
              }
              if (isLocalPath(url)) {
                nav(`${getLocalPath(url)}?${params.toString()}`);
              } else {
                window.location.href = `${url}?${params.toString()}`;
              }
            }
          }
          break;

        case WebViewAction.NAVIGATE_TO:
          {
            const params = new URLSearchParams();
            if (action.referrerId) {
              params.set('referrerId', action.referrerId);
            }
            nav(`${action.route}?${params.toString()}`);
          }
          break;

        case WebViewAction.NAVIGATE_TO_SEARCH:
          if (action.params?.searchKey) {
            nav(`/search/${encodeURIComponent(action.params?.searchKey)}`);
          }
          break;

        case WebViewAction.NAVIGATE_TO_RECORD:
          {
            const params = new URLSearchParams();
            params.set('params', JSON.stringify(action.params));
            if (action.referrerId) {
              params.set('referrerId', action.referrerId);
            }
            nav(`/do/record?${params.toString()}`);
          }
          break;

        case WebViewAction.NAVIGATE_TO_PROMPT:
          {
            const params = new URLSearchParams();
            if (action.referrerId) {
              params.set('referrerId', action.referrerId);
            }
            // default alias "search" is replaced by actual alias after load
            // "search" is one of a few safe words that won't clash with user names
            nav(`${getPromptLink({ alias: action?.params?.alias ?? '', id: action.params?.id ?? '', slug: action?.params?.promptSlug ?? '' })}?${params.toString()}`); //`/search/prompt/${action.params?.promptId}?${params.toString()}`);
          }
          break;

        case WebViewAction.NAVIGATE_TO_USER_CONVERSATIONS:
          window.open(`/t/${action.params?.id}`);
          break;

        case WebViewAction.NAVIGATE_TO_SWELL:
          {
            if (action.params?.canonicalId) {
              const listId = (action?.params?.alias ?? 'user').toLowerCase();
              const slug = slugify(action?.params?.title ?? 'swell');
              nav(getSwellLink({ listId, canonicalId: action.params.canonicalId, slug }));
            } else {
              window.open(`/t/${action.params?.id}`);
            }
          }
          break;

        case WebViewAction.NAVIGATE_TO_SWELLCAST:
          if (action.params?.alias) {
            nav(getSwellcastLink({ listId: action.params.alias }));
          } else {
            window.open(`/t/${action.params?.id}`);
          }
          break;

        case WebViewAction.NAVIGATE_TO_STATION:
          nav(getSwellcastLink({ listId: action.params?.categoryId ?? '', listSlug: slugify(action.params?.title ?? DEFAULT_LIST_SLUG), listType: SwellListType.STATION }));
          break;
      }
    }
  };

  return onClick;
};
