@use '../../assets/css/base' as *;

$sidebar-width: fit-content;//250px;
$mobile-width: 1000px;
$nav-height: 61px;
$content-max-width: 100%;//1200px;

.site-main {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  .site-nav {
    @extend .shadow-sm, .border-bottom;
    justify-self: flex-start;
    width: 100%;

    .site-nav-menu {
      max-width: 60px;
      transition: max-width 0.3s;
      overflow: hidden;
    }
  }

  &.peekaboo .site-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    transition: transform 0.3s;
  }

  .site-center {
    flex: 1 0 auto;
    display: grid;
    grid-template-columns: min-content auto;

    .site-sidebar {
      @extend .shadow-sm, .bg-grey-light, .border-end;
      overflow-x: hidden;
      max-width: 0;
      transition: max-width 0.3s ease-out, padding-top 0.3s;
      max-height: 100vh;
      position: sticky;
      top: 0;
      overflow-y: scroll;

      .site-sidebar-inner {
        width: $sidebar-width;
      }
    }

    .site-content {
      //   padding-top: $nav-height;
      transition: padding-top 0s;
      position: relative;
      .site-content-container {
        // @extend .p-3, .p-lg-5;
        // max-width: $content-max-width;
        min-height: 85vh;
      }
      .site-content-blocker {
        position: absolute;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.3);
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s;
        z-index: 9;
      }
    }
  }

  .site-footer {
    justify-self: flex-end;
    width: 100%;
  }

  &[data-open='1'] {
    .site-center {
      .site-sidebar {
        width: $sidebar-width;
        max-width: $sidebar-width;
      }
    }
  }

  &.peekaboo[data-hidenav='0'] {
    .site-center {
      .site-sidebar {
        padding-top: $nav-height;
      }
      .site-content {
        .site-content-container {
          padding-top: $nav-height;
        }
      }
    }
  }

  &.peekaboo[data-hidenav='1'] {
    .site-nav {
      transform: translateY(-100%);
    }
  }
}

@media only screen and (max-width: $mobile-width) {
  .site-main {
    .site-center {
      .site-content {
        overflow: hidden;
        .site-content-container {
          width: 100vw;
        }
      }
    }

    &[data-open='1'] {
      .site-center {
        .site-content {
          .site-content-blocker {
            pointer-events: all;
            opacity: 1;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
          }
        }
      }
    }
  }
}

@media only screen and (min-width: 1000px) {
  .site-main {
    .site-nav {
      .site-nav-menu {
        max-width: 0;
      }
    }
    .site-center {
      .site-sidebar {
        width: $sidebar-width !important;
        max-width: $sidebar-width !important;
      }
    }
  }
}
