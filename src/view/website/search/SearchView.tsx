import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useMatch, useNavigate } from 'react-router-dom';

import classNames from 'classnames';
import { useSearchResultsPage } from '../../../api/gql.getSearchResultsPage';
import { ThemeColors } from '../../../assets/css/ThemeColors';
import { SwellIcons } from '../../../assets/icons/SwellIcons';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { CircleLoader } from '../../../components/common/circleloader/CircleLoader';
import { InfiniteSectionsView } from '../../../components/sections/InfiniteSectionsView';
import { SectionDataItem, SectionDataKeys } from '../../../components/sections/sections-models';
import { OpenApiHomePageSectionResponse } from '../../../generated/graphql';
import { stopPropagation } from '../../../utils/stopPropagation';
import { safeDecodeURIComponent, useRouteParams } from '../../../utils/useRouteParams';
import { useScrollRestore } from '../../../utils/useScrollRestore';
import { isValidSearchTerm } from './isValidSearchTerm';
import { processSearchTerm } from './processSearchTerm';

// TODO: these Are probably more useful utilities for sections
function sectionHasData(section: OpenApiHomePageSectionResponse) {
  const key = section.sectionDataType?.toLowerCase() as SectionDataKeys;
  const data = section[key] as SectionDataItem<typeof key>[];
  return data && data.length;
}

function sectionsPageHasData(sections: OpenApiHomePageSectionResponse[]) {
  return sections.findIndex(sectionHasData) > -1;
}

export const SearchView = () => {
  const _params = useRouteParams();
  const loc = useLocation();
  const term = processSearchTerm(_params?.search || new URLSearchParams(loc.search).get('search') || loc.hash);
  const isValidTerm = isValidSearchTerm(term);
  const query = useSearchResultsPage({ searchTerm: term }, { enabled: isValidTerm });
  const hasData = sectionsPageHasData(query.data?.pages?.[0]?.sections ?? []);

  useScrollRestore(isValidTerm && query.isSuccess);

  if (new URLSearchParams(loc.search).get('search')) {
    return <Navigate to={`/search/${encodeURIComponent(term)}`} />;
  }

  if (loc.hash) {
    return <Navigate to={`/search/${encodeURIComponent(term)}`} />;
  }

  let $results: React.ReactNode = null;

  if (term) {
    $results = <BounceLoaderPage />;

    if (query.isError) {
      $results = <SearchError />;
    } else if (query.isSuccess) {
      if (!query.isLoading && !query.isFetchedAfterMount && !hasData) {
        $results = <SearchLoading />;
      } else if (query.isLoading) {
        $results = <SearchLoading />;
      } else {
        if (hasData) {
          $results = <InfiniteSectionsView query={query} />;
        } else {
          $results = <SearchEmpty />;
        }
      }
    }
  }

  return (
    <div className='d-flex flex-column gap-3 w-100'>
      <div className='d-lg-none p-3'>
        <SearchForm className='rounded-pill' />
      </div>
      {$results}
    </div>
  );
};

export const SearchForm = ({ className = '', onSubmit: onSubmitEvent }: { className?: string; onSubmit?(): void }) => {
  const match = useMatch('/search/:search');
  const history = useNavigate();
  const [tmpTerm, setTmpTerm] = useState(safeDecodeURIComponent(match?.params?.search ?? ''));
  const searchResults = useSearchResultsPage({ searchTerm: safeDecodeURIComponent(match?.params?.search ?? '') });
  const [opened, setOpened] = useState(true);

  useEffect(() => {
    setOpened(true);
  }, []);

  useEffect(() => {
    if (match?.params?.search) {
      setTmpTerm(() => safeDecodeURIComponent(match?.params?.search ?? ''));
    }
  }, [match]);

  const onClickClose = () => {
    setTmpTerm('');
  };

  const onSubmit: React.FormEventHandler<HTMLFormElement> = async (e) => {
    e.preventDefault();
    const confirmed = true;
    if (confirmed && isValidSearchTerm(tmpTerm)) {
      history(`/search/${encodeURIComponent(processSearchTerm(tmpTerm))}`);
      onSubmitEvent?.();
    }
  };

  const onInput: React.FormEventHandler<HTMLInputElement> = (e) => {
    setTmpTerm(e.currentTarget.value);
  };

  const onFocus: React.FocusEventHandler<HTMLInputElement> = (e) => e.target.select();
  const isLoading = searchResults.isLoading && searchResults.fetchStatus !== 'idle';

  // Note: Circle loader here was having a seriously detrimental effect on performance in mobile

  return (
    <form className={classNames(className, 'p-1 search-form', { filled: tmpTerm, loading: isLoading, opened })} onSubmit={onSubmit} action='/search'>
      <button className='btn shadow-none btn-link btn-icon btn-search transparent position-relative'>
        <SwellIcons.Find style={{ width: 16 }} />
        <div className='search-loading' style={{ position: 'absolute', left: '50%', top: '50%', transform: 'translate(-50%,-50%)' }}>
          <CircleLoader size={36} color={ThemeColors.squash} />
        </div>
      </button>
      <div className='search-inputs'>
        <input
          name='search' //
          className='p-2 fs-2 fw-light text-reset flex-grow-1 d-block w-100'
          style={{ appearance: 'none', background: 'none', outline: 'none', border: 'none' }}
          type='input'
          placeholder='Search...'
          value={tmpTerm}
          maxLength={100}
          onInput={onInput}
          onKeyDown={stopPropagation}
          onFocus={onFocus}
        />
        <button type='button' className='btn btn-link Xbtn-icon Xbtn-close Xtransparent' onClick={onClickClose} style={{ display: tmpTerm === '' ? 'none' : 'block' }}>
          {/* <Close /> */}
          <SwellIcons.Close />
        </button>
      </div>
    </form>
  );
};

const SearchLoading = () => <SearchMessage>Searching...</SearchMessage>;

const SearchError = () => <SearchMessage>Hmm... that didn&apos;t work</SearchMessage>;

const SearchEmpty = () => (
  <div className='p-4 text-center mb-4'>
    <div className='p-4 bg-squash text-black rounded d-inline-block'>
      <div className='p-4'>
        <h3 className='m-0'>Sorry, nothing matched your&nbsp;search.</h3>
      </div>
    </div>
  </div>
);

const SearchMessage = ({ children }: { children: React.ReactNode }) => (
  <div className='p-3 text-center text-lg-left'>
    <h3 className='m-0 text-shadow-mode'>{children}</h3>
  </div>
);
