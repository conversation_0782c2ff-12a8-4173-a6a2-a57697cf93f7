@use '../../../assets/css/base';

[data-bs-theme='light'] .search-form {
  background-color: rgba(90, 90, 90, 0.2);
  svg {
    color: #000;
  }
}

[data-bs-theme='dark'] .search-form {
  background-color: rgba(90, 90, 90, 0.5);
  svg {
    color: #fff;
  }
}

.search-form {
  @extend .w-100, .d-flex, .justify-content-start, .align-items-center; //, .rounded-pill;
  max-width: 45px;
  overflow: hidden;
  //   transition: all 0.3s;

  .search-inputs {
    @extend .w-100, .d-flex, .justify-content-start, .align-items-center;
    opacity: 0;
    // transition: all 0.3s;
  }
  &.opened {
    max-width: 100vw;
    .search-inputs {
      opacity: 1;
    }
  }

  input {
    height: 2em;
  }
  .btn-close {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0s linear 0.2s;
  }
  &.filled {
    .btn-close {
      opacity: 1;
      visibility: visible;
      transition: opacity 0.2s, visibility 0s;
    }
  }
  .search-loading {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0s linear 0.2s;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  &.loading {
    .search-loading {
      opacity: 1;
      visibility: visible;
      transition: opacity 0.2s, visibility 0s;
    }
  }
}

.btn-icon {
  @extend .rounded-circle;
  position: relative;
  min-width: 45px;
  min-height: 45px;
  &:not(.transparent) {
    @extend .bg-grey-10;
    &:hover {
      @extend .bg-grey-80;
    }
  }

  svg {
    font-size: 1.75rem;
    vertical-align: bottom;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  &.transparent {
    background: none;
  }
}
