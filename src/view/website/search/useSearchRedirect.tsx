import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { isValidSearchTerm } from './isValidSearchTerm';

// this migrates the old search url pattern whcih was: ?search=term to the updated: /search/{term}

export const useSearchRedirect = () => {
  const nav = useNavigate();
  const [searchParams] = useSearchParams({ search: '' });
  const inQuery = searchParams.has('search');
  const term = inQuery ? searchParams.get('search') || '' : '';
  const termIsValid = isValidSearchTerm(term);

  useEffect(() => {
    if (termIsValid) {
      nav(`/search/${encodeURIComponent(term)}`, { replace: true });
    }
  }, [nav, term, termIsValid]);
};
