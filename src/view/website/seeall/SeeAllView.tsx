import { useEffect, useState } from 'react';
import { useSeeAll } from '../../../api/gql.getSeeAll';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { useAuth } from '../../../components/login/useAuth';
import { InfiniteSeeAllView } from '../../../components/sections/InfiniteSeeAllView';
import { SectionHeader } from '../../../components/sections/SectionHeader';
import { SeeAllSectionType } from '../../../generated/graphql';
import { useRouteParams } from '../../../utils/useRouteParams';
import { useScrollRestore } from '../../../utils/useScrollRestore';
import { SwellcastError } from '../SwellcastError';

export const SeeAllView = () => {
  const params = useRouteParams();
  const auth = useAuth();
  const query = useSeeAll({
    sectionType: params?.sectionType ?? SeeAllSectionType.SearchCommunity, //
    limit: 24,
    params: decodeURIComponent(params?.sectionParams ?? ''),
    sinceId: '',
  });
  const [isReady, setIsReady] = useState(true);

  useEffect(() => {
    setIsReady(auth.isReady);
  }, [auth.isReady]);

  useScrollRestore(query.isSuccess);

  if (query.isError) {
    return <SwellcastError description={query?.error?.message} />;
  }

  if (isReady && query.isSuccess) {
    return (
      <div className='max-width-sections mx-auto'>
        <SectionHeader title={query.data.pages[0].title ?? ''} />
        <InfiniteSeeAllView query={query} />
      </div>
    );
  }

  return <BounceLoaderPage />;
};
