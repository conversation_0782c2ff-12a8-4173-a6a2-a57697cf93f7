import { useCommunitiesPage } from '../../../api/gql.getCommunitiesPage';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { InfiniteSectionsView } from '../../../components/sections/InfiniteSectionsView';
import { useRouteParams } from '../../../utils/useRouteParams';
import { useScrollRestore } from '../../../utils/useScrollRestore';
import { SwellcastError } from '../SwellcastError';

export const CommunitiesView = () => {
  const params = useRouteParams();
  const query = useCommunitiesPage(params);
  useScrollRestore(query.isSuccess);

  if (query.isError) {
    return <SwellcastError description={query?.error?.message} />;
  }

  if (query.isSuccess) {
    return <InfiniteSectionsView query={query} />;
  }

  return <BounceLoaderPage />;
};
