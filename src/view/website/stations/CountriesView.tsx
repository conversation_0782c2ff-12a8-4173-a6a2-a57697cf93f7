import { Link } from 'react-router-dom';
import { SectionHeader } from '../../../components/sections/SectionHeader';
import { COUNTRY_CODES } from '../../../framework/settings/COUNTRY_CODES';
import { useScrollRestore } from '../../../utils/useScrollRestore';

export const CountriesView = () => {
  useScrollRestore();

  const items = Object.entries(COUNTRY_CODES)
    .map(([key, value]) => ({ key, value }))
    .sort((a, b) => a.value.toLowerCase().localeCompare(b.value.toLowerCase()));

  return (
    <div className='container-md'>
      <SectionHeader title='Countries' />
      <div className='grid-pad gap-3' style={{ columns: '175px auto' }}>
        {items.map((k, i) => (
          <Link className='py-2 fix d-block text-hover-underline' key={`cc_${i}`} to={`/country/${k.key.toLowerCase()}`}>
            {k.value}
          </Link>
        ))}
      </div>
    </div>
  );
};
