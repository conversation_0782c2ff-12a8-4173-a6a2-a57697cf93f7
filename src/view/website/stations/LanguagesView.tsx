import { Link } from 'react-router-dom';
import { SectionHeader } from '../../../components/sections/SectionHeader';
import { LANGUAGE_CODES } from '../../../framework/settings/LANGUAGE_CODES';
import { useScrollRestore } from '../../../utils/useScrollRestore';

export const LanguagesView = () => {
  useScrollRestore();

  const items = Object.entries(LANGUAGE_CODES)
    .map(([key, value]) => ({ key, value }))
    .sort((a, b) => a.value.toLowerCase().localeCompare(b.value.toLowerCase()));

  return (
    <div className='container-md'>
      <SectionHeader title='Languages' />
      <div className='grid-pad gap-3' style={{ columns: '175px auto' }}>
        {items.map((k, i) => (
          <Link className='py-2 fix d-block text-hover-underline' key={`cc_${i}`} to={`/language/${k.key}`}>
            {k.value}
          </Link>
        ))}
      </div>
    </div>
  );
};
