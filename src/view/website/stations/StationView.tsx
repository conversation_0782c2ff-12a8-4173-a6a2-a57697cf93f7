import { useMemo } from 'react';
import { useStationPage } from '../../../api/gql.getStationPage';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { CommunityHeader } from '../../../components/common/CommunityHeader';
import { InfiniteSectionsView } from '../../../components/sections/InfiniteSectionsView';
import { HelmetBuilder } from '../../../framework/HelmetBuilder';
import { COUNTRY_CODES } from '../../../framework/settings/COUNTRY_CODES';
import { LANGUAGE_CODES } from '../../../framework/settings/LANGUAGE_CODES';
import { StationType } from '../../../generated/graphql';
import { getOGFromCommunity } from '../../../utils/swell-utils';
import { useRouteParams } from '../../../utils/useRouteParams';
import { useScrollRestore } from '../../../utils/useScrollRestore';
import { SwellcastError } from '../SwellcastError';

export const StationView = ({ type = StationType.Category }: { type?: StationType }) => {
  const params = useRouteParams();
  const query = useStationPage({ id: params.listId, type });
  const og = query.isSuccess ? getOGFromCommunity(query.data.pages[0]) : {};

  useScrollRestore(query.isSuccess);

  //   const errorMessage = useMemo(() => {
  //     let message = Localize.GENERAL_ERROR;
  //     if (type === StationType.Country && COUNTRY_CODES?.[params.listId.toUpperCase()]) {
  //       const country = COUNTRY_CODES[params.listId.toUpperCase()];
  //       message = `Be the first to Swell in ${country}`;
  //     }
  //     if (type === StationType.Language && LANGUAGE_CODES?.[params.listId.toLowerCase()]) {
  //       const language = LANGUAGE_CODES[params.listId.toLowerCase()];
  //       message = `Be the first to Swell in ${language}`;
  //     }
  //     return message;
  //   }, [params]);

  const error = useMemo(() => {
    // let e = <SwellcastError />;
    let message = '';
    let url = '';
    let label = 'Find related Swells';

    if (params.listId) {
      const key = params.listId.toUpperCase();
      // const countryKey = key as keyof typeof COUNTRY_CODES;
      // const langKey = key as keyof typeof LANGUAGE_CODES;

      if (type === StationType.Country && key in COUNTRY_CODES) {
        const validKey = key as keyof typeof COUNTRY_CODES;
        const country = COUNTRY_CODES[validKey];
        message = `Be the first to Swell in ${country}`;
        url = `/search/${encodeURIComponent(country)}`;
        label = `Find Swells about ${country}`;
      }
      if (type === StationType.Language && key in COUNTRY_CODES) {
        const validKey = key as keyof typeof LANGUAGE_CODES;
        const language = LANGUAGE_CODES[validKey];
        message = `Be the first to Swell in ${language}`;
        url = `/search/${encodeURIComponent(language)}`;
        label = `Find Swells in ${language}`;
      }
    }
    return <SwellcastError title={message} description='' url={url} label={label} />;
  }, [params]);

  if (query.isError) {
    return <SwellcastError description={query?.error?.description} />;
  }

  if (query.isSuccess) {
    return (
      <>
        <HelmetBuilder {...og} />
        <CommunityHeader station={query.data.pages[0]} />
        <InfiniteSectionsView query={query} error={error} />
      </>
    );
  }

  return <BounceLoaderPage />;
};
