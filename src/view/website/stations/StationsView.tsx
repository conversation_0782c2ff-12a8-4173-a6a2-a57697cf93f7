import { useCategory } from '../../../api/gql.listCategory';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { useLoginPopup } from '../../../components/common/loginpopup/LoginPopup';
import { ContentSection } from '../../../components/sections/ContentSection';
import { SectionStation } from '../../../components/sections/blocks/SectionStation';
import { HelmetBuilder } from '../../../framework/HelmetBuilder';
import { useScrollRestore } from '../../../utils/useScrollRestore';
import { SwellcastError } from '../SwellcastError';
import { stationsToSection } from './stationsToSection';

export const StationsView = () => {
  const stations = useCategory();
  useLoginPopup();
  useScrollRestore(stations.isSuccess);

  if (stations.isSuccess) {
    return (
      <div className='mx-auto' style={{ maxWidth: 1060 }}>
        <HelmetBuilder />
        <ContentSection section={stationsToSection('All Categories', stations.data)} Renderer={SectionStation} />
      </div>
    );
  }

  if (stations.isError) {
    return <SwellcastError description={stations?.failureReason?.description} />;
  }

  return <BounceLoaderPage />;
};
