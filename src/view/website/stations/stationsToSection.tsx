import { NormalSection } from '../../../components/sections/InfiniteSectionsView';
import { EMPTY_SECTION } from '../../../components/sections/sections-models';
import { HomePageRenderType, HomePageSectionDataType, OpenApiCategoryModel } from '../../../generated/graphql';
import { slugify } from '../../../utils/Utils';

export function stationsToSection(label: string, stations: OpenApiCategoryModel[]) {
  return {
    ...EMPTY_SECTION,
    type: HomePageRenderType.Vertical,
    label,
    id: slugify(label),
    stations,
    sectionDataType: HomePageSectionDataType.Stations,
  } as unknown as NormalSection;
}
