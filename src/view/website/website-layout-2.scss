@use '../../assets/css/base' as *;

.website-layout-2 {
  .fade-mask-right,
  .fade-mask-left,
  .fade-mask-both {
    -webkit-mask-image: none !important;
    mask-image: none !important;
  }
}

$max-content-width: 1190px;
$nav-min-width: 310px;

.site-center {
  grid-template-columns: minmax(min-content, 1fr) minmax(auto, var(--max-content-width)) 1fr;
}
@media (max-width: #{$max-content-width}) {
  .site-center {
    grid-template-columns: 0 auto 0;
  }
}

.site-content-left {
  @extend .shadow;
  z-index: 89;
  position: sticky;
  top: 0;
  height: 100vh;
  overflow: auto;
  transition: all 0.3s;
  max-width: 0px;
}

[data-navopen='1'] {
  .site-content-left {
    max-width: 500px;
  }
}

.site-content-center {
  @extend .flex-grow-1, .overflow-auto, .border-start, .border-end, .position-relative;
}
