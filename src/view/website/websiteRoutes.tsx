import { RouteObject } from 'react-router-dom';
import { RefreshPage } from '../../components/common/RefreshPage';
import { RecordPage } from '../../components/player/AudioRecorder/RecordPage';
import { StationType } from '../../generated/graphql';
import { AppRequiredPageContent } from '../error/AppRequiredPage';
import { Page404Content } from '../error/Page404';
import { GoBio } from '../go/bio/gobio';
import { PremiumHubPage } from '../go/premium/PremiumHubPage';
import { PromptFormPage } from '../go/prompt/PromptForm';
import { SpotifyFormPage } from '../go/spotify/SpotifyForm';
import { StartCommunityFormPage } from '../go/start-community/StartCommunityForm';
import { VideosPageContent } from '../go/videos/ScrollingVideos';
import { OpenRecorderPage } from './OpenRecorderPage';
import { PromptView } from './PromptView';
import { RedirectReload } from './RedirectReload';
import { SwellView } from './SwellView';
import { SwellcastView } from './SwellcastView';
import { WebsiteLayout } from './WebsiteLayout';
import HomeView from './home/<USER>';
import { SearchView } from './search/SearchView';
import { SeeAllView } from './seeall/SeeAllView';
import { CommunitiesView } from './stations/CommunitiesView';
import { CountriesView } from './stations/CountriesView';
import { LanguagesView } from './stations/LanguagesView';
import { StationView } from './stations/StationView';
import { StationsView } from './stations/StationsView';
import { WritePage } from './write/WritePage';
// TODO: incorporate jason-video-player-sound-system "context"
export const websiteRouteMap: RouteObject[] = [
  {
    path: '/',
    element: <WebsiteLayout />,
    children: [
      { index: true, element: <HomeView /> },
      { path: 't/:id', element: <RefreshPage /> },
      { path: 'p/:id', element: <RefreshPage /> },
      { path: 'p/:id/T', element: <RefreshPage /> },
      { path: 'categories', element: <StationsView /> },
      { path: 'stations', element: <StationsView /> }, // alias: SA-7591
      { path: 'rc', element: <OpenRecorderPage /> },
      { path: 'go/finder/*', element: <RedirectReload /> },
      { path: 'go/videos', element: <VideosPageContent /> },
      { path: 'go/premiumhub', element: <PremiumHubPage /> },
      { path: 'go/promptsuggestion', element: <PromptFormPage /> },
      { path: 'go/podcastPublish', element: <SpotifyFormPage /> },
      { path: 'go/start-a-community', element: <StartCommunityFormPage /> },
      { path: 'go/bio/:countryCode?', element: <GoBio /> },
      { path: 'go/pages/*', element: <RefreshPage /> }, // we have served routes that need a refresh
      { path: 'do/record', element: <RecordPage /> },
      { path: 'do/write', element: <WritePage /> },
      { path: 'do/:action', element: <AppRequiredPageContent /> },
      { path: 'search/:search?', element: <SearchView /> },
      { path: 'communities', element: <CommunitiesView /> },
      { path: 'countries', element: <CountriesView /> },
      { path: 'languages', element: <LanguagesView /> },
      { path: 'see-all/:sectionType/:sectionParams?', element: <SeeAllView /> },
      { path: 'category/:listId/:slug?', element: <StationView type={StationType.Category} /> },
      { path: 'country/:listId/:slug?', element: <StationView type={StationType.Country} /> },
      { path: 'language/:listId/:slug?', element: <StationView type={StationType.Language} /> },
      { path: ':listId/hashtag/:hash?', element: <SwellcastView /> },
      { path: ':listId/hashtag/:hash/:canonicalId/:slug?/:replyId?', element: <SwellView /> },
      { path: ':listId/prompt/:promptId/:slug?', element: <PromptView /> },
      { path: ':listId/:canonicalId/:slug?/:replyId?', element: <SwellView /> },
      { path: ':listId', element: <SwellcastView /> },
      { path: '*', element: <Page404Content /> },
    ],
  },
];
