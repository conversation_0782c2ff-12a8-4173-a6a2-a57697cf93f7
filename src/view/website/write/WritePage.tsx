import { useEffect, useMemo, useState } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import { useLocalStorage } from 'usehooks-ts';
import { useProfile } from '../../../api/gql.getProfilePage';
import { BounceLoaderPage } from '../../../components/common/bounceloader/BounceLoaderPage';
import { useDebug } from '../../../components/debug/useDebug';
import { useHeaderHeight } from '../../../components/header/useHeaderHeight';
import { DashSpacer } from '../../../components/player/AudioPlayerUI/DraggableDash';
import { canRecord15 } from '../../../components/player/AudioRecorder/canRecord15';
import { IOgParams } from '../../../components/player/AudioRecorder/RecordPage';
import { SwellButton } from '../../../components/swellbutton/SwellButton';
import { HelmetBuilder } from '../../../framework/HelmetBuilder';
import { BreakPoints } from '../../../framework/settings/BreakPoints';
import { DEFAULT_OG } from '../../../framework/settings/DEFAULT_OG';
import { useAudioMode } from '../../../framework/useAudioMode';
import { useOrchestration } from '../../../framework/useOrchestration';
import { AudioMode, OGModel } from '../../../models/models';
import { dataLayer } from '../../../tracking/Tracking';
import { useSearchParam } from '../../../utils/useSearchParams';

// SA-7899
const MAX_TEXT_LENGTH = 1500;
const MAX_TEXT_LENGTH_PRO = 5000;

export const WritePage = () => {
  const { debug } = useDebug('upload');
  const orch = useOrchestration();
  const paramsString = useSearchParam('params', '{"title":"Start a conversation on Swell..."}');
  const [params, setParams] = useState<{ tag: string; alias: string; title: string; isReply: boolean; swellId?: string; swellcastAlias?: string; promptId?: string }>({ tag: '', alias: '', title: '', isReply: false, swellcastAlias: '' });
  const [searchParams] = useSearchParams();
  const loc = useLocation();
  const profile = useProfile({ alias: params.alias });
  const mode = useAudioMode();
  const [maxLength, setMaxLength] = useState(50);
  const [length, setLength] = useState(0);
  const [savedText, setSavedText] = useLocalStorage('textswell', '');
  const [savedTextId, setSavedTextId] = useLocalStorage('textswellid', `${params.swellId ?? ''}_${params.promptId ?? ''}`);
  const [text, setText] = useState(savedText);
  const tag = params.tag;

  // prefill input with "tag" if it exists. Ignore if user has saved input
  useEffect(() => {
    // console.log({ tag, savedText, text });
    if (tag && savedText == '' && text == '') {
      setText(params.tag);
    }
  }, [tag]);

  // make sure previous text for this swell/prompt is cleared
  useEffect(() => {
    if (params.swellId || params.promptId) {
      const id = `${params.swellId ?? ''}_${params.promptId ?? ''}`;
      if (savedTextId !== id) {
        setSavedText('');
        setSavedTextId(id);
        setText(params.tag ?? '');
      }
    }
  }, [params]);

  const handleChange: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    setText(e.currentTarget.value);
    setLength(e.currentTarget.value.length);
  };

  const handleBlur: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    // clean up white space
    setText(
      e.currentTarget.value
        .trim()
        .replace(/ +/g, ' ')
        .replace(/\n{2,}/g, '\n\n'),
    );
  };

  const onSubmit = () => {
    // hack to fix type. If audioMode is set mornally, the mic tries to connect
    const audioMode = params?.isReply === true ? AudioMode.RECORD_REPLY : AudioMode.RECORD_PROMPT_RESPONSE;
    const type = audioMode === AudioMode.RECORD_PROMPT_RESPONSE ? AudioMode.RECORD_NEW : audioMode;

    // https://anecure.atlassian.net/browse/SA-7839
    dataLayer({
      ...mode.trackingData,
      swellcastAlias: params?.swellcastAlias ?? params?.alias ?? '',
      type: 'text',
      event: 'swellrecorder',
      context: 'post_swell',
    });

    if (debug) {
      if (confirm('Abort submit?')) {
        return;
      }
    }

    orch.completeTextResponse(text, type);
    setSavedText('');
  };

  const og: OGModel = useMemo(() => {
    let ogParams: Partial<IOgParams> = {};
    try {
      ogParams = JSON.parse(searchParams.has('params') ? searchParams.get('params') ?? '' : '{}');
    } catch (err) {
      console.log(err);
    }
    const hasOgImage = !!ogParams?.ogImage;
    const ogTitle = hasOgImage ? ogParams?.ogTitle ?? '' : DEFAULT_OG.title;
    const ogDescription = hasOgImage ? ogParams?.ogDescription ?? '' : DEFAULT_OG.description;

    return {
      title: `${ogTitle} | ${ogDescription}`, // SA-7597
      description: 'swellcast.com',
      image: hasOgImage ? ogParams.ogImage : DEFAULT_OG.image,
      canonicalPath: `${loc.pathname}?${searchParams.toString()}`,
    };
  }, [searchParams, loc]);

  useEffect(() => {
    try {
      const data = JSON.parse(paramsString);
      setParams(data);
    } catch (err) {
      console.log(err);
    }
  }, [paramsString]);

  useEffect(() => {
    mode.setTrackingData({ promptId: params?.promptId ?? null });
  }, [params]);

  useEffect(() => {
    // after profile loads, update maxLength
    if (profile.isSuccess) {
      if (params.isReply) {
        setMaxLength(MAX_TEXT_LENGTH);
      } else {
        mode.setTrackingData({ swellcastId: profile.data.id });
        setMaxLength(canRecord15(profile.data) ? MAX_TEXT_LENGTH_PRO : MAX_TEXT_LENGTH);
      }
    }
  }, [profile.isSuccess]);

  useEffect(() => {
    setSavedText(text);
  }, [text]);

  const headerHeight = useHeaderHeight();
  const style: React.CSSProperties = { minHeight: `calc(100vh - ${headerHeight}px)` };

  if (profile.isLoading) {
    return <BounceLoaderPage />;
  }

  return (
    <>
      <HelmetBuilder {...og} />
      <div className='d-flex w-100 justify-content-center align-items-center position-relative overflow-hidden backdrop-blur-recorder' style={style}>
        {new Array(32).fill(0).map((_e, i) => (
          <div key={`circle${i}`} className='circle-container'>
            <div className='circle'></div>
          </div>
        ))}
        <div className='mx-auto w-100' style={{ maxWidth: BreakPoints.L }}>
          <div className='text-white d-flex flex-column gap-3 p-3 position-relative'>
            <h1>{params?.title}</h1>
            {orch.errMsg ? (
              <div className='alert alert-danger' role='alert'>
                {orch.errMsg}
              </div>
            ) : null}
            <div className='d-flex flex-column gap-2'>
              <label className='fs-2'>Write your response</label>
              <textarea
                data-bs-theme='dark' //
                className='w-100 p-2 fs-2'
                maxLength={maxLength}
                value={text}
                onChange={handleChange}
                onBlur={handleBlur}
                style={{ maxHeight: '70vh', height: 200 }}
              />
              <div className='text-end'>
                {length}/{maxLength}
              </div>
            </div>
            <SwellButton.Primary onClick={onSubmit}>Submit</SwellButton.Primary>
          </div>
          <DashSpacer style={{ transition: 'height 0.3s' }} />
        </div>
      </div>
    </>
  );
};
