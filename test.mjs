import fs from 'fs';
// import https from 'https';
import path from 'path';
import * as url from 'url';

const __dirname = url.fileURLToPath(new URL('.', import.meta.url));

const fileUrls = [path.join(import.meta.url, '../downloads/STdhsE7oJKX0_0.m4a'), path.resolve('../downloads/STdhsE7oJKX0_1.m4a')];

const f = new URL('downloads/STdhsE7oJKX0_0.m4a', import.meta.url)
console.log(f.toString(), fs.existsSync(f));

console.log(fs.existsSync(fileUrls[0]));
console.log({__dirname, fileUrls, url:import.meta.url});