/* eslint-disable @typescript-eslint/no-unused-vars */
let mediaStream;
let audioContext;
let mediaRecorder;
let sourceNode;
let analyserNode;
let permissions;
let dataArray;
let audio = new Audio();
audio.autoplay = false;

function handleTrackMuted() {
  console.log('handleTrackMuted()');
}

function handleTrackUnmuted() {
  console.log('handleTrackUnmuted()');
}

function handleDeviceChange() {
  console.log('handleDeviceChange()');
}

function handleAudioContextStateChange() {
  console.log('handleAudioContextStateChange()');
}

function handleChangePermissions() {
  console.log('handleChangePermissions()');
}

function handleMediaRecorderEvent(e) {
  console.log('handleMediaRecorderEvent()', e.type);
}

function handleChangeSourceNode() {
  console.log('handleChangeSourceNode()');
}

//
//
//

function buildAudioContext() {
  if (!audioContext || audioContext.state === 'closed') {
    console.log('buildAudioContext() NEW');
    audioContext = new AudioContext();
    audioContext.addEventListener('statechange', handleAudioContextStateChange);
  } else {
    console.log('buildAudioContext() RECYCLE');
  }
}

async function buildPermissions() {
  if (!permissions) {
    console.log('buildPermissions() NEW');
    permissions = await navigator.permissions.query({ name: 'microphone' });
    permissions.addEventListener('change', handleChangePermissions);
  } else {
    console.log('buildPermissions() RECYCLE');
  }
}

async function buildMediaStream() {
  if (!mediaStream || mediaStream.active === false) {
    console.log('buildMediaStream() NEW');
    try {
      mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStream.getTracks().forEach((track) => {
        track.addEventListener('mute', handleTrackMuted);
        track.addEventListener('unmute', handleTrackUnmuted);
      });
      return true;
    } catch (err) {
      console.log('buildMediaStream() FAILED');
      return false;
    }
  } else {
    console.log('buildMediaStream() RECYCLE');
    return true;
  }
}

function buildMediaRecorder() {
  if (!mediaRecorder || mediaRecorder.stream.id !== mediaStream.id) {
    console.log('buildMediaRecorder() NEW');
    mediaRecorder = new MediaRecorder(mediaStream, {
      audio: true,
      video: false,
    });
    mediaRecorder.addEventListener('dataavailable', handleMediaRecorderEvent);
    mediaRecorder.addEventListener('error', handleMediaRecorderEvent);
    mediaRecorder.addEventListener('pause', handleMediaRecorderEvent);
    mediaRecorder.addEventListener('resume', handleMediaRecorderEvent);
    mediaRecorder.addEventListener('start', handleMediaRecorderEvent);
    mediaRecorder.addEventListener('stop', handleMediaRecorderEvent);
  } else {
    console.log('buildMediaRecorder() RECYCLE');
  }
}

function buildAnalyserNode() {
  if (!analyserNode) {
    console.log('buildAnalyserNode() NEW');
    analyserNode = audioContext.createAnalyser();
    const bufferLength = analyserNode.frequencyBinCount;
    dataArray = new Uint8Array(bufferLength);
  } else {
    console.log('buildAnalyserNode() RECYCLE');
  }
}

function buildSourceNode() {
  if (!sourceNode || sourceNode.stream.id !== mediaStream.id) {
    console.log('buildSourceNode() NEW');
    sourceNode = audioContext.createMediaStreamSource(mediaStream);
    sourceNode.connect(analyserNode);
    audio.sourceNode = sourceNode;
  } else {
    console.log('buildSourceNode() RECYCLE');
  }
}

function initDeviceChange() {
  navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
  navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
}

async function requestRecorder() {
  buildAudioContext();
  await buildPermissions();
  const streamSuccess = await buildMediaStream();
  if (streamSuccess) {
    buildMediaRecorder();
    buildAnalyserNode();
    buildSourceNode();
    initDeviceChange();
  }
}

const stopRecording = () => {
  mediaStream.getTracks().forEach((track) => track.stop());
};

const restartRecording = () => {
  const tracks = mediaStream.getTracks();
  const mutedTracks = tracks.filter((track) => track.muted);

  if (tracks.length === 0 || mutedTracks.length === 0) {
    //
  }
};

document.getElementById('request-btn').addEventListener('click', () => {
  requestRecorder();
});

document.getElementById('stop-btn').addEventListener('click', () => {
  stopRecording();
});

document.getElementById('start-btn').addEventListener('click', () => {
  mediaRecorder.start();
});

document.getElementById('pause-btn').addEventListener('click', () => {
  mediaRecorder.pause();
});

document.getElementById('resume-btn').addEventListener('click', () => {
  mediaRecorder.resume();
});

document.getElementById('stop-btn').addEventListener('click', () => {
  mediaRecorder.stop();
});
