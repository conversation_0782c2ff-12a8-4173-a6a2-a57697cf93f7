#Test OG
- Run this ina terminal from this folder
- Use multiple terminals to test

INPUT=test_urls.csv
while read httpRequest_requestUrl
do
  random_url="${httpRequest_requestUrl}"
  echo “\nRequesting ${random_url}”
  curl -s ${random_url} | grep -ioE '^<meta.*property="og:title".*content="([^"]+)".*\/>$'
done < $INPUT


INPUT=stage_urls.csv
while read httpRequest_requestUrl
do
  random_url="${httpRequest_requestUrl}"
  echo “\nRequesting ${random_url}”
  curl -s ${random_url} | grep -ioE '^<meta.*property="og:title".*content="([^"]+)".*\/>$'
done < $INPUT