const enzyme = require('enzyme');
const Adapter = require('enzyme-adapter-react-16');

enzyme.configure({ adapter: new Adapter() });

Object.defineProperty(window, 'dataLayer', {
  writable: true,
  value: [],
});

// Object.defineProperty(
//   window.location,
//   'href',
//   ((value) => ({
//     get() {
//       return value;
//     },
//     set(v) {
//       value = v;
//     },
//   }))(window.location['href']),
// );

Object.defineProperty(
  window.navigator,
  'userAgent',
  ((value) => ({
    get() {
      return value;
    },
    set(v) {
      value = v;
    },
  }))(window.navigator['userAgent']),
);

Object.defineProperty(
  window.navigator,
  'vendor',
  ((value) => ({
    get() {
      return value;
    },
    set(v) {
      value = v;
    },
  }))(window.navigator['vendor']),
);
