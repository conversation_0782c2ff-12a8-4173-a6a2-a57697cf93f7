import { shallow } from 'enzyme';
import React from 'react';
import { AppStoreButton } from '../../../src/components/common/AppStoreButton';
// import { AssetImageOld } from '../../../src/components/common/AssetImage';
// import { Faq } from '../../../src/components/common/Faq';
// import { HomeCard } from '../../../src/components/common/HomeCard';
// import { HubSpot } from '../../../src/components/common/HubSpot';
// import { PageHeading } from '../../../src/components/common/PageHeading';
// import { PDFButton } from '../../../src/components/common/PDFButton';
// import { PodcastButton } from '../../../src/components/common/PodcastButton';
// import { SwellLogo } from '../../../src/components/common/SwellLogo';
import { AssetImage } from '../../../src/util/AssetImage';
// import { SwellWidget } from '../../../src/components/common/SwellWidget';

const globalAny: any = global;

describe('Common Components', () => {
  //   it('SwellLogo: renders without crashing', () => {
  //     expect(shallow(<SwellLogo />).find('div').exists);
  //   });

  it('AssetImage: renders without crashing', () => {
    expect(shallow(<AssetImage src='' key='1' />).isEmpty);
  });

  //   it('HomeCard: renders without crashing', () => {
  //     expect(shallow(<HomeCard key='' title='' image='http://' description='' />).find('div').exists);
  //   });

  //   it('HomeCard: renders without crashing', () => {
  //     expect(shallow(<HomeCard key='' title='' description='' />).find('div').exists);
  //   });

  //   it('HomeCard: renders without crashing', () => {
  //     expect(shallow(<HomeCard key='' title='' description='' buttons={[{ key: '', text: '', href: '', className: '' }]} />).find('div').exists);
  //   });

  //   it('HomeCard: renders without crashing', () => {
  //     expect(
  //       shallow(<HomeCard key='' title='' description='' buttons={[{ key: '', text: '', href: 'AppStoreButton', className: '' }]} />).find('div')
  //         .exists,
  //     );
  //   });

  //   it('Faq: renders without crashing', () => {
  //     expect(shallow(<Faq key='' title='' description='' />).find('div').exists);
  //   });

  //   it('HubSpot: renders without crashing', () => {
  //     expect(shallow(<HubSpot formId='' trackEvent='' className='' />).find('div').exists);
  //   });

  //   it('HubSpot: renders without crashing', () => {
  //     expect(shallow(<HubSpot formId='' trackEvent='' />).find('div').exists);
  //   });

  //   it('PDFButton: renders without crashing', () => {
  //     expect(shallow(<PDFButton url='' tracker='' />).find('div').exists);
  //   });

  //   it('PDFButton: renders without crashing', () => {
  //     expect(shallow(<PDFButton url='' tracker='' label='' />).find('div').exists);
  //   });

  it('AppStoreButton: renders without crashing', () => {
    // let appButton = shallow(<AppStoreButton useStoreBranding={true} />);

    globalAny.navigator.userAgent = 'iPad';
    expect(shallow(<AppStoreButton useStoreBranding={true} />).find('div').exists);
    expect(shallow(<AppStoreButton useStoreBranding={false} />).find('div').exists);

    globalAny.navigator.userAgent = 'android';
    expect(shallow(<AppStoreButton useStoreBranding={true} />).find('div').exists);
    expect(shallow(<AppStoreButton useStoreBranding={false} />).find('div').exists);

    globalAny.navigator.userAgent = 'unknown';
    expect(shallow(<AppStoreButton useStoreBranding={true} />).find('div').exists);
    expect(shallow(<AppStoreButton useStoreBranding={false} />).find('div').exists);

    expect(shallow(<AppStoreButton align='start' />).find('div').exists);
    expect(shallow(<AppStoreButton align='end' />).find('div').exists);
  });

  //   it('PodcastButton: renders without crashing', () => {
  //     globalAny.navigator.userAgent = 'iPad';
  //     expect(shallow(<PodcastButton googleLink='' appleLink='' />).find('div').exists);
  //     expect(shallow(<PodcastButton googleLink='xxx' appleLink='' />).find('div').exists);
  //     expect(shallow(<PodcastButton googleLink='' appleLink='xxx' />).find('div').exists);

  //     globalAny.navigator.userAgent = 'android';
  //     expect(shallow(<PodcastButton googleLink='' appleLink='' />).find('div').exists);
  //     expect(shallow(<PodcastButton googleLink='xxx' appleLink='' />).find('div').exists);
  //     expect(shallow(<PodcastButton googleLink='' appleLink='xxx' />).find('div').exists);

  //     globalAny.navigator.userAgent = 'unknown';
  //     expect(shallow(<PodcastButton googleLink='' appleLink='' />).find('div').exists);
  //     expect(shallow(<PodcastButton googleLink='xxx' appleLink='' />).find('div').exists);
  //     expect(shallow(<PodcastButton googleLink='' appleLink='xxx' />).find('div').exists);
  //   });

  //   it('PageHeadgin', () => {
  //     expect(shallow(<PageHeading title='title' />).find('h1').exists);
  //   });
});

// describe('SwellWidget Widget', () => {
//   it('test 1', () => {
//     expect(shallow(<SwellWidget alias='test' header={true} canonicalId='id' />).find('div').exists);
//   });

//   it('test 2', () => {
//     expect(shallow(<SwellWidget alias='test' header={true} />).find('div').exists);
//   });

//   it('test 3', () => {
//     expect(shallow(<SwellWidget alias='test' />).find('div').exists);
//   });

//   it('test 4', () => {
//     expect(shallow(<SwellWidget alias='test' header={false} canonicalId='id' />).find('div').exists);
//   });

//   it('test 5', () => {
//     expect(shallow(<SwellWidget alias='test' header={false} />).find('div').exists);
//   });

//   it('test 5', () => {
//     expect(shallow(<SwellWidget alias='test' header={false} className='red' style={{ color: 'red' }} />).find('div').exists);
//   });

//   it('test 5', () => {
//     expect(shallow(<SwellWidget alias='test' />).find('div').exists);
//   });

//   it('test 5', () => {
//     expect(shallow(<SwellWidget alias='test' header={false} className={null} style={null} />).find('div').exists);
//   });
// });
