import { mount, shallow } from 'enzyme';
import React from 'react';
import { Header } from '../../../src/components/header';

const globalAny: any = global;
// describe('Header', () => {
//   it('Component: renders without crashing', () => {
//     expect(shallow(<Header />).find('.cls-swell-header').length).toBe(1);
//   });
// });

describe('Header unmount', () => {
  let wrapper = mount(<Header />);
  it('Component: renders without crashing', () => {
    expect(wrapper.find('.navbar').length).toBe(1);
    wrapper.unmount();
    expect(wrapper).toBeDefined();
  });
});

describe('Header on iOS', () => {
  globalAny.navigator.userAgent = 'iOS';
  let widget = shallow(<Header />);
  it('Header iOS', () => {
    expect(widget).toBeDefined();
  });
});

describe('Header on android', () => {
  globalAny.navigator.userAgent = 'android';
  let widget = shallow(<Header />);
  it('Header android', () => {
    expect(widget).toBeDefined();
  });
});
