// import { HelmetTag } from '../../../src/components/helmet';
// import { AppHead, AppKeywords } from '../../../src/util';

var layoutData = {
  head: {
    title: 'about',
    meta: [
      { property: 'og:title', content: 'about' },
      { property: 'keywords', content: '' },
    ],
    link: [
      {
        rel: 'alternate',
        href: 'test',
        type: 'xss/rss',
      },
    ],
  },
};

describe('Meta', () => {
  //   it('For Localhost', () => {
  //     const head: AppHead = layoutData.head;
  //     const path = '/test';
  //     const keywords: AppKeywords = { data: 'swell' };
  //     const domain = 'localhost';
  //     const page = 'test';
  //     expect(shallow(<HelmetTag {...{ ...head, path, domain, keywords, page }} />).find('meta').length).toBeGreaterThan(8);
  //   });
  //   it('For www.swell.life', () => {
  //     const head: AppHead = layoutData.head;
  //     const path = '/test';
  //     const keywords: AppKeywords = { data: 'swell' };
  //     const domain = 'www.swell.life';
  //     const page = 'test';
  //     expect(shallow(<HelmetTag {...{ ...head, path, domain, keywords, page }} />).find('meta').length).toBeGreaterThan(8);
  //   });
  //   it('For swell.life', () => {
  //     const head: AppHead = layoutData.head;
  //     const path = '/test';
  //     const keywords: AppKeywords = { data: 'swell' };
  //     const domain = 'swell.life';
  //     const page = 'test';
  //     expect(shallow(<HelmetTag {...{ ...head, path, domain, keywords, page }} />).find('meta').length).toBeGreaterThan(8);
  //   });
});
