import { shallow } from 'enzyme';
import React from 'react';
// import { ErrorScreen } from '../../../src/components/swellcast/components/ErrorScreen';
// import { SwellcastWidget } from '../../../src/components/swellcast/components/SwellcastWidget/SwellcastWidget';

// describe('SwellcastWidget', () => {
//   it('Component: renders without crashing', () => {
//     expect(shallow(<SwellcastWidget routeRoot='' graph={{}} />).find('div').length).toBeGreaterThan(0);
//   });
// });

// describe('SwellcastWidget', () => {
//   let fn = jest.fn();
//   let el = shallow(<ErrorScreen onRefresh={fn} />);
//   it('Component: renders without crashing', () => {
//     expect(el.find('div').length).toBeGreaterThan(0);
//     // el.find('button').at(0)?.simulate('click');
//     // expect(fn).toHaveBeenCalled();
//   });

//   el = shallow(<ErrorScreen />);
//   it('Component: renders without crashing', () => {
//     expect(el.find('div').length).toBeGreaterThan(0);
//   });
// });
