describe('Logger Test without level', () => {
  const OLD_ENV = process.env;
  beforeEach(() => {
    jest.resetModules(); // this is important - it clears the cache
    process.env = { ...OLD_ENV, LOG_LEVEL: '' };
  });
  afterEach(() => {
    process.env = OLD_ENV;
  });
  it('error function test', () => {
    const { logger } = require('../../../src/util');
    const error = jest.spyOn(logger, 'error');
    logger.error('test log', 'with message');
    expect(error).toHaveBeenCalled();
  });
});

describe('Logger Test with level', () => {
  const OLD_ENV = process.env;
  beforeEach(() => {
    jest.resetModules(); // this is important - it clears the cache
    process.env = { ...OLD_ENV, LOG_LEVEL: 'INFO' };
  });
  afterEach(() => {
    process.env = OLD_ENV;
  });
  it('info function test', () => {
    const { logger } = require('../../../src/util');
    const info = jest.spyOn(logger, 'info');
    logger.info('test log');
    expect(info).toHaveBeenCalled();
  });
  it('error function test', () => {
    const { logger } = require('../../../src/util');
    const error = jest.spyOn(logger, 'error');
    logger.error('test log');
    expect(error).toHaveBeenCalled();
  });
  it('warn function test', () => {
    const { logger } = require('../../../src/util');
    const warn = jest.spyOn(logger, 'warn');
    logger.warn('test log');
    expect(warn).toHaveBeenCalled();
  });
  it('debug function test', () => {
    const { logger } = require('../../../src/util');
    const debug = jest.spyOn(logger, 'debug');
    logger.debug('test log');
    expect(debug).toHaveBeenCalled();
  });
});
