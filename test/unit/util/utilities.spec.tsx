import { getMobileOperatingSystem, getUID, logger } from '../../../src/util';

const globalAny: any = global;

describe('Url Test With local set as true', () => {
  let pause = jest.fn();
  beforeEach(() => {
    let mediaEl = document.createElement('audio');
    mediaEl.id = 'id-swell-audio';
    mediaEl.src = 'test.mp3';
    mediaEl.autoplay = true;
    mediaEl.pause = pause;
    document.body.appendChild(mediaEl);
  });

  //   it('test close audio', () => {
  //     pauseAudio();
  //     expect(pause).toBeCalled();
  //   });

  it('test getUID', () => {
    let uid = getUID();
    expect(uid).toBeDefined();
  });

  it('test getMobileOperatingSystem', () => {
    let uid = getMobileOperatingSystem();
    expect(uid).toBeDefined();
  });

  it('test getMobileOperatingSystem', () => {
    globalAny.navigator.userAgent = 'windows phone';
    let uid = getMobileOperatingSystem();
    expect(uid).toBeDefined();
  });

  it('test getMobileOperatingSystem', () => {
    globalAny.navigator.userAgent = 'android';
    let uid = getMobileOperatingSystem();
    expect(uid).toBeDefined();
  });

  it('test getMobileOperatingSystem', () => {
    globalAny.navigator.userAgent = 'iPad';
    let uid = getMobileOperatingSystem();
    expect(uid).toBeDefined();
  });

  it('test getMobileOperatingSystem', () => {
    globalAny.navigator.userAgent = 'unknown';
    let uid = getMobileOperatingSystem();
    expect(uid).toBeDefined();
  });

  it('simple test', () => {
    globalAny.navigator.userAgent = null;
    globalAny.navigator.vendor = 'windows phone';
    let uid = getMobileOperatingSystem();
    logger.info(`TEST uid ${uid}`);
    expect(uid).toBeDefined();
  });
});
