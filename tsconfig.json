{"compilerOptions": {"baseUrl": "./", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "typeRoots": ["node_modules/@types", "src/@types"]}, "ts-node": {"compilerOptions": {"module": "CommonJS"}}, "include": ["src", "server", "handler.ts", "config"], "watchOptions": {"watchFile": "fixedPollingInterval", "watchDirectory": "fixedPollingInterval", "synchronousWatchDirectory": true}}